#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重组工具
按照临时文件和永久文件分类重新组织项目结构

作者: AI助手
日期: 2025-07-27
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class 文件重组工具:
    """项目文件重组工具"""
    
    def __init__(self):
        self.项目根目录 = Path(".")
        self.移动统计 = {
            '移动文件数': 0,
            '创建目录数': 0,
            '移动详情': []
        }
        
        # 定义文件分类规则
        self.文件分类规则 = {
            # 临时文件（保留在根目录）
            '临时文件': {
                '模式': [
                    '套利信息提取_*.md',
                    '原始文章_*.md', 
                    '项目检查报告.md',
                    '文章_*.md',  # 生成的示例文章
                ],
                '目标目录': '.'  # 保留在根目录
            },
            
            # 核心代码文件
            '核心代码': {
                '文件': [
                    '主程序.py',
                    '提取套利信息.py',
                    '采集并分析文章.py'
                ],
                '目标目录': '源代码/核心程序'
            },
            
            # 工具脚本
            '工具脚本': {
                '文件': [
                    '清理脚本.py',
                    '项目清理工具.py',
                    '文件重组工具.py'
                ],
                '目标目录': '工具脚本'
            },
            
            # 文档资料
            '文档资料': {
                '文件': [
                    'README.md',
                    '使用指南.md',
                    '开发规则文档.md',
                    '快速启动指南.md',
                    '文件管理规则.md',
                    '项目结构说明.md',
                    '环境准备清单.md',
                    'Gemini API密钥获取指南.md',
                    'Gemini2.5Pro使用指南.md',
                    'Gemini调用总结.md'
                ],
                '目标目录': '文档说明/用户文档'
            },
            
            # 配置文件
            '配置文件': {
                '文件': [
                    'requirements.txt',
                    '.env',
                    '.env.example'
                ],
                '目标目录': '配置文件'
            }
        }
    
    def 创建目录结构(self):
        """创建新的目录结构"""
        print("📁 创建目录结构...")
        
        需要创建的目录 = [
            '源代码/核心程序',
            '工具脚本', 
            '文档说明/用户文档',
            '临时文件'  # 为将来使用预留
        ]
        
        for 目录路径 in 需要创建的目录:
            目录 = self.项目根目录 / 目录路径
            if not 目录.exists():
                目录.mkdir(parents=True, exist_ok=True)
                self.移动统计['创建目录数'] += 1
                print(f"   ✅ 创建目录: {目录路径}")
    
    def 移动文件(self, 源文件路径: Path, 目标目录: str, 文件类型: str):
        """移动单个文件"""
        目标路径 = self.项目根目录 / 目标目录
        目标文件路径 = 目标路径 / 源文件路径.name
        
        # 如果目标目录是根目录，则不移动
        if 目标目录 == '.':
            return
        
        try:
            # 确保目标目录存在
            目标路径.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            shutil.move(str(源文件路径), str(目标文件路径))
            
            self.移动统计['移动文件数'] += 1
            self.移动统计['移动详情'].append({
                '文件': 源文件路径.name,
                '类型': 文件类型,
                '从': str(源文件路径.relative_to(self.项目根目录)),
                '到': str(目标文件路径.relative_to(self.项目根目录))
            })
            
            print(f"   ✅ 移动 {文件类型}: {源文件路径.name} → {目标目录}/")
            
        except Exception as e:
            print(f"   ❌ 移动失败: {源文件路径.name} - {e}")
    
    def 执行文件重组(self):
        """执行文件重组"""
        print("🔄 开始文件重组...")
        print("=" * 60)
        
        # 1. 创建目录结构
        self.创建目录结构()
        
        # 2. 按分类移动文件
        for 分类名称, 分类规则 in self.文件分类规则.items():
            if 分类名称 == '临时文件':
                continue  # 临时文件保留在根目录，不移动
                
            print(f"\n📂 处理 {分类名称}...")
            
            if '文件' in 分类规则:
                # 处理指定的文件列表
                for 文件名 in 分类规则['文件']:
                    文件路径 = self.项目根目录 / 文件名
                    if 文件路径.exists() and 文件路径.is_file():
                        self.移动文件(文件路径, 分类规则['目标目录'], 分类名称)
            
            if '模式' in 分类规则:
                # 处理文件模式匹配
                for 模式 in 分类规则['模式']:
                    for 文件路径 in self.项目根目录.glob(模式):
                        if 文件路径.is_file():
                            self.移动文件(文件路径, 分类规则['目标目录'], 分类名称)
        
        # 3. 显示重组结果
        self.显示重组结果()
    
    def 显示重组结果(self):
        """显示重组结果"""
        print("\n" + "=" * 60)
        print("📊 文件重组结果")
        print("=" * 60)
        
        print(f"📁 创建目录数: {self.移动统计['创建目录数']}")
        print(f"📄 移动文件数: {self.移动统计['移动文件数']}")
        
        if self.移动统计['移动详情']:
            print(f"\n📋 移动详情:")
            for 详情 in self.移动统计['移动详情']:
                print(f"   {详情['文件']} ({详情['类型']}) → {详情['到']}")
        
        print(f"\n🎉 文件重组完成！")
    
    def 显示新的项目结构(self):
        """显示重组后的项目结构"""
        print("\n" + "=" * 60)
        print("📁 重组后的项目结构")
        print("=" * 60)
        
        def 显示目录树(路径: Path, 前缀: str = "", 是最后一个: bool = True):
            """递归显示目录树"""
            if 路径.name.startswith('.') or 路径.name == '__pycache__':
                return
                
            连接符 = "└── " if 是最后一个 else "├── "
            print(f"{前缀}{连接符}{路径.name}")
            
            if 路径.is_dir():
                子项目 = [p for p in 路径.iterdir() if not p.name.startswith('.') and p.name != '__pycache__']
                子项目.sort(key=lambda x: (x.is_file(), x.name.lower()))
                
                for i, 子路径 in enumerate(子项目):
                    是子项目最后一个 = i == len(子项目) - 1
                    新前缀 = 前缀 + ("    " if 是最后一个 else "│   ")
                    显示目录树(子路径, 新前缀, 是子项目最后一个)
        
        print("公众号全自动/")
        根目录项目 = [p for p in self.项目根目录.iterdir() 
                    if not p.name.startswith('.') and p.name != '__pycache__']
        根目录项目.sort(key=lambda x: (x.is_file(), x.name.lower()))
        
        for i, 路径 in enumerate(根目录项目):
            是最后一个 = i == len(根目录项目) - 1
            显示目录树(路径, "", 是最后一个)
    
    def 生成文件清单(self):
        """生成重组后的文件清单"""
        清单文件 = self.项目根目录 / "文件重组清单.md"
        
        with open(清单文件, 'w', encoding='utf-8') as f:
            f.write(f"# 文件重组清单\n\n")
            f.write(f"**重组时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 📂 重组后的目录结构\n\n")
            f.write("### 🗂️ 临时文件（根目录）\n")
            f.write("这些文件可以定期手动删除：\n")
            for 文件 in self.项目根目录.glob("*.md"):
                if any(模式.replace('*', '') in 文件.name for 模式 in self.文件分类规则['临时文件']['模式']):
                    f.write(f"- {文件.name}\n")
            
            f.write("\n### 🏗️ 永久文件（分类存放）\n")
            f.write("这些文件不能删除：\n\n")
            
            for 分类名称, 分类规则 in self.文件分类规则.items():
                if 分类名称 == '临时文件':
                    continue
                    
                f.write(f"#### {分类名称}\n")
                f.write(f"位置：`{分类规则['目标目录']}/`\n")
                
                if '文件' in 分类规则:
                    for 文件名 in 分类规则['文件']:
                        f.write(f"- {文件名}\n")
                f.write("\n")
            
            f.write("## 📊 重组统计\n\n")
            f.write(f"- 创建目录数：{self.移动统计['创建目录数']}\n")
            f.write(f"- 移动文件数：{self.移动统计['移动文件数']}\n")
            
        print(f"📄 文件清单已生成：{清单文件.name}")

def 主函数():
    """主函数"""
    print("🔄 项目文件重组工具")
    print("=" * 60)
    print("📋 重组规则：")
    print("   • 临时文件 → 保留在根目录（可定期删除）")
    print("   • 核心代码 → 源代码/核心程序/")
    print("   • 工具脚本 → 工具脚本/")
    print("   • 文档资料 → 文档说明/用户文档/")
    print("   • 配置文件 → 配置文件/")
    print("\n🚀 开始自动重组...")

    # 执行重组
    重组工具 = 文件重组工具()
    重组工具.执行文件重组()
    重组工具.显示新的项目结构()
    重组工具.生成文件清单()

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 重组被用户中断")
    except Exception as e:
        print(f"\n❌ 重组过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
