#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一清理工具
合并了清理脚本.py和项目清理工具.py的功能

作者: AI助手
日期: 2025-07-27
合并来源: 清理脚本.py + 项目清理工具.py
"""

import os
import shutil
import time
from pathlib import Path
from datetime import datetime, timedelta

class 统一清理工具:
    """统一的项目文件清理工具"""
    
    def __init__(self):
        self.项目根目录 = Path(".")
        self.清理统计 = {
            '删除文件数': 0,
            '删除目录数': 0,
            '释放空间': 0,
            '清理详情': []
        }
    
    def 清理测试文件(self):
        """清理测试和示例文件"""
        print("🗂️ 清理测试和示例文件...")
        
        测试文件列表 = [
            "快速测试Gemini.py", "快速测试采集.py", "测试Gemini2.5Pro.py",
            "测试Gemini连接.py", "测试脚本.py", "投资公众号测试.py",
            "套利分析测试.py", "诊断采集器问题.py", "简单HTTP调用示例.py",
            "简单查看文章.py", "查看采集文章.py"
        ]
        
        for 文件名 in 测试文件列表:
            文件路径 = self.项目根目录 / 文件名
            if 文件路径.exists():
                try:
                    文件大小 = 文件路径.stat().st_size
                    文件路径.unlink()
                    self.清理统计['删除文件数'] += 1
                    self.清理统计['释放空间'] += 文件大小
                    print(f"   ✅ 删除测试文件: {文件名}")
                except Exception as e:
                    print(f"   ❌ 删除失败: {文件名} - {e}")
    
    def 清理临时文件(self, 保留天数: int = 7):
        """清理临时文件"""
        print("🗂️ 清理临时文件...")
        
        截止时间 = datetime.now() - timedelta(days=保留天数)
        临时文件模式 = [
            "套利分析结果.json", "生成的原创文章.md",
            "原创套利策略文章_*.md", "项目优化建议_*.json",
            "项目优化建议_*.md", "项目实施方案_*.md",
            "文章深度分析_*.md"
        ]
        
        for 模式 in 临时文件模式:
            if "*" in 模式:
                for 文件路径 in self.项目根目录.glob(模式):
                    if 文件路径.is_file():
                        文件修改时间 = datetime.fromtimestamp(文件路径.stat().st_mtime)
                        if 文件修改时间 < 截止时间:
                            try:
                                文件大小 = 文件路径.stat().st_size
                                文件路径.unlink()
                                self.清理统计['删除文件数'] += 1
                                self.清理统计['释放空间'] += 文件大小
                                print(f"   ✅ 删除临时文件: {文件路径.name}")
                            except Exception as e:
                                print(f"   ❌ 删除失败: {文件路径.name} - {e}")
    
    def 清理缓存文件(self):
        """清理Python缓存文件"""
        print("🗂️ 清理缓存文件...")
        
        # 删除__pycache__目录
        for pycache_dir in self.项目根目录.rglob("__pycache__"):
            if pycache_dir.is_dir():
                try:
                    shutil.rmtree(pycache_dir)
                    self.清理统计['删除目录数'] += 1
                    print(f"   ✅ 删除缓存目录: {pycache_dir.relative_to(self.项目根目录)}")
                except Exception as e:
                    print(f"   ❌ 删除缓存目录失败: {pycache_dir.name} - {e}")
        
        # 删除.pyc文件
        for pyc_file in self.项目根目录.rglob("*.pyc"):
            try:
                文件大小 = pyc_file.stat().st_size
                pyc_file.unlink()
                self.清理统计['删除文件数'] += 1
                self.清理统计['释放空间'] += 文件大小
                print(f"   ✅ 删除缓存文件: {pyc_file.relative_to(self.项目根目录)}")
            except Exception as e:
                print(f"   ❌ 删除缓存文件失败: {pyc_file.name} - {e}")
    
    def 格式化文件大小(self, 字节数):
        """格式化文件大小显示"""
        if 字节数 < 1024:
            return f"{字节数} B"
        elif 字节数 < 1024 * 1024:
            return f"{字节数 / 1024:.1f} KB"
        elif 字节数 < 1024 * 1024 * 1024:
            return f"{字节数 / (1024 * 1024):.1f} MB"
        else:
            return f"{字节数 / (1024 * 1024 * 1024):.1f} GB"
    
    def 执行完整清理(self):
        """执行完整的项目清理"""
        print("🧹 开始项目清理...")
        print("=" * 60)
        
        开始时间 = time.time()
        
        # 执行各种清理操作
        self.清理测试文件()
        self.清理临时文件()
        self.清理缓存文件()
        
        结束时间 = time.time()
        耗时 = 结束时间 - 开始时间
        
        # 显示清理结果
        print("\n" + "=" * 60)
        print("📊 清理结果统计")
        print("=" * 60)
        print(f"🗑️  删除文件数: {self.清理统计['删除文件数']}")
        print(f"📁 删除目录数: {self.清理统计['删除目录数']}")
        print(f"💾 释放空间: {self.格式化文件大小(self.清理统计['释放空间'])}")
        print(f"⏱️ 清理耗时: {耗时:.2f} 秒")
        
        if self.清理统计['删除文件数'] > 0 or self.清理统计['删除目录数'] > 0:
            print("\n🎉 项目清理完成！")
        else:
            print("\n💡 没有找到需要清理的文件")

def 主函数():
    """主函数"""
    print("🧹 统一清理工具")
    print("=" * 60)
    print("⚠️  此工具将删除测试文件、临时文件和缓存文件")
    print("💡 重要的核心功能文件将被保留")
    print("\n🚀 开始自动清理...")
    
    # 执行清理
    清理工具 = 统一清理工具()
    清理工具.执行完整清理()

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 清理被用户中断")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
