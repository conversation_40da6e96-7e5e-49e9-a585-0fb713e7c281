# 🔑 Gemini 2.5 Pro API密钥获取详细指南

## 📋 概述
本指南将详细说明如何获取Gemini 2.5 Pro的API密钥，并在系统中正确配置。

## 🌐 第一步：访问Google AI Studio

### 1.1 打开官方网站
- **网址**：https://aistudio.google.com/
- **备用网址**：https://makersuite.google.com/
- **说明**：这是Google官方的AI开发平台

### 1.2 网络要求
⚠️ **重要提醒**：
- 需要能够访问Google服务的网络环境
- 如果无法直接访问，可能需要使用VPN或代理
- 建议使用稳定的网络连接

## 👤 第二步：账户准备

### 2.1 Google账户要求
- **必需**：有效的Google账户（Gmail邮箱）
- **建议**：使用个人或企业Google账户
- **注意**：某些地区可能有访问限制

### 2.2 账户验证
- 确保Google账户已完成手机号验证
- 建议启用两步验证提高安全性

## 🔐 第三步：获取API密钥

### 3.1 登录AI Studio
1. 打开 https://aistudio.google.com/
2. 点击右上角"Sign in"按钮
3. 使用Google账户登录

### 3.2 创建API密钥
1. **进入API密钥页面**：
   - 在左侧导航栏找到"API keys"或"Get API key"
   - 或直接访问：https://aistudio.google.com/app/apikey

2. **创建新密钥**：
   - 点击"Create API key"按钮
   - 选择"Create API key in new project"（推荐）
   - 或选择现有的Google Cloud项目

3. **配置密钥权限**：
   - 确保选择了Gemini API访问权限
   - 建议限制API密钥的使用范围（可选）

4. **获取密钥**：
   - 系统会生成一个API密钥
   - **立即复制并保存**（密钥只显示一次）
   - 格式类似：`AIzaSyBsxU4m7X7WWwLehjg-6gv-TfjOx4orNqg`

### 3.3 密钥安全设置
1. **IP限制**（推荐）：
   - 在API密钥设置中添加IP白名单
   - 只允许特定IP地址使用此密钥

2. **API限制**：
   - 限制密钥只能访问Generative Language API
   - 避免过度权限

## 💰 第四步：了解配额和计费

### 4.1 免费配额
- **Gemini 1.5 Flash**：每分钟15个请求，每天1500个请求
- **Gemini 1.5 Pro**：每分钟2个请求，每天50个请求
- **Gemini 2.0 Flash**：每分钟10个请求，每天1000个请求

### 4.2 付费计划
- 如需更高配额，可以升级到付费计划
- 价格信息：https://ai.google.dev/pricing

### 4.3 配额监控
- 在AI Studio中可以查看API使用情况
- 建议定期检查使用量避免超限

## ⚙️ 第五步：在系统中配置API密钥

### 5.1 配置环境变量
1. **编辑.env文件**：
   ```bash
   # 打开项目根目录下的.env文件
   notepad .env
   ```

2. **更新API密钥**：
   ```env
   # 将your_gemini_api_key替换为实际的API密钥
   GEMINI_API_KEY=AIzaSyBsxU4m7X7WWwLehjg-6gv-TfjOx4orNqg
   ```

3. **保存文件**：
   - 确保文件编码为UTF-8
   - 保存后重启系统

### 5.2 验证配置
运行测试脚本验证配置：
```bash
python 测试Gemini2.5Pro.py
```

预期输出：
```
✅ 端点 1 可用
🤖 AI回复: 你好！我是 Gemini 2.5 Pro...
```

## 🔧 第六步：高级配置选项

### 6.1 多密钥配置（可选）
如果有多个API密钥，可以配置轮换使用：
```env
GEMINI_API_KEY_1=第一个密钥
GEMINI_API_KEY_2=第二个密钥
GEMINI_API_KEY_3=第三个密钥
```

### 6.2 代理配置（如需要）
如果需要通过代理访问：
```env
HTTP_PROXY=http://proxy.example.com:8080
HTTPS_PROXY=http://proxy.example.com:8080
```

## 🚨 常见问题解决

### Q1: 无法访问AI Studio网站
**解决方案**：
- 检查网络连接
- 尝试使用VPN或代理
- 确认DNS设置正确

### Q2: API密钥创建失败
**解决方案**：
- 确认Google账户已验证
- 检查是否在支持的地区
- 尝试刷新页面重新操作

### Q3: API调用返回403错误
**解决方案**：
- 检查API密钥是否正确
- 确认密钥权限设置
- 验证IP白名单配置

### Q4: API调用超出配额
**解决方案**：
- 检查当前使用量
- 等待配额重置（通常每分钟重置）
- 考虑升级到付费计划

### Q5: API密钥泄露怎么办
**解决方案**：
- 立即在AI Studio中删除泄露的密钥
- 创建新的API密钥
- 更新系统配置
- 检查是否有异常使用

## 🔒 安全最佳实践

### 1. 密钥保护
- ❌ 不要将API密钥提交到版本控制系统
- ❌ 不要在代码中硬编码API密钥
- ✅ 使用环境变量存储密钥
- ✅ 定期轮换API密钥

### 2. 访问控制
- 设置IP白名单限制访问
- 限制API权限范围
- 监控API使用情况

### 3. 备份策略
- 保存多个API密钥作为备份
- 记录密钥创建时间和用途
- 定期检查密钥状态

## 📞 技术支持

### 官方资源
- **文档**：https://ai.google.dev/docs
- **API参考**：https://ai.google.dev/api
- **社区论坛**：https://discuss.ai.google.dev/

### 紧急联系
如果遇到紧急问题：
1. 查看官方状态页面
2. 检查社区论坛
3. 联系Google Cloud支持

---

**提示**：API密钥是敏感信息，请妥善保管，不要分享给他人！
