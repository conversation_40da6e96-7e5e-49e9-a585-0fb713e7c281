# Gemini 2.5 Pro API 使用指南

## 📋 目录
1. [环境准备](#环境准备)
2. [API密钥获取](#api密钥获取)
3. [安装依赖](#安装依赖)
4. [配置环境变量](#配置环境变量)
5. [使用示例](#使用示例)
6. [常见问题](#常见问题)

## 🛠️ 环境准备

### 系统要求
- Python 3.9 或更高版本
- 稳定的网络连接（能访问 Google 服务）

### 检查 Python 版本
```bash
python --version
# 或
python3 --version
```

## 🔑 API密钥获取

### 步骤1: 访问 Google AI Studio
1. 打开浏览器，访问：https://aistudio.google.com/app/apikey
2. 使用 Google 账号登录

### 步骤2: 创建 API 密钥
1. 点击 "Create API key" 按钮
2. 选择 "Create API key in new project" 或选择现有项目
3. 复制生成的 API 密钥（格式类似：AIzaSyA...）

### 步骤3: 保存 API 密钥
⚠️ **重要**: API 密钥是敏感信息，请妥善保管，不要分享给他人！

## 📦 安装依赖

### 方法1: 安装官方 SDK（推荐）
```bash
pip install google-generativeai
```

### 方法2: 安装项目所有依赖
```bash
pip install -r requirements.txt
```

### 验证安装
```python
import google.generativeai as genai
print("✅ Google Generative AI SDK 安装成功")
```

## ⚙️ 配置环境变量

### 步骤1: 创建 .env 文件
```bash
# 复制示例文件
cp .env.example .env
```

### 步骤2: 编辑 .env 文件
打开 `.env` 文件，找到以下行：
```
GEMINI_API_KEY=your_gemini_api_key_here
```

将 `your_gemini_api_key_here` 替换为你的实际 API 密钥：
```
GEMINI_API_KEY=AIzaSyA1234567890abcdefghijklmnop
```

### 步骤3: 验证配置
```python
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('GEMINI_API_KEY')
print(f"API密钥配置: {'✅ 成功' if api_key else '❌ 失败'}")
```

## 🚀 使用示例

### 快速开始
```python
import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置 API 密钥
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# 创建模型实例
model = genai.GenerativeModel('gemini-2.5-flash')

# 生成内容
response = model.generate_content("你好，请介绍一下你的能力")
print(response.text)
```

### 运行完整示例
```bash
python Gemini2.5Pro完整示例.py
```

## 🔧 Gemini 2.5 Pro 特色功能

### 1. 思维功能 (Thinking)
Gemini 2.5 Pro 具有内置的"思维"能力，可以进行深度推理：

```python
# 启用思维功能
response = model.generate_content(
    "解决这个复杂问题：如何优化投资组合？",
    generation_config=genai.types.GenerationConfig(
        thinking_config=genai.types.ThinkingConfig(
            thinking_budget=1000  # 思维token预算
        )
    )
)
```

### 2. 多模态能力
支持文本、图像、视频等多种输入：

```python
import PIL.Image

# 加载图像
image = PIL.Image.open("your_image.jpg")

# 多模态分析
response = model.generate_content([
    "请分析这张图片中的内容",
    image
])
```

### 3. 长上下文处理
支持高达 100万 token 的上下文：

```python
# 处理长文档
long_text = "..." # 很长的文本内容
response = model.generate_content(f"请总结以下文档：\n{long_text}")
```

## ❓ 常见问题

### Q1: API 调用失败，返回 403 错误
**A**: 检查以下几点：
- API 密钥是否正确
- API 密钥是否已激活
- 网络是否能访问 Google 服务

### Q2: 提示 "模型不存在" 错误
**A**: 确保使用正确的模型名称：
- `gemini-2.5-flash` (推荐，速度快)
- `gemini-2.5-pro` (功能最强)
- `gemini-1.5-pro` (稳定版本)

### Q3: 网络连接问题
**A**: 如果在中国大陆使用，可能需要：
- 使用 VPN 或代理
- 配置代理设置
- 使用第三方 API 代理服务

### Q4: 如何控制生成内容的质量？
**A**: 调整生成参数：
```python
generation_config = {
    "temperature": 0.3,      # 降低随机性
    "max_output_tokens": 1000,  # 限制输出长度
    "top_p": 0.8,           # 核采样参数
    "top_k": 40             # Top-K 采样
}
```

### Q5: 如何处理 API 配额限制？
**A**: 
- 查看 [Google AI Studio](https://aistudio.google.com/) 中的配额使用情况
- 考虑升级到付费计划
- 优化提示词，减少不必要的 API 调用

## 📚 更多资源

- [Google AI Studio](https://aistudio.google.com/)
- [Gemini API 官方文档](https://ai.google.dev/gemini-api/docs)
- [Python SDK 文档](https://ai.google.dev/gemini-api/docs/quickstart?lang=python)
- [API 参考](https://ai.google.dev/api)

## 🆘 获取帮助

如果遇到问题：
1. 查看本指南的常见问题部分
2. 检查官方文档
3. 在项目中运行测试脚本：`python 测试Gemini连接.py`

---

**祝你使用愉快！** 🎉
