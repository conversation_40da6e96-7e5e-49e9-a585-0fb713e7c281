# Gemini 2.5 Pro API 调用总结

## 🎯 已为你准备的文件

我已经为你创建了完整的 Gemini 2.5 Pro API 调用示例和指南：

### 📁 核心文件
1. **`Gemini2.5Pro完整示例.py`** - 完整的 API 调用示例，包含多种功能
2. **`快速测试Gemini.py`** - 使用官方 SDK 的快速测试
3. **`简单HTTP调用示例.py`** - 使用 HTTP 请求的简单示例
4. **`Gemini2.5Pro使用指南.md`** - 详细的使用指南和文档

### 📦 环境配置
- **`requirements.txt`** - 已添加 `google-generativeai==0.8.3`
- **`.env.example`** - 环境变量配置示例

## 🚀 快速开始

### 第1步：安装依赖
```bash
pip install google-generativeai
```

### 第2步：获取 API 密钥
1. 访问：https://aistudio.google.com/app/apikey
2. 使用 Google 账号登录
3. 点击 "Create API key" 创建密钥
4. 复制生成的 API 密钥（格式：AIzaSyA...）

### 第3步：配置环境变量
```bash
# 复制配置文件
cp .env.example .env

# 编辑 .env 文件，设置你的 API 密钥
GEMINI_API_KEY=你的实际API密钥
```

### 第4步：运行测试
```bash
# 方式1：使用官方 SDK
python 快速测试Gemini.py

# 方式2：使用 HTTP 请求
python 简单HTTP调用示例.py

# 方式3：运行完整示例
python Gemini2.5Pro完整示例.py
```

## 💻 代码示例

### 最简单的调用方式

```python
import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置 API 密钥
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

# 创建模型
model = genai.GenerativeModel('gemini-2.5-flash')

# 生成内容
response = model.generate_content("你好，请介绍一下你的能力")
print(response.text)
```

### HTTP 请求方式

```python
import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('GEMINI_API_KEY')

url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"

headers = {
    "Content-Type": "application/json",
    "x-goog-api-key": api_key
}

data = {
    "contents": [{
        "parts": [{"text": "你好，请介绍一下你的能力"}]
    }]
}

response = requests.post(url, headers=headers, json=data)
result = response.json()
print(result["candidates"][0]["content"]["parts"][0]["text"])
```

## 🌟 Gemini 2.5 Pro 特色功能

### 1. 思维功能 (Thinking)
```python
response = model.generate_content(
    "解决复杂问题：如何优化投资组合？",
    generation_config=genai.types.GenerationConfig(
        thinking_config=genai.types.ThinkingConfig(
            thinking_budget=1000  # 思维token预算
        )
    )
)
```

### 2. 多模态能力
```python
import PIL.Image

# 处理图像
image = PIL.Image.open("image.jpg")
response = model.generate_content(["分析这张图片", image])
```

### 3. 长上下文处理
- 支持高达 100万 token 的上下文
- 可以处理超长文档和对话历史

### 4. 结构化输出
```python
prompt = """请以JSON格式分析股票：
{
    "stock_name": "股票名称",
    "price": "当前价格", 
    "recommendation": "投资建议"
}"""

response = model.generate_content(prompt)
```

## 🔧 可用的模型

1. **`gemini-2.5-flash`** - 速度最快，适合大多数任务
2. **`gemini-2.5-pro`** - 功能最强，适合复杂任务
3. **`gemini-1.5-pro`** - 稳定版本，兼容性好

## ⚠️ 常见问题

### Q: API 调用失败，返回 403 错误
**A**: 检查 API 密钥是否正确，是否已激活

### Q: 网络连接问题
**A**: 如果在中国大陆，可能需要使用 VPN 访问 Google 服务

### Q: 如何控制生成质量？
**A**: 调整参数：
```python
generation_config = {
    "temperature": 0.3,      # 降低随机性
    "max_output_tokens": 1000,  # 限制输出长度
    "top_p": 0.8,           # 核采样
    "top_k": 40             # Top-K 采样
}
```

## 📚 学习资源

- [Google AI Studio](https://aistudio.google.com/) - 在线测试平台
- [官方文档](https://ai.google.dev/gemini-api/docs) - 完整的 API 文档
- [Python SDK 文档](https://ai.google.dev/gemini-api/docs/quickstart?lang=python) - Python 使用指南

## 🎉 下一步

1. **运行测试脚本**，确保 API 正常工作
2. **阅读使用指南**：`Gemini2.5Pro使用指南.md`
3. **查看完整示例**：`Gemini2.5Pro完整示例.py`
4. **开始你的项目开发**

---

**祝你使用愉快！如果有任何问题，可以参考相关文档或运行测试脚本进行诊断。** 🚀
