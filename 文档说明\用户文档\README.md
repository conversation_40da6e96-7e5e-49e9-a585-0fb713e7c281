# 微信公众号内容自动化工作流系统

## 项目概述

这是一个完整的微信公众号内容自动化工作流系统，能够自动收集、分析、整合和发布公众号内容。

## 系统架构

```
公众号自动化系统
├── 内容收集模块 (Content Collector)
│   ├── 微信公众号API集成
│   ├── 内容爬虫
│   └── 数据存储
├── 内容分析模块 (Content Analyzer)
│   ├── 文本分析
│   ├── 关键信息提取
│   └── 价值评估
├── 内容整合模块 (Content Integrator)
│   ├── 内容重组
│   ├── 原创性改写
│   └── 文章生成
├── 自动发布模块 (Auto Publisher)
│   ├── 微信公众号发布API
│   ├── 内容审核
│   └── 发布调度
└── 系统管理模块 (System Manager)
    ├── 配置管理
    ├── 日志记录
    ├── 错误处理
    └── 定时任务
```

## 核心功能

1. **内容收集**：自动监控和抓取指定公众号文章
2. **内容分析**：提取有价值信息并进行质量评估
3. **内容整合**：重新组织内容生成原创文章
4. **自动发布**：定时发布到目标公众号
5. **系统管理**：配置管理、监控和日志

## 技术栈

- **开发语言**：Python 3.8+
- **Web框架**：Flask (用于API服务)
- **数据库**：SQLite (本地) / PostgreSQL (生产)
- **任务调度**：APScheduler
- **文本处理**：jieba, NLTK
- **AI集成**：OpenAI API / 本地大模型
- **HTTP客户端**：requests, aiohttp

## 项目结构

```
公众号全自动/
├── 源代码/                    # 源代码目录
│   ├── 内容收集器/            # 内容收集模块
│   ├── 内容分析器/            # 内容分析模块
│   ├── 内容整合器/            # 内容整合模块
│   ├── 自动发布器/            # 自动发布模块
│   ├── 系统管理器/            # 系统管理模块
│   └── 工具模块/              # 工具函数
├── 配置文件/                  # 配置文件
├── 数据存储/                  # 数据存储
├── 日志文件/                  # 日志文件
├── 测试文件/                  # 测试文件
├── 文档说明/                  # 文档
├── requirements.txt        # Python依赖
├── setup.py               # 安装脚本
├── 主程序.py                # 主程序入口
└── 清理脚本.py              # 文件清理工具
```

## 安装和使用

### 环境要求
- Python 3.8+
- pip 包管理器

### 快速开始
1. 按照 `环境准备清单.md` 配置环境
2. 安装依赖：`pip install -r requirements.txt`
3. 配置参数：编辑 `配置文件/系统配置.yaml`
4. 设置环境变量：复制 `.env.example` 为 `.env` 并填入真实配置
5. 运行系统：`python 主程序.py`

## 配置说明

系统支持通过配置文件管理不同的公众号源和发布目标，详见 `配置文件/系统配置.yaml`。

## 文档指南

- `环境准备清单.md` - 详细的环境配置指南
- `项目结构说明.md` - 完整的项目结构说明
- `开发规则文档.md` - 开发规范和注意事项
- `文件管理规则.md` - 文件管理和清理规则
- `使用指南.md` - 系统使用教程

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装Python 3.8+
# 创建虚拟环境
python -m venv 公众号自动化环境
公众号自动化环境\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置系统
```bash
# 复制配置文件
copy .env.example .env

# 编辑.env文件，至少配置：
# GEMINI_API_KEY=your_gemini_api_key
```

### 3. 测试运行
```bash
# 运行测试脚本
python 测试脚本.py

# 启动系统
python 主程序.py
```

## 🎯 核心特性

### ✅ 已实现功能
- **内容收集**：自动收集指定公众号文章（模拟数据）
- **智能分析**：集成Gemini AI进行内容质量评估
- **数据管理**：SQLite数据库存储和管理
- **系统监控**：实时状态监控和日志记录
- **中文界面**：完全中文化的界面和文档

### 🔧 技术亮点
- **AI集成**：支持Gemini API，免费额度高
- **模块化设计**：清晰的模块分离，易于维护
- **异步处理**：高效的并发任务处理
- **完善日志**：详细的运行日志和错误追踪
- **新手友好**：详细的文档和使用指南

## 📊 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   内容收集器    │───▶│   内容分析器    │───▶│   内容整合器    │
│  (模拟数据)     │    │  (Gemini AI)    │    │   (待完善)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    系统管理器 + 数据库                          │
│              (统一调度 + SQLite存储)                           │
└─────────────────────────────────────────────────────────────────┘
```

## 📚 文档指南

| 文档 | 说明 |
|------|------|
| `快速启动指南.md` | 🚀 三步快速启动系统 |
| `环境准备清单.md` | 🛠️ 详细环境配置指南 |
| `使用指南.md` | 📖 完整使用教程 |
| `开发规则文档.md` | 👨‍💻 开发规范和注意事项 |
| `项目结构说明.md` | 📁 详细项目结构说明 |
| `文件管理规则.md` | 🗂️ 文件分类和清理规则 |

## 🧪 测试功能

运行测试脚本验证系统功能：
```bash
python 测试脚本.py
```

测试内容：
- ✅ 配置加载
- ✅ 数据库连接
- ✅ 内容收集（模拟数据）
- ✅ AI内容分析
- ✅ 完整工作流程

## ⚠️ 重要说明

### 微信API限制
- **个人订阅号**：API功能受限，系统使用模拟数据
- **企业认证号**：可获得完整API权限
- **替代方案**：RSS订阅、网页爬虫等

### AI服务选择
- **推荐**：Gemini API（免费额度高，中文支持好）
- **备选**：OpenAI API（需付费，效果优秀）
- **本地**：支持本地AI模型部署

## 🔒 安全和合规

- 遵守微信公众号平台规则
- 注意内容版权和原创性
- 合理设置API调用频率
- 定期备份重要数据
- 保护API密钥安全

## 🛠️ 维护工具

- `清理脚本.py` - 自动清理临时文件
- `测试脚本.py` - 系统功能测试
- `主程序.py` - 系统主入口

## 📈 性能建议

- 调整收集频率避免过度调用API
- 设置合理的质量阈值筛选内容
- 定期清理日志和临时文件
- 监控系统资源使用情况

## 🤝 贡献指南

1. 遵循中文命名规范
2. 添加详细的中文注释
3. 更新相关文档
4. 运行测试确保功能正常

## 📞 技术支持

遇到问题时：
1. 查看 `日志文件/系统日志.log`
2. 运行 `python 测试脚本.py` 诊断
3. 参考相关文档解决方案
4. 检查网络连接和API配置

## 📄 许可证

MIT License - 详见 LICENSE 文件
