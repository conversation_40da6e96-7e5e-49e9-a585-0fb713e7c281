# 微信公众号自动化系统 - 使用指南

## 🚀 快速开始

### 第一步：环境准备
1. 按照 `环境准备清单.md` 安装Python和依赖包
2. 获取必要的API密钥（Gemini、微信等）
3. 配置环境变量文件

### 第二步：配置系统
1. 复制 `.env.example` 为 `.env`
2. 填入真实的API密钥和配置信息
3. 根据需要修改 `配置文件/系统配置.yaml`

### 第三步：启动系统
```bash
# 激活虚拟环境
公众号自动化环境\Scripts\activate

# 启动系统
python 主程序.py
```

## ⚙️ 详细配置说明

### 🔑 API密钥配置

#### Gemini API配置
```env
# .env 文件中添加
GEMINI_API_KEY=your_gemini_api_key_here
```

#### 微信公众号配置
```env
# .env 文件中添加
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 📝 源公众号配置

编辑 `配置文件/系统配置.yaml`：

```yaml
微信配置:
  源账号列表:
    - 名称: "科技前沿"
      账号ID: "tech_frontier"
      关键词: ["人工智能", "科技", "创新"]
      优先级: 1
    - 名称: "商业观察"  
      账号ID: "business_watch"
      关键词: ["商业", "创业", "投资"]
      优先级: 2
```

### ⏰ 发布时间配置

```yaml
自动发布:
  发布计划:
    启用: true
    发布时间:
      - "09:00"  # 早上9点
      - "18:00"  # 晚上6点
    时区: "Asia/Shanghai"
```

## 🎛️ 系统操作

### 启动系统
```bash
python 主程序.py
```

### 停止系统
- 在运行窗口按 `Ctrl+C`
- 系统会安全关闭所有模块

### 重启系统
```bash
# 停止系统后重新启动
python 主程序.py
```

## 📊 监控和管理

### 查看系统状态
```bash
# 查看实时日志
tail -f 日志文件/系统日志.log

# 查看错误日志
tail -f 日志文件/错误日志.log
```

### 系统清理
```bash
# 运行清理脚本
python 清理脚本.py
```

### 数据备份
```bash
# 运行备份脚本
python 备份脚本.py
```

## 🔧 常用操作

### 添加新的源公众号

1. 编辑配置文件：
```yaml
微信配置:
  源账号列表:
    - 名称: "新公众号名称"
      账号ID: "new_account_id"
      关键词: ["关键词1", "关键词2"]
      优先级: 3
```

2. 重启系统使配置生效

### 修改发布频率

1. 编辑配置文件：
```yaml
内容收集:
  抓取间隔: 120  # 改为2小时抓取一次

自动发布:
  频率限制:
    每日最大发布数: 2  # 每天最多发布2篇
    最小间隔小时: 6    # 最小间隔6小时
```

2. 重启系统使配置生效

### 调整内容质量阈值

```yaml
内容分析:
  质量阈值: 0.8  # 提高质量要求到0.8
```

## 📈 性能优化

### 调整并发设置
```yaml
系统管理:
  性能设置:
    最大并发数: 5      # 同时处理的任务数
    请求间隔: 2        # 请求间隔秒数
    超时时间: 30       # 请求超时时间
```

### 优化数据库性能
```bash
# 定期优化数据库
python -c "from 源代码.工具模块.数据库管理器 import 优化数据库; 优化数据库()"
```

## 🚨 故障排除

### 系统无法启动

#### 检查步骤：
1. **验证Python环境**
   ```bash
   python --version
   pip list | grep -E "(flask|requests|sqlalchemy)"
   ```

2. **检查配置文件**
   ```bash
   # 验证配置文件语法
   python -c "import yaml; yaml.safe_load(open('配置文件/系统配置.yaml', 'r', encoding='utf-8'))"
   ```

3. **检查权限**
   - 确保有读写 `数据存储/` 目录的权限
   - 确保有写入 `日志文件/` 目录的权限

### API调用失败

#### 解决方案：
1. **检查网络连接**
   ```bash
   ping google.com
   ```

2. **验证API密钥**
   - 确认Gemini API密钥有效
   - 检查微信API密钥是否过期

3. **检查API限制**
   - 确认没有超过调用频率限制
   - 检查账户余额（如适用）

### 内容收集失败

#### 可能原因：
1. **目标公众号不存在或已更改**
2. **网络连接问题**
3. **反爬虫机制**

#### 解决方案：
1. 验证公众号ID正确性
2. 调整抓取频率
3. 检查日志了解具体错误

### 发布失败

#### 常见问题：
1. **微信API权限不足**
2. **内容不符合平台规范**
3. **发布频率过高**

#### 解决方案：
1. 检查微信公众号权限设置
2. 调整内容过滤规则
3. 降低发布频率

## 📋 日常维护

### 每日检查清单
- [ ] 查看系统运行状态
- [ ] 检查错误日志
- [ ] 确认发布是否正常
- [ ] 监控磁盘空间

### 每周维护清单
- [ ] 运行清理脚本
- [ ] 备份重要数据
- [ ] 检查系统性能
- [ ] 更新配置（如需要）

### 每月维护清单
- [ ] 更新Python依赖包
- [ ] 清理历史日志
- [ ] 检查API使用量
- [ ] 优化系统配置

## 🔒 安全注意事项

### API密钥安全
- 不要在代码中硬编码密钥
- 定期更换API密钥
- 不要分享 `.env` 文件

### 内容合规
- 确保收集的内容符合版权要求
- 生成的内容要原创性足够
- 避免发布敏感或违规内容

### 系统安全
- 定期备份重要数据
- 监控系统访问日志
- 及时更新系统依赖

## 📞 获取帮助

### 查看日志
```bash
# 查看最近的错误
grep "ERROR" 日志文件/系统日志.log | tail -10

# 查看特定时间的日志
grep "2025-07-27 12:00" 日志文件/系统日志.log
```

### 系统诊断
```bash
# 运行系统诊断（如果有）
python 诊断脚本.py
```

### 重置配置
```bash
# 重置为默认配置（谨慎使用）
cp 配置文件/系统配置.yaml.backup 配置文件/系统配置.yaml
```

## 🎯 最佳实践

### 内容策略
1. **多样化源头**：配置多个不同类型的源公众号
2. **质量优先**：设置较高的质量阈值
3. **定期调整**：根据效果调整配置参数

### 发布策略
1. **合理频率**：避免过于频繁的发布
2. **时间优化**：选择用户活跃的时间发布
3. **内容审核**：启用发布前审核功能

### 系统维护
1. **定期监控**：关注系统运行状态
2. **及时备份**：重要数据要定期备份
3. **持续优化**：根据使用情况优化配置

---

**提示**：系统使用过程中如遇到问题，请先查看相关日志文件，大多数问题都能从日志中找到原因。
