# 微信公众号自动化系统 - 开发规则文档

## 📖 文档概述
本文档为编程新手提供详细的开发规则和注意事项，帮助您安全、高效地使用和维护微信公众号自动化系统。

## 🏗️ 项目结构说明

### 📁 目录结构
```
公众号全自动/
├── 源代码/                    # 核心源代码目录
│   ├── 内容收集器/            # 负责收集公众号文章
│   ├── 内容分析器/            # 负责分析文章内容
│   ├── 内容整合器/            # 负责整合生成新文章
│   ├── 自动发布器/            # 负责自动发布文章
│   ├── 系统管理器/            # 负责系统管理和调度
│   └── 工具模块/              # 提供基础工具功能
├── 配置文件/                  # 系统配置文件
├── 数据存储/                  # 数据库和数据文件
├── 日志文件/                  # 系统运行日志
├── 测试文件/                  # 测试代码和测试数据
├── 文档说明/                  # 项目文档
├── 主程序.py                  # 系统主入口
├── 清理脚本.py                # 文件清理工具
└── requirements.txt           # Python依赖包列表
```

### 🔧 核心模块功能

#### 内容收集器
- **功能**：从指定公众号抓取文章内容
- **文件**：`源代码/内容收集器/`
- **作用**：数据来源，为后续分析提供原材料

#### 内容分析器
- **功能**：分析文章质量、提取关键信息
- **文件**：`源代码/内容分析器/`
- **作用**：筛选有价值内容，提高生成文章质量

#### 内容整合器
- **功能**：将多篇文章整合成原创内容
- **文件**：`源代码/内容整合器/`
- **作用**：核心创作模块，生成新文章

#### 自动发布器
- **功能**：自动发布文章到公众号
- **文件**：`源代码/自动发布器/`
- **作用**：自动化发布，减少人工操作

#### 系统管理器
- **功能**：统一管理各个模块，调度任务
- **文件**：`源代码/系统管理器/`
- **作用**：系统核心，协调各模块工作

## ⚠️ 重要注意事项

### 🚨 绝对禁止的操作

#### 1. 删除核心文件
❌ **禁止删除以下文件**：
- 任何 `.py` 源代码文件
- `配置文件/系统配置.yaml`
- `requirements.txt`
- 数据库文件 `*.db`

#### 2. 随意修改配置
❌ **禁止随意修改**：
- 数据库连接配置
- API密钥配置
- 系统核心参数

#### 3. 直接编辑数据库
❌ **禁止直接操作**：
- 不要直接编辑 `.db` 文件
- 不要手动修改数据库表结构
- 不要删除重要数据表

### ✅ 安全的操作

#### 1. 配置修改
✅ **可以安全修改**：
- 源公众号列表
- 发布时间设置
- 日志级别设置
- 抓取频率设置

#### 2. 数据管理
✅ **可以安全操作**：
- 查看日志文件
- 清理临时文件
- 备份重要数据
- 查看统计报告

## 🛠️ 代码修改规范

### 📝 修改前准备

#### 1. 备份重要文件
```bash
# 备份配置文件
copy 配置文件\系统配置.yaml 配置文件\系统配置.yaml.backup

# 备份数据库
copy 数据存储\公众号自动化.db 数据存储\公众号自动化.db.backup
```

#### 2. 理解代码结构
- 阅读相关模块的文档注释
- 了解函数的输入输出参数
- 确认修改的影响范围

#### 3. 测试环境准备
- 在测试环境中进行修改
- 确保有完整的回滚方案
- 准备测试数据和测试用例

### 🔧 修改规则

#### 1. 命名规范
```python
# ✅ 正确的中文命名
def 获取文章列表(公众号ID: str) -> List[Dict]:
    """获取指定公众号的文章列表"""
    pass

# ❌ 错误的混合命名
def get文章List(account_id: str) -> List[Dict]:
    pass
```

#### 2. 注释规范
```python
# ✅ 详细的中文注释
def 分析文章质量(文章内容: str) -> float:
    """
    分析文章质量并返回评分
    
    参数:
        文章内容: 要分析的文章文本
        
    返回:
        质量评分 (0.0-1.0)
        
    注意:
        评分越高表示质量越好
    """
    pass
```

#### 3. 错误处理
```python
# ✅ 完善的错误处理
try:
    结果 = 执行某个操作()
    return 结果
except Exception as 错误:
    日志器.error(f"操作失败: {错误}")
    return None
```

### 🧪 测试规范

#### 1. 单元测试
- 每个函数都应该有对应的测试
- 测试文件放在 `测试文件/` 目录
- 使用中文命名测试函数

#### 2. 集成测试
- 测试模块间的协作
- 验证数据流的正确性
- 确保系统整体功能正常

#### 3. 性能测试
- 监控内存使用情况
- 检查处理速度
- 验证并发处理能力

## 🔒 安全使用指南

### 🔐 API密钥管理

#### 1. 密钥存储
```bash
# ✅ 使用环境变量文件
echo "GEMINI_API_KEY=your_api_key" >> .env
echo "WECHAT_APP_SECRET=your_secret" >> .env
```

#### 2. 密钥保护
- 不要在代码中硬编码密钥
- 不要将 `.env` 文件提交到版本控制
- 定期更换API密钥

### 🛡️ 数据安全

#### 1. 数据备份
```python
# 定期备份重要数据
python 备份脚本.py
```

#### 2. 访问控制
- 限制数据库文件访问权限
- 设置日志文件访问权限
- 保护配置文件不被误修改

### 🌐 网络安全

#### 1. API调用限制
- 遵守各平台的API调用频率限制
- 实现重试机制和错误处理
- 监控API使用量

#### 2. 内容合规
- 确保收集的内容符合版权要求
- 生成的内容要符合平台规范
- 避免发布敏感或违规内容

## 🚨 常见问题和解决方案

### ❓ 系统无法启动

#### 问题排查步骤：
1. **检查Python环境**
   ```bash
   python --version
   pip list
   ```

2. **检查配置文件**
   - 确认 `配置文件/系统配置.yaml` 存在
   - 检查配置文件格式是否正确

3. **检查依赖包**
   ```bash
   pip install -r requirements.txt
   ```

4. **查看错误日志**
   - 检查 `日志文件/系统日志.log`
   - 查看具体错误信息

### ❓ 数据库连接失败

#### 解决方案：
1. **检查数据库文件**
   - 确认 `数据存储/公众号自动化.db` 存在
   - 检查文件权限

2. **重新初始化数据库**
   ```python
   python -c "from 源代码.工具模块.数据库管理器 import 初始化数据库; 初始化数据库()"
   ```

### ❓ API调用失败

#### 解决方案：
1. **检查API密钥**
   - 确认 `.env` 文件中的密钥正确
   - 验证密钥是否过期

2. **检查网络连接**
   - 确认能够访问API服务器
   - 检查防火墙设置

3. **查看API限制**
   - 确认没有超过调用频率限制
   - 检查账户余额（如适用）

## 📞 技术支持

### 🆘 获取帮助

#### 1. 查看日志
```bash
# 查看最新日志
tail -f 日志文件/系统日志.log

# 查看错误日志
grep "ERROR" 日志文件/系统日志.log
```

#### 2. 系统诊断
```python
# 运行系统诊断
python 诊断脚本.py
```

#### 3. 重置系统
```python
# 重置配置（谨慎使用）
python 重置脚本.py
```

### 📋 报告问题

报告问题时请提供：
1. 详细的错误描述
2. 相关的日志信息
3. 系统环境信息
4. 重现问题的步骤

## 🎯 最佳实践

### ✨ 开发建议

1. **小步快跑**：每次只修改一个小功能
2. **频繁测试**：修改后立即测试
3. **及时备份**：重要修改前先备份
4. **详细记录**：记录每次修改的内容和原因

### 🔄 维护建议

1. **定期清理**：每周运行一次清理脚本
2. **监控日志**：定期查看系统日志
3. **更新依赖**：定期更新Python包
4. **备份数据**：定期备份重要数据

### 📈 性能优化

1. **监控资源**：关注CPU和内存使用
2. **优化查询**：优化数据库查询语句
3. **缓存机制**：合理使用缓存减少重复计算
4. **并发控制**：合理设置并发数量

## 📚 相关文档

- `环境准备清单.md` - 环境配置指南
- `文件管理规则.md` - 文件管理说明
- `项目结构说明.md` - 详细项目结构
- `使用指南.md` - 系统使用教程

---

**记住**：安全第一，谨慎操作，遇到问题及时查看日志和文档！
