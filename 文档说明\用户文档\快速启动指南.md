# 🚀 微信公众号自动化系统 - 快速启动指南

## 📋 启动前检查清单

### ✅ 环境准备
- [ ] Python 3.8+ 已安装
- [ ] 虚拟环境已创建并激活
- [ ] 依赖包已安装 (`pip install -r requirements.txt`)
- [ ] 配置文件已设置

### ✅ 配置检查
- [ ] `.env` 文件已创建（从 `.env.example` 复制）
- [ ] API密钥已配置（Gemini推荐）
- [ ] 源公众号列表已配置
- [ ] 数据库路径已确认

## 🎯 三步快速启动

### 第一步：安装依赖
```bash
# 激活虚拟环境
公众号自动化环境\Scripts\activate  # Windows
# source 公众号自动化环境/bin/activate  # macOS/Linux

# 安装依赖包
pip install -r requirements.txt
```

### 第二步：配置系统
```bash
# 复制环境变量配置文件
copy .env.example .env

# 编辑 .env 文件，填入真实的API密钥
# 至少需要配置 GEMINI_API_KEY
```

### 第三步：启动系统
```bash
# 运行测试脚本（推荐）
python 测试脚本.py

# 或直接启动主程序
python 主程序.py
```

## 🔧 详细配置说明

### 1. 环境变量配置 (.env)
```env
# 必需配置
GEMINI_API_KEY=your_gemini_api_key_here

# 可选配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
DEBUG=false
LOG_LEVEL=INFO
```

### 2. 系统配置 (配置文件/系统配置.yaml)
```yaml
# 源公众号配置
微信配置:
  源账号列表:
    - 名称: "科技前沿"
      账号ID: "tech_frontier"
      关键词: ["人工智能", "科技", "创新"]
      优先级: 1

# 内容收集配置
内容收集:
  抓取间隔: 60  # 分钟
  每次抓取数量: 10

# 内容分析配置
内容分析:
  AI模型:
    服务商: "gemini"
    API密钥: "your_gemini_api_key"
  质量阈值: 0.7
```

## 🧪 测试系统功能

### 运行完整测试
```bash
python 测试脚本.py
```

测试内容包括：
- ✅ 配置加载测试
- ✅ 数据库连接测试
- ✅ 内容收集测试
- ✅ 内容分析测试
- ✅ 完整流程测试

### 预期输出示例
```
🚀 微信公众号自动化系统 - 功能测试
==================================================
🔍 测试配置加载...
✅ 配置加载成功

🔍 测试数据库连接...
✅ 数据库连接成功

🔍 测试内容收集功能...
✅ 收集完成，收集到 5 篇文章

🔍 测试内容分析功能...
✅ 分析完成，成功分析 5 篇，高质量文章 3 篇

🎯 测试完成: 5/5 项通过
🎉 所有测试通过！系统功能正常。
```

## 📊 系统监控

### 查看系统状态
系统运行时会自动输出状态信息：
```
系统状态监控 - 总文章数: 15, 已分析: 10, 高质量: 6
```

### 查看日志文件
```bash
# 查看实时日志
tail -f 日志文件/系统日志.log

# 查看错误日志
grep "ERROR" 日志文件/系统日志.log
```

### 数据库查看
- 数据库文件：`数据存储/公众号自动化.db`
- 可使用SQLite Browser等工具查看

## 🚨 常见问题解决

### 问题1：依赖包安装失败
```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
```

### 问题2：Gemini API调用失败
- 检查API密钥是否正确
- 确认网络连接正常
- 查看API使用限制

### 问题3：数据库初始化失败
- 检查 `数据存储/` 目录权限
- 确认磁盘空间充足
- 查看详细错误日志

### 问题4：系统无法启动
```bash
# 检查Python版本
python --version

# 检查虚拟环境
which python

# 重新安装依赖
pip install --upgrade -r requirements.txt
```

## 🎛️ 系统控制

### 启动系统
```bash
python 主程序.py
```

### 停止系统
- 在运行窗口按 `Ctrl+C`
- 系统会安全关闭所有模块

### 重启系统
```bash
# 停止后重新启动
python 主程序.py
```

## 📈 性能优化建议

### 1. 调整收集频率
```yaml
内容收集:
  抓取间隔: 120  # 改为2小时，减少API调用
```

### 2. 限制并发数量
```yaml
系统管理:
  最大并发数: 3  # 根据系统性能调整
```

### 3. 优化质量阈值
```yaml
内容分析:
  质量阈值: 0.8  # 提高阈值，只处理高质量内容
```

## 🔄 日常维护

### 每日检查
- [ ] 查看系统运行状态
- [ ] 检查错误日志
- [ ] 确认数据收集正常

### 每周维护
- [ ] 运行清理脚本：`python 清理脚本.py`
- [ ] 备份重要数据
- [ ] 检查磁盘空间

### 每月维护
- [ ] 更新依赖包：`pip install --upgrade -r requirements.txt`
- [ ] 检查API使用量
- [ ] 优化系统配置

## 📞 获取帮助

### 查看详细文档
- `环境准备清单.md` - 环境配置指南
- `开发规则文档.md` - 开发规范
- `使用指南.md` - 详细使用说明
- `文件管理规则.md` - 文件管理规则

### 故障排除
1. 查看错误日志了解具体问题
2. 检查配置文件是否正确
3. 确认网络连接和API密钥
4. 参考常见问题解决方案

---

**提示**：首次使用建议先运行测试脚本确认系统功能正常，然后再启动完整系统。
