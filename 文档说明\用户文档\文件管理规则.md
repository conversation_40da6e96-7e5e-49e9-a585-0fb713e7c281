# 文件管理规则说明

## 📁 文件分类说明

### 🔒 核心文件（禁止删除）
这些文件是系统运行的核心，删除后会导致系统无法正常工作：

#### 程序文件
- `主程序.py` - 系统主入口文件
- `源代码/` - 所有源代码文件
  - `工具模块/` - 基础工具模块
  - `内容收集器/` - 内容收集功能
  - `内容分析器/` - 内容分析功能
  - `内容整合器/` - 内容整合功能
  - `自动发布器/` - 自动发布功能
  - `系统管理器/` - 系统管理功能

#### 配置文件
- `配置文件/系统配置.yaml` - 主配置文件
- `.env` - 环境变量配置文件
- `requirements.txt` - Python依赖包列表
- `setup.py` - 安装脚本

#### 文档文件
- `README.md` - 项目说明文档
- `环境准备清单.md` - 环境配置指南
- `文件管理规则.md` - 本文件
- `开发规则文档.md` - 开发规范文档

### 🗂️ 数据文件（谨慎删除）
这些文件包含重要数据，删除前请确认已备份：

#### 数据库文件
- `数据存储/公众号自动化.db` - SQLite数据库文件
- `数据存储/备份文件/` - 数据库备份文件

#### 用户数据
- `数据存储/用户配置/` - 用户自定义配置
- `数据存储/文章内容/` - 收集的文章内容
- `数据存储/生成文章/` - 系统生成的文章

### 🧹 临时文件（可以删除）
这些文件可以安全删除，系统会自动重新生成：

#### 日志文件
- `日志文件/系统日志.log` - 当前日志文件
- `日志文件/系统日志.log.*.zip` - 历史日志压缩文件
- `日志文件/错误日志.log` - 错误日志文件

#### 缓存文件
- `__pycache__/` - Python字节码缓存
- `*.pyc` - Python编译文件
- `.pytest_cache/` - 测试缓存文件

#### 临时文件
- `临时文件/` - 系统临时文件目录
- `*.tmp` - 临时文件
- `*.temp` - 临时文件

### 🔄 自动生成文件（可以删除）
这些文件由系统自动生成，删除后会重新创建：

#### 运行时文件
- `*.pid` - 进程ID文件
- `*.lock` - 锁文件
- `运行状态.json` - 运行状态文件

#### 统计文件
- `统计报告/` - 系统生成的统计报告
- `性能监控/` - 性能监控数据

## 🛠️ 自动清理机制

### 清理策略
1. **日志文件清理**：保留最近30天的日志文件
2. **缓存文件清理**：定期清理Python缓存文件
3. **临时文件清理**：清理超过7天的临时文件
4. **备份文件清理**：保留最近10个备份文件

### 清理时机
- **每日清理**：凌晨2点自动执行
- **手动清理**：运行清理脚本
- **启动清理**：系统启动时清理过期文件

## 📋 文件安全等级

### 🔴 高风险文件（绝对不能删除）
- 所有 `.py` 源代码文件
- `配置文件/系统配置.yaml`
- `requirements.txt`
- 数据库文件 `*.db`

### 🟡 中风险文件（删除前需备份）
- 用户配置文件
- 重要数据文件
- 自定义模板文件

### 🟢 低风险文件（可以安全删除）
- 日志文件
- 缓存文件
- 临时文件
- 自动生成的报告

## 🔧 文件权限管理

### 只读文件
- 核心配置文件（防止意外修改）
- 重要数据文件（防止数据丢失）

### 可写文件
- 日志文件
- 临时文件
- 用户数据文件

### 可执行文件
- `主程序.py`
- `清理脚本.py`
- `备份脚本.py`

## 📊 存储空间管理

### 空间监控
- 监控数据存储目录大小
- 监控日志文件大小
- 监控临时文件大小

### 空间优化
- 压缩历史日志文件
- 清理过期临时文件
- 优化数据库存储

### 空间告警
- 磁盘空间不足时发送告警
- 单个文件过大时发送提醒
- 总存储空间超过阈值时清理

## 🔄 备份策略

### 自动备份
- **每日备份**：重要数据文件
- **每周备份**：完整系统备份
- **每月备份**：长期存档备份

### 备份内容
- 数据库文件
- 配置文件
- 用户数据
- 重要日志

### 备份位置
- 本地备份：`数据存储/备份文件/`
- 云端备份：（可选配置）
- 外部存储：（可选配置）

## ⚠️ 注意事项

### 删除前检查
1. 确认文件类型和重要性
2. 检查是否有其他程序在使用
3. 确认是否已备份重要数据
4. 测试删除后系统是否正常

### 恢复机制
1. 从备份文件恢复
2. 重新生成配置文件
3. 重新安装依赖包
4. 重新初始化数据库

### 紧急情况处理
1. 误删重要文件时立即停止系统
2. 从最近的备份恢复数据
3. 检查系统完整性
4. 重新启动系统并验证功能

## 📞 技术支持

如果遇到文件管理问题：
1. 查看系统日志了解详情
2. 检查文件权限设置
3. 确认磁盘空间是否充足
4. 参考备份文件进行恢复
