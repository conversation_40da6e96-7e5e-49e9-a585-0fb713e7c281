# 微信公众号自动化系统 - 环境准备清单

## 📋 概述
本文档为编程新手提供详细的环境准备指南，帮助您从零开始搭建微信公众号自动化系统的开发环境。

## 🐍 Python环境安装

### 1. Python安装（推荐Python 3.9-3.11）

#### Windows系统：
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新的Python 3.11版本
3. 运行安装程序，**重要**：勾选"Add Python to PATH"
4. 选择"Customize installation"
5. 确保勾选"pip"和"Add Python to environment variables"
6. 安装完成后，打开命令提示符，输入：
   ```bash
   python --version
   pip --version
   ```
   如果显示版本号，说明安装成功

#### macOS系统：
```bash
# 使用Homebrew安装（推荐）
brew install python@3.11

# 或者下载官方安装包
# 访问 https://www.python.org/downloads/macos/
```

#### Linux系统：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip python3.11-venv

# CentOS/RHEL
sudo yum install python3.11 python3.11-pip
```

### 2. 虚拟环境设置
```bash
# 创建虚拟环境
python -m venv 公众号自动化环境

# 激活虚拟环境
# Windows:
公众号自动化环境\Scripts\activate

# macOS/Linux:
source 公众号自动化环境/bin/activate

# 升级pip
pip install --upgrade pip
```

## 🛠️ 开发工具安装

### 1. 代码编辑器（选择其一）

#### Visual Studio Code（推荐）
1. 下载：[VS Code官网](https://code.visualstudio.com/)
2. 安装必要插件：
   - Python
   - Python Docstring Generator
   - GitLens
   - Chinese (Simplified) Language Pack

#### PyCharm Community Edition
1. 下载：[PyCharm官网](https://www.jetbrains.com/pycharm/download/)
2. 选择Community版本（免费）

### 2. Git版本控制
#### Windows：
下载 [Git for Windows](https://git-scm.com/download/win)

#### macOS：
```bash
brew install git
```

#### Linux：
```bash
sudo apt install git  # Ubuntu/Debian
sudo yum install git   # CentOS/RHEL
```

### 3. 数据库工具（可选）
- **SQLite Browser**：用于查看SQLite数据库
- **DBeaver**：通用数据库管理工具

## 🔑 微信公众号API配置

### 1. 微信公众平台注册
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 注册订阅号账户
3. 完成认证（个人订阅号功能有限）

### 2. 获取API密钥
1. 登录微信公众平台
2. 进入"开发" → "基本配置"
3. 获取以下信息：
   - **AppID**：应用ID
   - **AppSecret**：应用密钥（需要管理员扫码确认）

### 3. 订阅号API限制说明
⚠️ **重要提醒**：个人订阅号API功能受限
- 无法主动推送消息给用户
- 无法获取用户列表
- 无法群发消息
- 主要用于被动回复和菜单管理

### 4. 替代方案
由于订阅号限制，建议使用以下方案：
- **网页版微信公众号**：通过模拟登录获取内容
- **RSS订阅**：使用第三方RSS服务
- **爬虫技术**：合规地抓取公开内容

## 🤖 AI服务配置

### 1. Google Gemini API（推荐）

#### 获取API密钥：
1. 访问 [Google AI Studio](https://makersuite.google.com/)
2. 使用Google账号登录
3. 创建新的API密钥
4. 复制API密钥备用

#### 配置示例：
```yaml
# 在配置文件中添加
ai_service:
  provider: "gemini"
  api_key: "your_gemini_api_key"
  model: "gemini-pro"
  base_url: "https://generativelanguage.googleapis.com"
```

### 2. OpenAI API（备选）
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册账户并获取API密钥
3. 注意：需要付费使用

### 3. 本地AI模型（高级选项）
- **Ollama**：本地运行大模型
- **LM Studio**：图形化本地模型管理

## 📦 依赖包安装

### 1. 基础依赖安装
```bash
# 激活虚拟环境后执行
pip install -r requirements.txt
```

### 2. 核心依赖说明
```txt
# Web框架
flask==2.3.3
requests==2.31.0

# 数据库
sqlalchemy==2.0.21

# 任务调度
apscheduler==3.10.4

# 文本处理
jieba==0.42.1
beautifulsoup4==4.12.2

# AI服务
google-generativeai==0.3.2  # Gemini
openai==0.28.1              # OpenAI（可选）

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 日志
loguru==0.7.2
```

## 🔧 系统配置

### 1. 环境变量配置
创建 `.env` 文件：
```env
# 微信配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# AI服务配置
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key  # 可选

# 系统配置
DEBUG=false
LOG_LEVEL=INFO
```

### 2. 防火墙配置
如果运行Web服务，确保以下端口开放：
- **5000**：Flask应用端口
- **80/443**：HTTP/HTTPS端口（如需外网访问）

## 🌐 网络配置

### 1. 代理设置（如需要）
```bash
# 设置代理环境变量
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080
```

### 2. DNS配置
确保能够访问以下域名：
- `api.openai.com`（OpenAI）
- `generativelanguage.googleapis.com`（Gemini）
- `mp.weixin.qq.com`（微信公众平台）

## ✅ 环境验证

### 1. Python环境检查
```bash
python --version  # 应显示 3.9+ 版本
pip list          # 查看已安装包
```

### 2. 依赖检查脚本
创建 `环境检查.py`：
```python
import sys
import importlib

required_packages = [
    'flask', 'requests', 'sqlalchemy', 
    'apscheduler', 'jieba', 'beautifulsoup4',
    'yaml', 'dotenv', 'loguru'
]

print("检查Python版本...")
print(f"Python版本: {sys.version}")

print("\n检查必要包...")
for package in required_packages:
    try:
        importlib.import_module(package)
        print(f"✅ {package} - 已安装")
    except ImportError:
        print(f"❌ {package} - 未安装")

print("\n环境检查完成！")
```

## 🚨 常见问题解决

### 1. Python安装问题
- **问题**：命令行找不到python命令
- **解决**：重新安装Python，确保勾选"Add to PATH"

### 2. pip安装失败
- **问题**：网络超时或包安装失败
- **解决**：使用国内镜像源
```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ package_name
```

### 3. 虚拟环境问题
- **问题**：无法激活虚拟环境
- **解决**：检查路径，使用绝对路径

### 4. API访问问题
- **问题**：无法访问Gemini API
- **解决**：检查网络连接，确认API密钥正确

## 📞 技术支持

如果遇到问题，请：
1. 查看错误日志
2. 检查网络连接
3. 确认配置文件正确
4. 参考官方文档

## 🎯 下一步
环境准备完成后，请继续阅读：
- `项目结构说明.md`
- `开发规则文档.md`
- `使用指南.md`
