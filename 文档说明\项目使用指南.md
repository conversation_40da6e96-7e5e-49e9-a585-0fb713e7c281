# 📖 微信公众号自动化系统 - 项目使用指南

## 🎯 项目概述

这是一个微信公众号内容自动化工作流系统，主要功能包括：
- 📰 自动收集公众号文章
- 🤖 智能分析文章内容
- ✍️ 自动整合生成原创内容
- 📤 自动发布到公众号
- 🧠 智能学习系统

## 🚀 快速开始

### 第一步：环境准备

1. **检查Python环境**
   ```bash
   python --version  # 需要Python 3.8+
   ```

2. **安装依赖包**
   ```bash
   pip install -r 配置文件/requirements.txt
   ```

3. **设置环境变量**
   ```bash
   # 创建.env文件，添加以下内容：
   GEMINI_API_KEY=your_gemini_api_key_here
   WECHAT_APP_ID=your_wechat_app_id
   WECHAT_APP_SECRET=your_wechat_app_secret
   ```

### 第二步：配置系统

1. **编辑系统配置**
   - 打开 `配置文件/系统配置.yaml`
   - 修改微信配置中的应用ID和密钥
   - 设置要监控的源公众号列表

2. **配置公众号信息**
   - 打开 `配置文件/公众号配置.py`
   - 添加要监控的公众号信息

### 第三步：启动系统

#### 方式1：启动完整系统（推荐新手）
```bash
python 源代码/核心程序/主程序.py
```

#### 方式2：启动智能学习系统
```bash
python 智能学习系统/启动智能学习系统.py
```

#### 方式3：单独测试模块
```bash
# 测试文章收集
python 源代码/内容收集器/主采集程序.py

# 测试智能学习
python 智能学习系统/测试学习系统.py
```

## 📁 项目结构说明

```
公众号全自动/
├── 源代码/                    # 核心源代码
│   ├── 内容收集器/            # 文章采集模块
│   ├── 内容分析器/            # 内容分析模块
│   ├── 内容整合器/            # 内容整合模块
│   ├── 自动发布器/            # 自动发布模块
│   ├── 系统管理器/            # 系统管理模块
│   └── 核心程序/              # 主程序入口
├── 智能学习系统/              # 智能学习模块
├── 配置文件/                  # 配置文件
├── 数据存储/                  # 数据库和数据文件
├── 日志文件/                  # 系统日志
├── 文档说明/                  # 项目文档
└── .augment/                  # Augment配置
```

## ⚙️ 配置说明

### 系统配置文件
- **位置**: `配置文件/系统配置.yaml`
- **内容**: 数据库、微信、AI模型等配置

### 环境变量配置
- **位置**: `.env` 文件（需要创建）
- **内容**: API密钥等敏感信息

### 公众号配置
- **位置**: `配置文件/公众号配置.py`
- **内容**: 监控的公众号列表和示例链接

## 🔧 常用操作

### 1. 收集文章
```bash
cd 源代码/内容收集器
python 主采集程序.py
```

### 2. 查看日志
```bash
# 查看系统日志
type 日志文件/系统日志.log

# 查看学习日志
type 智能学习系统/学习日志/智能学习系统.log
```

### 3. 清理临时文件
```bash
python 工具脚本/统一清理工具.py
```

### 4. 备份数据
```bash
# 手动备份数据库
copy 数据存储/公众号自动化.db 数据存储/公众号自动化.db.backup
```

## 🚨 注意事项

### 安全提醒
1. **不要泄露API密钥** - 确保.env文件不被提交到版本控制
2. **定期备份数据** - 重要数据要定期备份
3. **遵守平台规则** - 确保内容符合各平台规范

### 常见问题
1. **系统无法启动** - 检查Python环境和依赖包
2. **API调用失败** - 检查API密钥是否正确
3. **数据库错误** - 检查数据库文件权限

## 📞 获取帮助

1. **查看开发规则** - 阅读 `.augment/rules/开发规则文档.md`
2. **查看模块文档** - 各模块目录下的README文件
3. **查看日志信息** - 系统会记录详细的运行日志

## 🎯 下一步计划

1. **完善测试** - 为核心功能编写单元测试
2. **优化性能** - 根据使用情况优化系统性能
3. **扩展功能** - 根据需求添加新功能
4. **完善文档** - 补充更详细的使用文档

---

**记住**: 作为编程新手，建议先从单个模块开始测试，熟悉后再启动完整系统！
