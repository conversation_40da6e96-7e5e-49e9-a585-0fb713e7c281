#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能学习系统主启动器

作者: AI助手
日期: 2025-07-27
功能: 智能学习系统的统一启动入口
"""

import os
import sys
import time
from datetime import datetime

# 添加核心程序和配置文件路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(当前目录)
sys.path.append(os.path.join(当前目录, '核心程序'))
sys.path.append(os.path.join(根目录, '配置文件'))  # 使用根目录的配置文件夹

try:
    from 智能学习配置 import 显示配置信息, 创建必要目录, 获取配置
    from 环境变量配置 import 检查必需环境变量, 显示环境变量状态, 设置环境变量提示, 获取API密钥
except ImportError as e:
    print(f"❌ 导入配置失败: {e}")
    sys.exit(1)


def 检查依赖():
    """检查必要的依赖包"""
    print("🔍 检查系统依赖...")
    
    必要包 = [
        'schedule',
        'google.generativeai',
        'requests',
        'beautifulsoup4',
        'dotenv'
    ]
    
    缺失包 = []
    
    for 包名 in 必要包:
        try:
            模块名 = 包名.replace('.', '_') if '.' in 包名 else 包名
            if 包名 == 'google.generativeai':
                import google.generativeai
            elif 包名 == 'beautifulsoup4':
                import bs4
            elif 包名 == 'dotenv':
                import dotenv
            else:
                __import__(模块名)
            print(f"✅ {包名}")
        except ImportError:
            print(f"❌ {包名} - 缺失")
            缺失包.append(包名)
    
    if 缺失包:
        print(f"\n⚠️ 发现缺失依赖包: {', '.join(缺失包)}")
        print("请运行以下命令安装:")
        安装命令 = "pip install " + " ".join(缺失包)
        print(安装命令)
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def 检查环境变量():
    """检查环境变量配置"""
    print("\n🔑 检查环境变量...")

    try:
        检查结果 = 检查必需环境变量()

        if 检查结果['成功']:
            print("✅ 所有必需环境变量已正确设置")
            for 变量名 in 检查结果['已设置变量']:
                详情 = 检查结果['详细信息'][变量名]
                print(f"  ✅ {变量名}: {详情['值']}")
            return True
        else:
            print("❌ 发现缺失的必需环境变量")
            for 变量名 in 检查结果['缺失变量']:
                print(f"  ❌ {变量名}: 未设置")

            print("\n💡 请设置环境变量后重试")
            设置环境变量提示()
            return False

    except Exception as e:
        print(f"❌ 检查环境变量失败: {e}")
        return False


def 显示系统信息():
    """显示系统信息"""
    print("\n" + "="*60)
    print("🤖 智能学习系统")
    print("="*60)
    print("📋 系统功能:")
    print("  🧠 自动套利类型发现")
    print("  🔍 智能信息获取分析")
    print("  📖 自动文章采集")
    print("  📚 知识库管理")
    print("  📊 学习报告生成")
    print()
    print("💡 系统特点:")
    print("  ✅ 完全自动化运行")
    print("  ✅ 智能学习优化")
    print("  ✅ 多渠道信息获取")
    print("  ✅ AI智能分析")
    print("  ✅ 统一配置管理")
    print()
    print("📁 目录结构:")
    print("  📂 核心程序/ - 主要程序文件")
    print("  📂 知识库/ - 学习数据和状态")
    print("  📂 学习日志/ - 运行日志")
    print("  📂 配置文件/ - 系统配置")
    print("="*60)


def 启动简化系统():
    """启动简化全自动系统"""
    print("\n🚀 启动简化全自动学习系统...")
    
    try:
        from 简化全自动系统 import 简化全自动套利系统
        
        系统 = 简化全自动套利系统()
        
        print("✅ 系统初始化成功")
        print("🔄 开始自动学习...")
        print("💡 按 Ctrl+C 可停止系统")
        print()
        
        # 启动自动学习
        系统.启动自动学习()
        
    except ImportError as e:
        print(f"❌ 导入简化系统失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()


def 启动完整系统():
    """启动完整智能学习系统"""
    print("\n🚀 启动完整智能学习系统...")
    
    try:
        from 全自动智能套利系统 import 全自动智能套利系统
        
        系统 = 全自动智能套利系统()
        
        print("✅ 系统初始化成功")
        print("🔄 开始自动学习...")
        print("💡 按 Ctrl+C 可停止系统")
        print()
        
        # 启动自动学习
        系统.启动自动学习()
        
    except ImportError as e:
        print(f"❌ 导入完整系统失败: {e}")
        print("💡 建议使用简化系统")
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()


def 检测学习质量():
    """检测和评估学习质量"""
    print("\n📊 学习质量检测系统")
    print("=" * 60)

    try:
        import json
        import os
        from datetime import datetime, timedelta

        # 加载系统状态和知识库
        知识库路径 = os.path.join(当前目录, '知识库')
        状态文件 = os.path.join(知识库路径, '简化系统状态.json')

        if not os.path.exists(状态文件):
            print("❌ 未找到系统状态文件，请先运行学习系统")
            return

        with open(状态文件, 'r', encoding='utf-8') as f:
            系统状态 = json.load(f)

        # 1. 关键词质量分析
        print("\n🔑 关键词质量分析:")
        关键词池 = 系统状态.get('动态关键词池', [])
        套利知识库 = 系统状态.get('套利知识库', {})

        print(f"  📊 关键词池大小: {len(关键词池)}")

        # 分析关键词类型分布
        套利相关词 = [词 for 词 in 关键词池 if '套利' in 词]
        策略相关词 = [词 for 词 in 关键词池 if '策略' in 词 or '方法' in 词]
        风险相关词 = [词 for 词 in 关键词池 if '风险' in 词 or '分析' in 词]

        print(f"  🎯 套利相关词汇: {len(套利相关词)} 个 ({len(套利相关词)/len(关键词池)*100:.1f}%)")
        print(f"  📈 策略相关词汇: {len(策略相关词)} 个 ({len(策略相关词)/len(关键词池)*100:.1f}%)")
        print(f"  ⚠️  风险相关词汇: {len(风险相关词)} 个 ({len(风险相关词)/len(关键词池)*100:.1f}%)")

        # 显示高质量关键词示例
        print(f"\n  💎 高质量关键词示例:")
        高质量词汇 = [词 for 词 in 关键词池 if len(词) > 3 and ('套利' in 词 or '投资' in 词 or '交易' in 词)][:5]
        for i, 词 in enumerate(高质量词汇, 1):
            print(f"    {i}. {词}")

        # 2. 学习历史分析
        print(f"\n📈 学习历史分析:")
        学习历史 = 系统状态.get('学习历史', [])
        print(f"  📊 总学习记录: {len(学习历史)} 条")

        if 学习历史:
            # 按类型统计
            类型统计 = {}
            总学习量 = 0

            for 记录 in 学习历史:
                类型 = 记录.get('类型', '未知')
                if 类型 not in 类型统计:
                    类型统计[类型] = {'次数': 0, '总量': 0}
                类型统计[类型]['次数'] += 1

                # 统计学习量
                for key in ['学习数量', '收集数量', '新关键词数量']:
                    if key in 记录:
                        类型统计[类型]['总量'] += 记录[key]
                        总学习量 += 记录[key]

            print(f"  📚 总学习量: {总学习量} 个概念/信息")

            for 类型, 统计 in 类型统计.items():
                print(f"  📋 {类型}: {统计['次数']} 次，共 {统计['总量']} 项")

            # 最近学习活跃度
            现在 = datetime.now()
            最近24小时 = [记录 for 记录 in 学习历史
                        if (现在 - datetime.fromisoformat(记录['时间'])).total_seconds() < 86400]

            print(f"  🕐 最近24小时活动: {len(最近24小时)} 次学习")

        # 3. 知识库质量分析
        print(f"\n🧠 知识库质量分析:")
        print(f"  📊 知识条目数量: {len(套利知识库)}")

        if 套利知识库:
            # 分析学习深度
            多次学习词汇 = [词 for 词, 信息 in 套利知识库.items()
                          if 信息.get('学习次数', 0) > 1]

            print(f"  🔄 深度学习词汇: {len(多次学习词汇)} 个")
            print(f"  📈 平均学习深度: {sum(信息.get('学习次数', 1) for 信息 in 套利知识库.values()) / len(套利知识库):.1f} 次")

            # 显示学习最多的词汇
            学习次数排序 = sorted(套利知识库.items(),
                               key=lambda x: x[1].get('学习次数', 0),
                               reverse=True)[:5]

            print(f"\n  🏆 学习最深入的词汇:")
            for i, (词, 信息) in enumerate(学习次数排序, 1):
                次数 = 信息.get('学习次数', 1)
                相关词数 = len(信息.get('相关词汇', []))
                print(f"    {i}. {词} - 学习{次数}次，关联{相关词数}个词汇")

        # 4. 学习效率评估
        print(f"\n⚡ 学习效率评估:")

        if 学习历史:
            # 计算学习效率指标
            最近记录 = 学习历史[-10:] if len(学习历史) >= 10 else 学习历史

            基础学习记录 = [r for r in 最近记录 if r.get('类型') == '基础学习']
            信息收集记录 = [r for r in 最近记录 if r.get('类型') == '信息收集']

            if 基础学习记录:
                平均学习量 = sum(r.get('学习数量', 0) for r in 基础学习记录) / len(基础学习记录)
                平均新词量 = sum(r.get('新关键词数量', 0) for r in 基础学习记录) / len(基础学习记录)
                print(f"  📚 平均每次基础学习: {平均学习量:.1f} 个概念")
                print(f"  🆕 平均每次新增词汇: {平均新词量:.1f} 个")

            if 信息收集记录:
                平均收集量 = sum(r.get('收集数量', 0) for r in 信息收集记录) / len(信息收集记录)
                print(f"  🔍 平均每次信息收集: {平均收集量:.1f} 条")

        # 5. 质量评分
        print(f"\n🏅 综合质量评分:")

        质量分数 = 0
        最大分数 = 100

        # 关键词池大小评分 (30分)
        关键词分数 = min(len(关键词池) / 50 * 30, 30)
        质量分数 += 关键词分数
        print(f"  🔑 关键词丰富度: {关键词分数:.1f}/30 分")

        # 学习活跃度评分 (25分)
        活跃度分数 = min(len(学习历史) / 20 * 25, 25)
        质量分数 += 活跃度分数
        print(f"  📈 学习活跃度: {活跃度分数:.1f}/25 分")

        # 知识深度评分 (25分)
        if 套利知识库:
            平均深度 = sum(信息.get('学习次数', 1) for 信息 in 套利知识库.values()) / len(套利知识库)
            深度分数 = min(平均深度 / 3 * 25, 25)
        else:
            深度分数 = 0
        质量分数 += 深度分数
        print(f"  🧠 知识深度: {深度分数:.1f}/25 分")

        # 词汇质量评分 (20分)
        if 关键词池:
            质量词汇比例 = len(高质量词汇) / len(关键词池)
            词汇质量分数 = 质量词汇比例 * 20
        else:
            词汇质量分数 = 0
        质量分数 += 词汇质量分数
        print(f"  💎 词汇质量: {词汇质量分数:.1f}/20 分")

        print(f"\n🎯 总体质量评分: {质量分数:.1f}/{最大分数} 分")

        # 质量等级
        if 质量分数 >= 80:
            等级 = "🏆 优秀"
            建议 = "学习质量很高，继续保持！"
        elif 质量分数 >= 60:
            等级 = "🥈 良好"
            建议 = "学习质量不错，可以适当增加学习频率"
        elif 质量分数 >= 40:
            等级 = "🥉 一般"
            建议 = "建议增加学习时间，提高学习深度"
        else:
            等级 = "📈 待提升"
            建议 = "建议重新启动学习系统，增加学习活动"

        print(f"🏅 质量等级: {等级}")
        print(f"💡 改进建议: {建议}")

        # 6. 详细建议
        print(f"\n📋 详细改进建议:")

        if len(关键词池) < 30:
            print("  🔑 建议增加基础学习频率，扩展关键词池")

        if len(学习历史) < 10:
            print("  📈 建议让系统运行更长时间，积累更多学习经验")

        if 套利知识库 and sum(信息.get('学习次数', 1) for 信息 in 套利知识库.values()) / len(套利知识库) < 2:
            print("  🧠 建议增加重复学习，加深知识理解")

        if len(高质量词汇) / len(关键词池) < 0.3:
            print("  💎 建议优化关键词生成策略，提高词汇质量")

    except Exception as e:
        print(f"❌ 质量检测失败: {e}")
        import traceback
        traceback.print_exc()


def 测试单个模块():
    """测试单个模块功能"""
    print("\n🧪 测试单个模块功能...")

    while True:
        print(f"\n📋 请选择测试模块:")
        print("1. 测试套利发现系统")
        print("2. 测试信息获取系统")
        print("3. 测试简化学习系统")
        print("4. 🔍 检测学习质量")
        print("0. 返回主菜单")

        选择 = input("\n请输入选项 (0-4): ").strip()

        if 选择 == "0":
            break
        elif 选择 == "1":
            try:
                from 智能套利发现系统 import 智能套利发现系统
                系统 = 智能套利发现系统()
                套利类型 = 系统.智能发现套利类型("套利")
                print(f"✅ 发现 {len(套利类型)} 种套利类型")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        elif 选择 == "2":
            try:
                from 智能信息获取系统 import 智能信息获取系统
                系统 = 智能信息获取系统()
                关键词 = 系统.智能生成搜索关键词("套利")
                print(f"✅ 生成 {len(关键词)} 个搜索关键词")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        elif 选择 == "3":
            try:
                from 简化全自动系统 import 简化全自动套利系统
                系统 = 简化全自动套利系统()
                系统.基础学习任务()
                print("✅ 简化学习测试完成")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        elif 选择 == "4":
            检测学习质量()
        else:
            print("❌ 无效选项，请重新选择")


def 主函数():
    """主函数"""
    print("🚀 智能学习系统启动器")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 系统检查
    if not 检查依赖():
        print("\n❌ 依赖检查失败，请安装缺失的包后重试")
        return
    
    if not 检查环境变量():
        print("\n❌ 环境变量检查失败，请设置GEMINI_API_KEY后重试")
        return
    
    # 创建必要目录
    print("\n📂 创建必要目录...")
    创建必要目录()
    
    # 显示系统信息
    显示系统信息()
    
    # 显示配置信息
    print("\n📋 显示配置信息...")
    显示配置信息()
    
    # 主菜单
    while True:
        print(f"\n📋 请选择操作:")
        print("1. 🚀 启动简化自动学习系统 (推荐)")
        print("2. 🤖 启动完整智能学习系统")
        print("3. 🧪 测试单个模块")
        print("4. 📊 显示配置信息")
        print("0. 退出")
        
        选择 = input("\n请输入选项 (0-4): ").strip()
        
        if 选择 == "0":
            print("👋 程序退出")
            break
        elif 选择 == "1":
            启动简化系统()
        elif 选择 == "2":
            启动完整系统()
        elif 选择 == "3":
            测试单个模块()
        elif 选择 == "4":
            显示配置信息()
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
