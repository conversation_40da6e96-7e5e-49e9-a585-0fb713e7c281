#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习质量分析器

作者: AI助手
日期: 2025-07-27
功能: 专门分析和评估智能学习系统的学习质量
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加配置文件路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(当前目录)
sys.path.append(os.path.join(根目录, '配置文件'))

from 智能学习配置 import 获取文件路径


class 学习质量分析器:
    """学习质量分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.知识库路径 = os.path.join(当前目录, '知识库')
        self.系统状态 = None
        self.学习历史 = []
        self.套利知识库 = {}
        self.关键词池 = []
        
        self._加载数据()
    
    def _加载数据(self):
        """加载学习数据"""
        try:
            状态文件 = os.path.join(self.知识库路径, '简化系统状态.json')
            
            if os.path.exists(状态文件):
                with open(状态文件, 'r', encoding='utf-8') as f:
                    self.系统状态 = json.load(f)
                    self.学习历史 = self.系统状态.get('学习历史', [])
                    self.套利知识库 = self.系统状态.get('套利知识库', {})
                    self.关键词池 = self.系统状态.get('动态关键词池', [])
                print("✅ 学习数据加载成功")
            else:
                print("❌ 未找到系统状态文件")
                
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
    
    def 分析关键词质量(self) -> Dict[str, Any]:
        """分析关键词质量"""
        print("\n🔑 关键词质量分析")
        print("-" * 40)
        
        if not self.关键词池:
            return {'评分': 0, '建议': '无关键词数据'}
        
        分析结果 = {
            '总数量': len(self.关键词池),
            '套利相关': 0,
            '策略相关': 0,
            '风险相关': 0,
            '高质量词汇': [],
            '低质量词汇': [],
            '评分': 0
        }
        
        # 分类统计
        for 词 in self.关键词池:
            if '套利' in 词:
                分析结果['套利相关'] += 1
            if '策略' in 词 or '方法' in 词:
                分析结果['策略相关'] += 1
            if '风险' in 词 or '分析' in 词:
                分析结果['风险相关'] += 1
            
            # 质量判断
            if len(词) >= 4 and any(关键字 in 词 for 关键字 in ['套利', '投资', '交易', '策略', '分析']):
                分析结果['高质量词汇'].append(词)
            elif len(词) <= 2 or 词.endswith('策略') or 词.endswith('机会'):
                分析结果['低质量词汇'].append(词)
        
        # 计算评分
        质量比例 = len(分析结果['高质量词汇']) / len(self.关键词池)
        数量分数 = min(len(self.关键词池) / 50, 1) * 50
        质量分数 = 质量比例 * 50
        分析结果['评分'] = 数量分数 + 质量分数
        
        # 显示结果
        print(f"📊 关键词总数: {分析结果['总数量']}")
        print(f"🎯 套利相关: {分析结果['套利相关']} ({分析结果['套利相关']/分析结果['总数量']*100:.1f}%)")
        print(f"📈 策略相关: {分析结果['策略相关']} ({分析结果['策略相关']/分析结果['总数量']*100:.1f}%)")
        print(f"⚠️  风险相关: {分析结果['风险相关']} ({分析结果['风险相关']/分析结果['总数量']*100:.1f}%)")
        print(f"💎 高质量词汇: {len(分析结果['高质量词汇'])} ({质量比例*100:.1f}%)")
        print(f"🏅 关键词质量评分: {分析结果['评分']:.1f}/100")
        
        # 显示示例
        if 分析结果['高质量词汇']:
            print(f"\n💎 高质量词汇示例:")
            for i, 词 in enumerate(分析结果['高质量词汇'][:5], 1):
                print(f"  {i}. {词}")
        
        if 分析结果['低质量词汇']:
            print(f"\n⚠️  待优化词汇:")
            for i, 词 in enumerate(分析结果['低质量词汇'][:3], 1):
                print(f"  {i}. {词}")
        
        return 分析结果
    
    def 分析学习活跃度(self) -> Dict[str, Any]:
        """分析学习活跃度"""
        print("\n📈 学习活跃度分析")
        print("-" * 40)
        
        if not self.学习历史:
            return {'评分': 0, '建议': '无学习历史数据'}
        
        分析结果 = {
            '总记录数': len(self.学习历史),
            '类型统计': {},
            '最近24小时': 0,
            '最近7天': 0,
            '平均学习量': 0,
            '评分': 0
        }
        
        现在 = datetime.now()
        总学习量 = 0
        
        # 统计分析
        for 记录 in self.学习历史:
            # 类型统计
            类型 = 记录.get('类型', '未知')
            if 类型 not in 分析结果['类型统计']:
                分析结果['类型统计'][类型] = {'次数': 0, '总量': 0}
            分析结果['类型统计'][类型]['次数'] += 1
            
            # 学习量统计
            for key in ['学习数量', '收集数量', '新关键词数量']:
                if key in 记录:
                    量 = 记录[key]
                    分析结果['类型统计'][类型]['总量'] += 量
                    总学习量 += 量
            
            # 时间分析
            try:
                记录时间 = datetime.fromisoformat(记录['时间'])
                时间差 = (现在 - 记录时间).total_seconds()
                
                if 时间差 < 86400:  # 24小时
                    分析结果['最近24小时'] += 1
                if 时间差 < 604800:  # 7天
                    分析结果['最近7天'] += 1
            except:
                pass
        
        分析结果['平均学习量'] = 总学习量 / len(self.学习历史) if self.学习历史 else 0
        
        # 计算评分
        记录分数 = min(len(self.学习历史) / 20, 1) * 40
        活跃分数 = min(分析结果['最近24小时'] / 5, 1) * 30
        学习量分数 = min(分析结果['平均学习量'] / 10, 1) * 30
        分析结果['评分'] = 记录分数 + 活跃分数 + 学习量分数
        
        # 显示结果
        print(f"📊 总学习记录: {分析结果['总记录数']} 条")
        print(f"🕐 最近24小时: {分析结果['最近24小时']} 次学习")
        print(f"📅 最近7天: {分析结果['最近7天']} 次学习")
        print(f"📚 平均学习量: {分析结果['平均学习量']:.1f} 项/次")
        print(f"🏅 学习活跃度评分: {分析结果['评分']:.1f}/100")
        
        print(f"\n📋 学习类型分布:")
        for 类型, 统计 in 分析结果['类型统计'].items():
            print(f"  {类型}: {统计['次数']} 次，共 {统计['总量']} 项")
        
        return 分析结果
    
    def 分析知识深度(self) -> Dict[str, Any]:
        """分析知识深度"""
        print("\n🧠 知识深度分析")
        print("-" * 40)
        
        if not self.套利知识库:
            return {'评分': 0, '建议': '无知识库数据'}
        
        分析结果 = {
            '知识条目数': len(self.套利知识库),
            '平均学习次数': 0,
            '深度学习词汇': 0,
            '最深学习词汇': [],
            '评分': 0
        }
        
        学习次数列表 = []
        
        for 词, 信息 in self.套利知识库.items():
            学习次数 = 信息.get('学习次数', 1)
            学习次数列表.append(学习次数)
            
            if 学习次数 > 1:
                分析结果['深度学习词汇'] += 1
        
        if 学习次数列表:
            分析结果['平均学习次数'] = sum(学习次数列表) / len(学习次数列表)
        
        # 找出学习最深入的词汇
        学习次数排序 = sorted(self.套利知识库.items(), 
                           key=lambda x: x[1].get('学习次数', 0), 
                           reverse=True)
        分析结果['最深学习词汇'] = 学习次数排序[:5]
        
        # 计算评分
        数量分数 = min(len(self.套利知识库) / 30, 1) * 40
        深度分数 = min(分析结果['平均学习次数'] / 3, 1) * 40
        覆盖分数 = min(分析结果['深度学习词汇'] / len(self.套利知识库), 1) * 20
        分析结果['评分'] = 数量分数 + 深度分数 + 覆盖分数
        
        # 显示结果
        print(f"📊 知识条目数: {分析结果['知识条目数']}")
        print(f"🔄 深度学习词汇: {分析结果['深度学习词汇']} ({分析结果['深度学习词汇']/分析结果['知识条目数']*100:.1f}%)")
        print(f"📈 平均学习次数: {分析结果['平均学习次数']:.1f}")
        print(f"🏅 知识深度评分: {分析结果['评分']:.1f}/100")
        
        if 分析结果['最深学习词汇']:
            print(f"\n🏆 学习最深入的词汇:")
            for i, (词, 信息) in enumerate(分析结果['最深学习词汇'], 1):
                次数 = 信息.get('学习次数', 1)
                相关词数 = len(信息.get('相关词汇', []))
                print(f"  {i}. {词} - 学习{次数}次，关联{相关词数}个词汇")
        
        return 分析结果
    
    def 生成综合评估报告(self):
        """生成综合评估报告"""
        print("\n" + "="*60)
        print("📊 智能学习系统质量综合评估报告")
        print("="*60)
        print(f"📅 评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if not self.系统状态:
            print("❌ 无法生成报告：缺少系统数据")
            return
        
        # 执行各项分析
        关键词分析 = self.分析关键词质量()
        活跃度分析 = self.分析学习活跃度()
        深度分析 = self.分析知识深度()
        
        # 计算综合评分
        综合评分 = (关键词分析['评分'] * 0.3 + 
                   活跃度分析['评分'] * 0.4 + 
                   深度分析['评分'] * 0.3)
        
        print(f"\n🎯 综合质量评分")
        print("-" * 40)
        print(f"🔑 关键词质量: {关键词分析['评分']:.1f}/100 (权重30%)")
        print(f"📈 学习活跃度: {活跃度分析['评分']:.1f}/100 (权重40%)")
        print(f"🧠 知识深度: {深度分析['评分']:.1f}/100 (权重30%)")
        print(f"🏅 综合评分: {综合评分:.1f}/100")
        
        # 质量等级评定
        if 综合评分 >= 80:
            等级 = "🏆 优秀"
            等级描述 = "学习质量很高，系统运行良好"
        elif 综合评分 >= 60:
            等级 = "🥈 良好"
            等级描述 = "学习质量不错，有进一步提升空间"
        elif 综合评分 >= 40:
            等级 = "🥉 一般"
            等级描述 = "学习质量一般，需要优化学习策略"
        else:
            等级 = "📈 待提升"
            等级描述 = "学习质量较低，建议重新配置系统"
        
        print(f"\n🏅 质量等级: {等级}")
        print(f"📝 等级描述: {等级描述}")
        
        # 改进建议
        print(f"\n💡 改进建议")
        print("-" * 40)
        
        建议列表 = []
        
        if 关键词分析['评分'] < 60:
            建议列表.append("🔑 增加基础学习频率，扩展关键词池")
        
        if 活跃度分析['评分'] < 60:
            建议列表.append("📈 增加学习活动频率，保持系统活跃")
        
        if 深度分析['评分'] < 60:
            建议列表.append("🧠 增加重复学习，提高知识掌握深度")
        
        if len(self.关键词池) < 30:
            建议列表.append("🎯 关键词池规模较小，建议长期运行系统")
        
        if 活跃度分析['最近24小时'] == 0:
            建议列表.append("⚠️  系统最近无学习活动，请检查运行状态")
        
        if not 建议列表:
            建议列表.append("✅ 系统运行良好，继续保持当前学习策略")
        
        for i, 建议 in enumerate(建议列表, 1):
            print(f"  {i}. {建议}")
        
        # 保存报告
        try:
            报告数据 = {
                '评估时间': datetime.now().isoformat(),
                '关键词分析': 关键词分析,
                '活跃度分析': 活跃度分析,
                '深度分析': 深度分析,
                '综合评分': 综合评分,
                '质量等级': 等级,
                '改进建议': 建议列表
            }
            
            报告文件 = os.path.join(self.知识库路径, f"质量评估报告_{datetime.now().strftime('%Y%m%d_%H%M')}.json")
            with open(报告文件, 'w', encoding='utf-8') as f:
                json.dump(报告数据, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 评估报告已保存: {os.path.basename(报告文件)}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")


def 主函数():
    """主函数"""
    print("📊 智能学习系统质量分析器")
    print("=" * 60)
    
    分析器 = 学习质量分析器()
    
    if not 分析器.系统状态:
        print("❌ 无法加载学习数据，请先运行智能学习系统")
        return
    
    while True:
        print(f"\n📋 请选择分析功能:")
        print("1. 🔑 关键词质量分析")
        print("2. 📈 学习活跃度分析")
        print("3. 🧠 知识深度分析")
        print("4. 📊 综合评估报告")
        print("0. 退出")
        
        选择 = input("\n请输入选项 (0-4): ").strip()
        
        if 选择 == "0":
            print("👋 分析器退出")
            break
        elif 选择 == "1":
            分析器.分析关键词质量()
        elif 选择 == "2":
            分析器.分析学习活跃度()
        elif 选择 == "3":
            分析器.分析知识深度()
        elif 选择 == "4":
            分析器.生成综合评估报告()
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
