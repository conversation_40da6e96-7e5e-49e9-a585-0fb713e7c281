#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动智能套利学习系统

作者: AI助手
日期: 2025-07-27
功能: 完全自动化的套利信息学习和更新系统，无需人工干预
"""

import os
import json
import time
import schedule
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Set
import google.generativeai as genai
from dotenv import load_dotenv
import logging

# 导入其他模块
智能套利发现系统 = None
智能信息获取系统 = None
简化文章采集器 = None

try:
    from 智能套利发现系统 import 智能套利发现系统
    print("✅ 智能套利发现系统导入成功")
except ImportError as e:
    print(f"⚠️ 智能套利发现系统导入失败: {e}")

try:
    from 智能信息获取系统 import 智能信息获取系统
    print("✅ 智能信息获取系统导入成功")
except ImportError as e:
    print(f"⚠️ 智能信息获取系统导入失败: {e}")

try:
    from 简化文章采集器 import 简化文章采集器
    print("✅ 简化文章采集器导入成功")
except ImportError as e:
    print(f"⚠️ 简化文章采集器导入失败: {e}")


class 全自动智能套利系统:
    """全自动智能套利学习系统"""
    
    def __init__(self):
        """初始化系统"""
        self.运行状态 = False
        self.学习周期 = {
            '套利发现': 24,    # 24小时运行一次
            '信息获取': 4,     # 4小时运行一次
            '文章采集': 2,     # 2小时运行一次
            '知识整理': 12     # 12小时运行一次
        }
        
        self.基础关键词池 = [
            "套利", "价差套利", "跨市场套利", "可转债套利", "分红套利",
            "期现套利", "统计套利", "配对交易", "ETF套利", "REIT套利",
            "债券套利", "汇率套利", "利率套利", "商品套利"
        ]
        
        self.动态关键词池 = set(self.基础关键词池)
        self.学习历史 = []
        self.系统状态 = {}
        
        # 初始化子系统
        self.套利发现系统 = None
        self.信息获取系统 = None
        self.文章采集器 = None
        
        self._初始化子系统()
        self._设置日志()
        self._加载系统状态()
    
    def _初始化子系统(self):
        """初始化各个子系统"""
        try:
            if 智能套利发现系统:
                self.套利发现系统 = 智能套利发现系统()
                print("✅ 套利发现系统初始化成功")
            else:
                print("⚠️ 套利发现系统不可用")

            if 智能信息获取系统:
                self.信息获取系统 = 智能信息获取系统()
                print("✅ 信息获取系统初始化成功")
            else:
                print("⚠️ 信息获取系统不可用")

            if 简化文章采集器:
                self.文章采集器 = 简化文章采集器()
                print("✅ 文章采集器初始化成功")
            else:
                print("⚠️ 文章采集器不可用")

        except Exception as e:
            print(f"❌ 子系统初始化失败: {e}")
    
    def _设置日志(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('全自动套利系统.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _加载系统状态(self):
        """加载系统状态"""
        状态文件 = "系统状态.json"
        try:
            if os.path.exists(状态文件):
                with open(状态文件, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.系统状态 = data.get('系统状态', {})
                    self.动态关键词池.update(data.get('动态关键词池', []))
                    self.学习历史 = data.get('学习历史', [])
                self.logger.info("系统状态加载成功")
        except Exception as e:
            self.logger.error(f"加载系统状态失败: {e}")
    
    def _保存系统状态(self):
        """保存系统状态"""
        状态文件 = "系统状态.json"
        try:
            data = {
                '系统状态': self.系统状态,
                '动态关键词池': list(self.动态关键词池),
                '学习历史': self.学习历史,
                '最后更新': datetime.now().isoformat()
            }
            with open(状态文件, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info("系统状态保存成功")
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {e}")
    
    def 自动套利发现(self):
        """自动套利类型发现"""
        self.logger.info("🧠 开始自动套利发现")
        
        try:
            if not self.套利发现系统:
                self.logger.error("套利发现系统未初始化")
                return
            
            发现数量 = 0
            新关键词 = set()
            
            # 对每个基础关键词进行发现
            for 关键词 in list(self.动态关键词池)[:10]:  # 限制数量避免过多请求
                try:
                    self.logger.info(f"正在发现关键词: {关键词}")
                    套利类型列表 = self.套利发现系统.智能发现套利类型(关键词)
                    
                    if 套利类型列表:
                        发现数量 += len(套利类型列表)
                        
                        # 为每种套利类型生成新的搜索关键词
                        for 套利类型 in 套利类型列表:
                            关键词列表 = self.套利发现系统.生成搜索关键词(套利类型)
                            新关键词.update(关键词列表)
                    
                    time.sleep(2)  # 避免请求过快
                    
                except Exception as e:
                    self.logger.error(f"发现关键词 {关键词} 失败: {e}")
                    continue
            
            # 更新动态关键词池
            self.动态关键词池.update(新关键词)
            
            # 记录学习历史
            学习记录 = {
                '时间': datetime.now().isoformat(),
                '类型': '套利发现',
                '发现数量': 发现数量,
                '新关键词数量': len(新关键词),
                '关键词池大小': len(self.动态关键词池)
            }
            self.学习历史.append(学习记录)
            
            self.logger.info(f"✅ 套利发现完成: 发现 {发现数量} 种套利类型，新增 {len(新关键词)} 个关键词")
            
        except Exception as e:
            self.logger.error(f"自动套利发现失败: {e}")
    
    def 自动信息获取(self):
        """自动信息获取和分析"""
        self.logger.info("🔍 开始自动信息获取")
        
        try:
            if not self.信息获取系统:
                self.logger.error("信息获取系统未初始化")
                return
            
            获取数量 = 0
            分析结果列表 = []
            
            # 从动态关键词池中选择关键词
            选择关键词 = list(self.动态关键词池)[:8]  # 每次选择8个关键词
            
            for 关键词 in 选择关键词:
                try:
                    self.logger.info(f"正在获取信息: {关键词}")
                    结果 = self.信息获取系统.智能信息获取流程(关键词)
                    
                    if 结果 and 结果.get('获取信息数量', 0) > 0:
                        获取数量 += 结果['获取信息数量']
                        
                        # 保存结果
                        结果文件 = f"自动获取_{关键词}_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
                        with open(结果文件, 'w', encoding='utf-8') as f:
                            json.dump(结果, f, ensure_ascii=False, indent=2)
                        
                        # 收集分析结果
                        if 'AI分析结果' in 结果:
                            分析结果列表.append({
                                '关键词': 关键词,
                                '分析结果': 结果['AI分析结果']
                            })
                    
                    time.sleep(3)  # 避免请求过快
                    
                except Exception as e:
                    self.logger.error(f"获取信息 {关键词} 失败: {e}")
                    continue
            
            # 记录学习历史
            学习记录 = {
                '时间': datetime.now().isoformat(),
                '类型': '信息获取',
                '获取数量': 获取数量,
                '分析数量': len(分析结果列表),
                '关键词': 选择关键词
            }
            self.学习历史.append(学习记录)
            
            self.logger.info(f"✅ 信息获取完成: 获取 {获取数量} 条信息，分析 {len(分析结果列表)} 个结果")
            
        except Exception as e:
            self.logger.error(f"自动信息获取失败: {e}")
    
    def 自动文章采集(self):
        """自动文章采集"""
        self.logger.info("📖 开始自动文章采集")
        
        try:
            if not self.文章采集器:
                self.logger.error("文章采集器未初始化")
                return
            
            # 从配置文件获取示例链接
            try:
                import sys
                配置文件路径 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "配置文件")
                sys.path.append(配置文件路径)
                from 公众号配置 import 获取示例链接
                
                示例链接列表 = 获取示例链接()
                采集数量 = 0
                
                for 链接信息 in 示例链接列表:
                    try:
                        文章数据 = self.文章采集器.采集文章(
                            链接信息['链接'], 
                            链接信息['来源账号']
                        )
                        
                        if 文章数据:
                            文件路径 = self.文章采集器.保存文章到文件(文章数据)
                            if 文件路径:
                                采集数量 += 1
                                self.logger.info(f"采集文章成功: {文章数据['标题']}")
                        
                        time.sleep(2)  # 避免请求过快
                        
                    except Exception as e:
                        self.logger.error(f"采集文章失败: {e}")
                        continue
                
                # 记录学习历史
                学习记录 = {
                    '时间': datetime.now().isoformat(),
                    '类型': '文章采集',
                    '采集数量': 采集数量,
                    '总链接数': len(示例链接列表)
                }
                self.学习历史.append(学习记录)
                
                self.logger.info(f"✅ 文章采集完成: 采集 {采集数量} 篇文章")
                
            except ImportError:
                self.logger.error("无法导入公众号配置")
                
        except Exception as e:
            self.logger.error(f"自动文章采集失败: {e}")
    
    def 自动知识整理(self):
        """自动知识整理和优化"""
        self.logger.info("📚 开始自动知识整理")
        
        try:
            # 清理过期的学习历史（保留最近30天）
            cutoff_date = datetime.now() - timedelta(days=30)
            原始数量 = len(self.学习历史)
            
            self.学习历史 = [
                记录 for 记录 in self.学习历史
                if datetime.fromisoformat(记录['时间']) > cutoff_date
            ]
            
            清理数量 = 原始数量 - len(self.学习历史)
            
            # 优化关键词池（移除低效关键词）
            if len(self.动态关键词池) > 100:
                # 保留基础关键词和最近使用的关键词
                保留关键词 = set(self.基础关键词池)
                
                # 从最近的学习历史中提取有效关键词
                for 记录 in self.学习历史[-50:]:  # 最近50条记录
                    if '关键词' in 记录:
                        if isinstance(记录['关键词'], list):
                            保留关键词.update(记录['关键词'])
                        else:
                            保留关键词.add(记录['关键词'])
                
                原始关键词数 = len(self.动态关键词池)
                self.动态关键词池 = 保留关键词
                优化数量 = 原始关键词数 - len(self.动态关键词池)
                
                self.logger.info(f"关键词池优化: 从 {原始关键词数} 优化到 {len(self.动态关键词池)} 个")
            
            # 生成学习报告
            self._生成学习报告()
            
            self.logger.info(f"✅ 知识整理完成: 清理历史 {清理数量} 条")
            
        except Exception as e:
            self.logger.error(f"自动知识整理失败: {e}")
    
    def _生成学习报告(self):
        """生成学习报告"""
        try:
            报告数据 = {
                '生成时间': datetime.now().isoformat(),
                '系统运行状态': self.运行状态,
                '关键词池大小': len(self.动态关键词池),
                '学习历史数量': len(self.学习历史),
                '最近24小时活动': []
            }
            
            # 统计最近24小时的活动
            cutoff_time = datetime.now() - timedelta(hours=24)
            最近活动 = [
                记录 for 记录 in self.学习历史
                if datetime.fromisoformat(记录['时间']) > cutoff_time
            ]
            
            活动统计 = {}
            for 记录 in 最近活动:
                类型 = 记录['类型']
                if 类型 not in 活动统计:
                    活动统计[类型] = {'次数': 0, '总量': 0}
                活动统计[类型]['次数'] += 1
                
                # 统计具体数量
                if '发现数量' in 记录:
                    活动统计[类型]['总量'] += 记录['发现数量']
                elif '获取数量' in 记录:
                    活动统计[类型]['总量'] += 记录['获取数量']
                elif '采集数量' in 记录:
                    活动统计[类型]['总量'] += 记录['采集数量']
            
            报告数据['最近24小时活动'] = 活动统计
            
            # 保存报告
            报告文件 = f"学习报告_{datetime.now().strftime('%Y%m%d')}.json"
            with open(报告文件, 'w', encoding='utf-8') as f:
                json.dump(报告数据, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"学习报告已生成: {报告文件}")
            
        except Exception as e:
            self.logger.error(f"生成学习报告失败: {e}")
    
    def 启动自动学习(self):
        """启动自动学习系统"""
        self.logger.info("🚀 启动全自动智能套利学习系统")
        
        # 设置定时任务
        schedule.every(self.学习周期['套利发现']).hours.do(self.自动套利发现)
        schedule.every(self.学习周期['信息获取']).hours.do(self.自动信息获取)
        schedule.every(self.学习周期['文章采集']).hours.do(self.自动文章采集)
        schedule.every(self.学习周期['知识整理']).hours.do(self.自动知识整理)
        
        # 立即执行一次初始化学习
        self.logger.info("执行初始化学习...")
        self.自动套利发现()
        time.sleep(60)  # 等待1分钟
        self.自动信息获取()
        time.sleep(60)  # 等待1分钟
        self.自动文章采集()
        
        self.运行状态 = True
        self._保存系统状态()
        
        self.logger.info("✅ 系统启动完成，进入自动学习模式")
        
        # 主循环
        try:
            while self.运行状态:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
                # 每小时保存一次状态
                if datetime.now().minute == 0:
                    self._保存系统状态()
                    
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭系统...")
            self.停止自动学习()
    
    def 停止自动学习(self):
        """停止自动学习系统"""
        self.运行状态 = False
        self._保存系统状态()
        self.logger.info("🛑 全自动智能套利学习系统已停止")
    
    def 显示系统状态(self):
        """显示系统状态"""
        print(f"\n📊 全自动智能套利系统状态")
        print("=" * 60)
        print(f"🔄 运行状态: {'运行中' if self.运行状态 else '已停止'}")
        print(f"🔑 关键词池大小: {len(self.动态关键词池)}")
        print(f"📚 学习历史: {len(self.学习历史)} 条记录")
        
        if self.学习历史:
            最近记录 = self.学习历史[-1]
            print(f"🕐 最后学习: {最近记录['时间']}")
            print(f"📋 最后类型: {最近记录['类型']}")
        
        print(f"\n⏰ 学习周期设置:")
        for 类型, 小时 in self.学习周期.items():
            print(f"  {类型}: 每 {小时} 小时")


def 主函数():
    """主函数"""
    print("🤖 全自动智能套利学习系统")
    print("=" * 60)
    print("💡 系统将完全自动运行，无需人工干预")
    
    系统 = 全自动智能套利系统()
    
    while True:
        print(f"\n📋 请选择操作:")
        print("1. 🚀 启动自动学习 (推荐)")
        print("2. 📊 显示系统状态")
        print("3. ⚙️ 修改学习周期")
        print("0. 退出")
        
        选择 = input("\n请输入选项 (0-3): ").strip()
        
        if 选择 == "0":
            if 系统.运行状态:
                系统.停止自动学习()
            print("👋 程序退出")
            break
        elif 选择 == "1":
            print("🚀 启动自动学习系统...")
            print("💡 系统将自动运行，按 Ctrl+C 可停止")
            系统.启动自动学习()
        elif 选择 == "2":
            系统.显示系统状态()
        elif 选择 == "3":
            print("⚙️ 当前学习周期:")
            for 类型, 小时 in 系统.学习周期.items():
                print(f"  {类型}: {小时} 小时")
            print("💡 可以手动修改代码中的学习周期设置")
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
