#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动智能套利系统启动器

作者: AI助手
日期: 2025-07-27
功能: 一键启动全自动智能套利学习系统
"""

import os
import sys
import time
import subprocess
from datetime import datetime


def 检查依赖():
    """检查必要的依赖包"""
    print("🔍 检查系统依赖...")
    
    必要包 = [
        'schedule',
        'google.generativeai',
        'requests',
        'beautifulsoup4',
        'dotenv'
    ]
    
    缺失包 = []
    
    for 包名 in 必要包:
        try:
            __import__(包名.replace('.', '_') if '.' in 包名 else 包名)
            print(f"✅ {包名}")
        except ImportError:
            print(f"❌ {包名} - 缺失")
            缺失包.append(包名)
    
    if 缺失包:
        print(f"\n⚠️ 发现缺失依赖包: {', '.join(缺失包)}")
        print("请运行以下命令安装:")
        print("pip install schedule google-generativeai requests beautifulsoup4 python-dotenv")
        return False
    
    print("✅ 所有依赖检查通过")
    return True


def 检查环境变量():
    """检查环境变量配置"""
    print("\n🔑 检查环境变量...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        print(f"✅ GEMINI_API_KEY: {api_key[:10]}...{api_key[-4:]}")
        return True
    else:
        print("❌ GEMINI_API_KEY 未设置")
        print("请设置环境变量:")
        print("set GEMINI_API_KEY=your_api_key_here")
        return False


def 检查文件结构():
    """检查必要的文件结构"""
    print("\n📁 检查文件结构...")
    
    必要文件 = [
        '全自动智能套利系统.py',
        '智能套利发现系统.py',
        '智能信息获取系统.py',
        '简化文章采集器.py'
    ]
    
    缺失文件 = []
    
    for 文件名 in 必要文件:
        if os.path.exists(文件名):
            print(f"✅ {文件名}")
        else:
            print(f"❌ {文件名} - 缺失")
            缺失文件.append(文件名)
    
    if 缺失文件:
        print(f"\n⚠️ 发现缺失文件: {', '.join(缺失文件)}")
        return False
    
    print("✅ 所有必要文件检查通过")
    return True


def 创建必要目录():
    """创建必要的目录"""
    print("\n📂 创建必要目录...")
    
    目录列表 = [
        "../../数据存储/原文章",
        "logs",
        "reports"
    ]
    
    for 目录 in 目录列表:
        try:
            os.makedirs(目录, exist_ok=True)
            print(f"✅ {目录}")
        except Exception as e:
            print(f"❌ {目录} - 创建失败: {e}")


def 显示系统信息():
    """显示系统信息"""
    print("\n" + "="*60)
    print("🤖 全自动智能套利学习系统")
    print("="*60)
    print("📋 系统功能:")
    print("  🧠 自动套利类型发现 - 每24小时")
    print("  🔍 自动信息获取分析 - 每4小时")
    print("  📖 自动文章采集 - 每2小时")
    print("  📚 自动知识整理 - 每12小时")
    print()
    print("💡 系统特点:")
    print("  ✅ 完全自动化，无需人工干预")
    print("  ✅ 智能学习，持续优化")
    print("  ✅ 多渠道信息获取")
    print("  ✅ AI智能分析")
    print("  ✅ 知识库自动管理")
    print()
    print("📊 输出文件:")
    print("  📚 套利知识库.json - AI学习的套利知识")
    print("  📄 自动获取_*.json - 信息获取结果")
    print("  📋 学习报告_*.json - 每日学习报告")
    print("  📝 全自动套利系统.log - 系统运行日志")
    print("="*60)


def 启动系统():
    """启动全自动系统"""
    print("\n🚀 启动全自动智能套利系统...")
    
    try:
        # 导入并启动系统
        from 全自动智能套利系统 import 全自动智能套利系统
        
        系统 = 全自动智能套利系统()
        
        print("✅ 系统初始化成功")
        print("🔄 开始自动学习...")
        print("💡 按 Ctrl+C 可停止系统")
        print("📝 详细日志请查看: 全自动套利系统.log")
        print()
        
        # 启动自动学习
        系统.启动自动学习()
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()


def 主函数():
    """主函数"""
    print("🚀 全自动智能套利系统启动器")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 系统检查
    if not 检查依赖():
        print("\n❌ 依赖检查失败，请安装缺失的包后重试")
        return
    
    if not 检查环境变量():
        print("\n❌ 环境变量检查失败，请设置GEMINI_API_KEY后重试")
        return
    
    if not 检查文件结构():
        print("\n❌ 文件结构检查失败，请确保所有必要文件存在")
        return
    
    # 创建目录
    创建必要目录()
    
    # 显示系统信息
    显示系统信息()
    
    # 询问是否启动
    while True:
        选择 = input("\n是否启动全自动系统? (y/n): ").strip().lower()
        
        if 选择 in ['y', 'yes', '是', '启动']:
            启动系统()
            break
        elif 选择 in ['n', 'no', '否', '退出']:
            print("👋 程序退出")
            break
        else:
            print("❌ 请输入 y 或 n")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
