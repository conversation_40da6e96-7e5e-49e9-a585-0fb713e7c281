#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习质量评估系统

作者: AI助手
日期: 2025-07-27
功能: 评估智能学习系统的学习质量和效果
"""

import os
import sys
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
from collections import Counter

# 添加配置文件路径
根目录 = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.append(os.path.join(根目录, '配置文件'))

from 智能学习配置 import 获取文件路径, 目录配置


class 学习质量评估系统:
    """学习质量评估系统"""
    
    def __init__(self):
        """初始化评估系统"""
        self.质量标准 = {
            '关键词质量': {
                '专业性': 0.3,    # 是否包含专业术语
                '相关性': 0.3,    # 与套利的相关程度
                '多样性': 0.2,    # 关键词的多样性
                '实用性': 0.2     # 实际应用价值
            },
            '信息质量': {
                '准确性': 0.25,   # 信息的准确程度
                '时效性': 0.25,   # 信息的新鲜程度
                '完整性': 0.25,   # 信息的完整程度
                '价值性': 0.25    # 信息的价值程度
            },
            '学习效果': {
                '增长率': 0.4,    # 知识库增长速度
                '覆盖面': 0.3,    # 知识覆盖广度
                '深度': 0.3       # 知识学习深度
            }
        }
        
        # 套利相关的专业术语库
        self.专业术语库 = {
            '核心术语': [
                '套利', '价差', '溢价', '折价', '无风险', '风险中性',
                '期现', '跨市场', '统计套利', '配对交易', '均值回归'
            ],
            '金融工具': [
                '可转债', 'ETF', 'REIT', '期货', '期权', '债券',
                '股指', '商品', '外汇', '利率', '信用'
            ],
            '策略类型': [
                '分红套利', '转股套利', '申赎套利', '跨期套利',
                '跨品种套利', '日历套利', '波动率套利'
            ],
            '技术指标': [
                '溢价率', '折价率', '基差', '价差', '相关系数',
                '波动率', '流动性', '成交量', '持仓量'
            ]
        }
        
        # 低质量关键词特征
        self.低质量特征 = [
            '策略策略', '套利套利',  # 重复词汇
            '的的', '和和',          # 无意义重复
            '123', 'abc',           # 无意义字符
            '测试', 'test',         # 测试词汇
        ]
    
    def 评估关键词质量(self, 关键词列表: List[str]) -> Dict[str, Any]:
        """评估关键词质量"""
        评估结果 = {
            '总体评分': 0,
            '详细评分': {},
            '质量分析': {},
            '改进建议': []
        }
        
        if not 关键词列表:
            return 评估结果
        
        # 1. 专业性评估
        专业性得分 = self._评估专业性(关键词列表)
        
        # 2. 相关性评估
        相关性得分 = self._评估相关性(关键词列表)
        
        # 3. 多样性评估
        多样性得分 = self._评估多样性(关键词列表)
        
        # 4. 实用性评估
        实用性得分 = self._评估实用性(关键词列表)
        
        # 计算总体评分
        权重 = self.质量标准['关键词质量']
        总体评分 = (
            专业性得分 * 权重['专业性'] +
            相关性得分 * 权重['相关性'] +
            多样性得分 * 权重['多样性'] +
            实用性得分 * 权重['实用性']
        ) * 100
        
        评估结果.update({
            '总体评分': round(总体评分, 2),
            '详细评分': {
                '专业性': round(专业性得分 * 100, 2),
                '相关性': round(相关性得分 * 100, 2),
                '多样性': round(多样性得分 * 100, 2),
                '实用性': round(实用性得分 * 100, 2)
            },
            '质量分析': self._分析关键词质量(关键词列表),
            '改进建议': self._生成关键词改进建议(关键词列表, 总体评分)
        })
        
        return 评估结果
    
    def _评估专业性(self, 关键词列表: List[str]) -> float:
        """评估关键词的专业性"""
        专业词汇数 = 0
        总词汇数 = len(关键词列表)
        
        if 总词汇数 == 0:
            return 0
        
        # 统计包含专业术语的关键词
        for 关键词 in 关键词列表:
            for 类别, 术语列表 in self.专业术语库.items():
                if any(术语 in 关键词 for 术语 in 术语列表):
                    专业词汇数 += 1
                    break
        
        return min(专业词汇数 / 总词汇数, 1.0)
    
    def _评估相关性(self, 关键词列表: List[str]) -> float:
        """评估关键词与套利的相关性"""
        相关词汇数 = 0
        总词汇数 = len(关键词列表)
        
        if 总词汇数 == 0:
            return 0
        
        套利相关词 = ['套利', '价差', '溢价', '折价', '无风险', '对冲', '配对']
        
        for 关键词 in 关键词列表:
            if any(相关词 in 关键词 for 相关词 in 套利相关词):
                相关词汇数 += 1
        
        return min(相关词汇数 / 总词汇数 * 1.5, 1.0)  # 加权提升相关性重要性
    
    def _评估多样性(self, 关键词列表: List[str]) -> float:
        """评估关键词的多样性"""
        if len(关键词列表) <= 1:
            return 0
        
        # 计算词汇长度分布
        长度分布 = Counter(len(词) for 词 in 关键词列表)
        长度多样性 = len(长度分布) / min(len(关键词列表), 10)  # 最多10种长度
        
        # 计算词汇类型分布
        类型分布 = {'策略': 0, '工具': 0, '指标': 0, '其他': 0}
        for 关键词 in 关键词列表:
            if any(词 in 关键词 for 词 in ['策略', '方法', '技术']):
                类型分布['策略'] += 1
            elif any(词 in 关键词 for 词 in ['ETF', '债券', '期货', '股票']):
                类型分布['工具'] += 1
            elif any(词 in 关键词 for 词 in ['率', '差', '量', '价']):
                类型分布['指标'] += 1
            else:
                类型分布['其他'] += 1
        
        有效类型数 = sum(1 for 数量 in 类型分布.values() if 数量 > 0)
        类型多样性 = 有效类型数 / 4
        
        return (长度多样性 + 类型多样性) / 2
    
    def _评估实用性(self, 关键词列表: List[str]) -> float:
        """评估关键词的实用性"""
        实用词汇数 = 0
        总词汇数 = len(关键词列表)
        
        if 总词汇数 == 0:
            return 0
        
        # 检查低质量特征
        for 关键词 in 关键词列表:
            是否低质量 = False
            
            # 检查重复字符
            if any(特征 in 关键词 for 特征 in self.低质量特征):
                是否低质量 = True
            
            # 检查长度（太短或太长都不好）
            if len(关键词) < 2 or len(关键词) > 20:
                是否低质量 = True
            
            # 检查是否全是数字或特殊字符
            if 关键词.isdigit() or not any(c.isalnum() for c in 关键词):
                是否低质量 = True
            
            if not 是否低质量:
                实用词汇数 += 1
        
        return 实用词汇数 / 总词汇数
    
    def _分析关键词质量(self, 关键词列表: List[str]) -> Dict[str, Any]:
        """分析关键词质量详情"""
        分析结果 = {
            '总数量': len(关键词列表),
            '专业术语数量': 0,
            '套利相关数量': 0,
            '低质量数量': 0,
            '优质关键词': [],
            '低质量关键词': [],
            '类型分布': {}
        }
        
        for 关键词 in 关键词列表:
            # 检查专业性
            是否专业 = any(
                any(术语 in 关键词 for 术语 in 术语列表)
                for 术语列表 in self.专业术语库.values()
            )
            if 是否专业:
                分析结果['专业术语数量'] += 1
            
            # 检查套利相关性
            套利相关词 = ['套利', '价差', '溢价', '折价']
            if any(词 in 关键词 for 词 in 套利相关词):
                分析结果['套利相关数量'] += 1
            
            # 检查质量
            是否低质量 = any(特征 in 关键词 for 特征 in self.低质量特征)
            if 是否低质量 or len(关键词) < 2:
                分析结果['低质量数量'] += 1
                分析结果['低质量关键词'].append(关键词)
            elif 是否专业:
                分析结果['优质关键词'].append(关键词)
        
        return 分析结果
    
    def _生成关键词改进建议(self, 关键词列表: List[str], 总体评分: float) -> List[str]:
        """生成关键词改进建议"""
        建议列表 = []
        
        if 总体评分 < 60:
            建议列表.append("🔴 关键词质量较低，建议重新优化学习策略")
        elif 总体评分 < 80:
            建议列表.append("🟡 关键词质量中等，有改进空间")
        else:
            建议列表.append("🟢 关键词质量良好，继续保持")
        
        # 具体建议
        专业性得分 = self._评估专业性(关键词列表) * 100
        if 专业性得分 < 50:
            建议列表.append("📚 增加更多专业术语，如'转股溢价率'、'基差套利'等")
        
        相关性得分 = self._评估相关性(关键词列表) * 100
        if 相关性得分 < 60:
            建议列表.append("🎯 提高与套利的相关性，聚焦核心概念")
        
        多样性得分 = self._评估多样性(关键词列表) * 100
        if 多样性得分 < 50:
            建议列表.append("🌈 增加关键词多样性，覆盖更多套利类型")
        
        # 检查低质量关键词
        低质量数量 = sum(1 for 词 in 关键词列表 if any(特征 in 词 for 特征 in self.低质量特征))
        if 低质量数量 > 0:
            建议列表.append(f"🧹 清理 {低质量数量} 个低质量关键词")
        
        return 建议列表
    
    def 评估信息质量(self, 信息列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估收集信息的质量"""
        if not 信息列表:
            return {'总体评分': 0, '详细评分': {}, '分析结果': {}}
        
        # 1. 准确性评估（基于内容长度和结构）
        准确性得分 = self._评估信息准确性(信息列表)
        
        # 2. 时效性评估（基于时间戳）
        时效性得分 = self._评估信息时效性(信息列表)
        
        # 3. 完整性评估（基于字段完整度）
        完整性得分 = self._评估信息完整性(信息列表)
        
        # 4. 价值性评估（基于内容相关性）
        价值性得分 = self._评估信息价值性(信息列表)
        
        # 计算总体评分
        权重 = self.质量标准['信息质量']
        总体评分 = (
            准确性得分 * 权重['准确性'] +
            时效性得分 * 权重['时效性'] +
            完整性得分 * 权重['完整性'] +
            价值性得分 * 权重['价值性']
        ) * 100
        
        return {
            '总体评分': round(总体评分, 2),
            '详细评分': {
                '准确性': round(准确性得分 * 100, 2),
                '时效性': round(时效性得分 * 100, 2),
                '完整性': round(完整性得分 * 100, 2),
                '价值性': round(价值性得分 * 100, 2)
            },
            '分析结果': {
                '信息数量': len(信息列表),
                '平均内容长度': sum(len(str(info.get('内容', ''))) for info in 信息列表) / len(信息列表),
                '完整字段比例': self._计算完整字段比例(信息列表)
            }
        }
    
    def _评估信息准确性(self, 信息列表: List[Dict[str, Any]]) -> float:
        """评估信息准确性"""
        if not 信息列表:
            return 0
        
        准确信息数 = 0
        for 信息 in 信息列表:
            内容 = str(信息.get('内容', ''))
            # 基于内容长度和结构判断准确性
            if len(内容) > 50 and '套利' in 内容:  # 基本的准确性判断
                准确信息数 += 1
        
        return 准确信息数 / len(信息列表)
    
    def _评估信息时效性(self, 信息列表: List[Dict[str, Any]]) -> float:
        """评估信息时效性"""
        if not 信息列表:
            return 0
        
        现在时间 = datetime.now()
        时效信息数 = 0
        
        for 信息 in 信息列表:
            时间字符串 = 信息.get('时间', '')
            try:
                信息时间 = datetime.fromisoformat(时间字符串.replace('Z', '+00:00'))
                时间差 = 现在时间 - 信息时间
                if 时间差.days <= 7:  # 7天内的信息认为是时效的
                    时效信息数 += 1
            except:
                pass  # 时间格式错误，跳过
        
        return 时效信息数 / len(信息列表)
    
    def _评估信息完整性(self, 信息列表: List[Dict[str, Any]]) -> float:
        """评估信息完整性"""
        if not 信息列表:
            return 0
        
        必需字段 = ['标题', '内容', '来源', '时间']
        完整信息数 = 0
        
        for 信息 in 信息列表:
            完整字段数 = sum(1 for 字段 in 必需字段 if 字段 in 信息 and 信息[字段])
            if 完整字段数 >= 3:  # 至少3个字段完整
                完整信息数 += 1
        
        return 完整信息数 / len(信息列表)
    
    def _评估信息价值性(self, 信息列表: List[Dict[str, Any]]) -> float:
        """评估信息价值性"""
        if not 信息列表:
            return 0
        
        高价值信息数 = 0
        价值关键词 = ['套利机会', '价差', '溢价', '投资策略', '风险', '收益']
        
        for 信息 in 信息列表:
            内容 = str(信息.get('内容', '')) + str(信息.get('标题', ''))
            价值词数量 = sum(1 for 词 in 价值关键词 if 词 in 内容)
            if 价值词数量 >= 2:  # 包含至少2个价值关键词
                高价值信息数 += 1
        
        return 高价值信息数 / len(信息列表)
    
    def _计算完整字段比例(self, 信息列表: List[Dict[str, Any]]) -> float:
        """计算完整字段比例"""
        if not 信息列表:
            return 0
        
        必需字段 = ['标题', '内容', '来源', '时间']
        总字段数 = len(信息列表) * len(必需字段)
        完整字段数 = 0
        
        for 信息 in 信息列表:
            for 字段 in 必需字段:
                if 字段 in 信息 and 信息[字段]:
                    完整字段数 += 1
        
        return 完整字段数 / 总字段数 if 总字段数 > 0 else 0
    
    def 生成质量报告(self) -> Dict[str, Any]:
        """生成完整的质量评估报告"""
        报告 = {
            '生成时间': datetime.now().isoformat(),
            '关键词质量评估': {},
            '信息质量评估': {},
            '学习效果评估': {},
            '总体建议': []
        }
        
        try:
            # 加载系统状态
            状态文件 = 获取文件路径('简化系统状态')
            if os.path.exists(状态文件):
                with open(状态文件, 'r', encoding='utf-8') as f:
                    系统状态 = json.load(f)
                
                # 评估关键词质量
                关键词列表 = 系统状态.get('动态关键词池', [])
                报告['关键词质量评估'] = self.评估关键词质量(关键词列表)
                
                # 评估学习效果
                学习历史 = 系统状态.get('学习历史', [])
                报告['学习效果评估'] = self._评估学习效果(学习历史, 关键词列表)
            
            # 评估信息质量（查找最新的信息收集文件）
            知识库目录 = 目录配置['知识库目录']
            信息文件列表 = [f for f in os.listdir(知识库目录) if f.startswith('信息收集_') and f.endswith('.json')]
            
            if 信息文件列表:
                最新信息文件 = max(信息文件列表)
                信息文件路径 = os.path.join(知识库目录, 最新信息文件)
                
                with open(信息文件路径, 'r', encoding='utf-8') as f:
                    信息列表 = json.load(f)
                    报告['信息质量评估'] = self.评估信息质量(信息列表)
            
            # 生成总体建议
            报告['总体建议'] = self._生成总体建议(报告)
            
        except Exception as e:
            报告['错误'] = f"生成报告时出错: {e}"
        
        return 报告
    
    def _评估学习效果(self, 学习历史: List[Dict], 关键词列表: List[str]) -> Dict[str, Any]:
        """评估学习效果"""
        if not 学习历史:
            return {'总体评分': 0, '详细评分': {}, '分析结果': {}}
        
        # 计算增长率
        增长率得分 = self._计算增长率(学习历史)
        
        # 计算覆盖面
        覆盖面得分 = min(len(关键词列表) / 100, 1.0)  # 100个关键词为满分
        
        # 计算学习深度
        深度得分 = self._计算学习深度(学习历史)
        
        # 计算总体评分
        权重 = self.质量标准['学习效果']
        总体评分 = (
            增长率得分 * 权重['增长率'] +
            覆盖面得分 * 权重['覆盖面'] +
            深度得分 * 权重['深度']
        ) * 100
        
        return {
            '总体评分': round(总体评分, 2),
            '详细评分': {
                '增长率': round(增长率得分 * 100, 2),
                '覆盖面': round(覆盖面得分 * 100, 2),
                '深度': round(深度得分 * 100, 2)
            },
            '分析结果': {
                '学习次数': len(学习历史),
                '关键词总数': len(关键词列表),
                '平均每次学习增长': self._计算平均增长(学习历史)
            }
        }
    
    def _计算增长率(self, 学习历史: List[Dict]) -> float:
        """计算知识增长率"""
        if len(学习历史) < 2:
            return 0.5  # 默认中等水平
        
        最近记录 = 学习历史[-5:]  # 最近5次记录
        增长数量 = [记录.get('新关键词数量', 0) for 记录 in 最近记录]
        平均增长 = sum(增长数量) / len(增长数量)
        
        # 每次学习增长5个关键词为满分
        return min(平均增长 / 5, 1.0)
    
    def _计算学习深度(self, 学习历史: List[Dict]) -> float:
        """计算学习深度"""
        if not 学习历史:
            return 0
        
        学习类型统计 = Counter(记录.get('类型', '') for 记录 in 学习历史)
        类型多样性 = len(学习类型统计) / 3  # 假设有3种学习类型
        
        学习频率 = len(学习历史) / max((datetime.now() - datetime.fromisoformat(学习历史[0]['时间'])).days, 1)
        频率得分 = min(学习频率 / 5, 1.0)  # 每天5次学习为满分
        
        return (类型多样性 + 频率得分) / 2
    
    def _计算平均增长(self, 学习历史: List[Dict]) -> float:
        """计算平均每次学习的增长量"""
        if not 学习历史:
            return 0
        
        增长总量 = sum(记录.get('新关键词数量', 0) for 记录 in 学习历史)
        return 增长总量 / len(学习历史)
    
    def _生成总体建议(self, 报告: Dict[str, Any]) -> List[str]:
        """生成总体改进建议"""
        建议列表 = []
        
        # 基于关键词质量给建议
        关键词评估 = 报告.get('关键词质量评估', {})
        关键词得分 = 关键词评估.get('总体评分', 0)
        
        if 关键词得分 < 60:
            建议列表.append("🔴 关键词质量需要重点改进，建议优化学习算法")
        elif 关键词得分 < 80:
            建议列表.append("🟡 关键词质量中等，可以进一步优化")
        
        # 基于信息质量给建议
        信息评估 = 报告.get('信息质量评估', {})
        信息得分 = 信息评估.get('总体评分', 0)
        
        if 信息得分 < 60:
            建议列表.append("📰 信息收集质量有待提高，建议改进信息源")
        
        # 基于学习效果给建议
        学习评估 = 报告.get('学习效果评估', {})
        学习得分 = 学习评估.get('总体评分', 0)
        
        if 学习得分 < 60:
            建议列表.append("📈 学习效果需要改进，建议调整学习策略")
        
        if not 建议列表:
            建议列表.append("🎉 系统运行良好，继续保持当前学习策略")
        
        return 建议列表


def 主函数():
    """主函数"""
    print("📊 学习质量评估系统")
    print("=" * 60)
    
    评估系统 = 学习质量评估系统()
    
    while True:
        print(f"\n📋 请选择评估类型:")
        print("1. 📊 生成完整质量报告")
        print("2. 🔑 评估关键词质量")
        print("3. 📰 评估信息质量")
        print("4. 📈 评估学习效果")
        print("0. 退出")
        
        选择 = input("\n请输入选项 (0-4): ").strip()
        
        if 选择 == "0":
            print("👋 程序退出")
            break
        elif 选择 == "1":
            print("📊 生成完整质量报告...")
            报告 = 评估系统.生成质量报告()
            
            # 保存报告
            报告文件 = os.path.join(目录配置['知识库目录'], f"质量评估报告_{datetime.now().strftime('%Y%m%d_%H%M')}.json")
            with open(报告文件, 'w', encoding='utf-8') as f:
                json.dump(报告, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 报告已生成: {os.path.basename(报告文件)}")
            
            # 显示摘要
            print(f"\n📋 质量评估摘要:")
            if '关键词质量评估' in 报告:
                print(f"  🔑 关键词质量: {报告['关键词质量评估'].get('总体评分', 0):.1f}分")
            if '信息质量评估' in 报告:
                print(f"  📰 信息质量: {报告['信息质量评估'].get('总体评分', 0):.1f}分")
            if '学习效果评估' in 报告:
                print(f"  📈 学习效果: {报告['学习效果评估'].get('总体评分', 0):.1f}分")
            
            if '总体建议' in 报告:
                print(f"\n💡 改进建议:")
                for 建议 in 报告['总体建议']:
                    print(f"  {建议}")
        
        else:
            print("❌ 功能开发中...")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
