#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能信息获取系统

作者: AI助手
日期: 2025-07-27
功能: 整合雪球、公众号等多数据源，智能获取套利信息
"""

import os
import json
import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Set
from bs4 import BeautifulSoup
import google.generativeai as genai
from dotenv import load_dotenv


class 智能信息获取系统:
    """智能信息获取系统"""
    
    def __init__(self):
        """初始化系统"""
        self.数据源配置 = {
            '雪球': {
                '基础URL': 'https://xueqiu.com',
                '搜索URL': 'https://xueqiu.com/k?q=',
                '请求头': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'https://xueqiu.com'
                }
            },
            '微信公众号': {
                '搜索URL': 'https://weixin.sogou.com/weixin?type=2&query=',
                '请求头': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            }
        }
        
        self.gemini模型 = None
        self.套利知识库 = {}
        self._初始化AI模型()
        self._加载套利知识库()
    
    def _初始化AI模型(self):
        """初始化AI模型"""
        try:
            load_dotenv()
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
                self.gemini模型 = genai.GenerativeModel('gemini-2.5-flash')
                print("✅ AI模型初始化成功")
            else:
                print("❌ 未找到GEMINI_API_KEY")
        except Exception as e:
            print(f"❌ AI模型初始化失败: {e}")
    
    def _加载套利知识库(self):
        """加载套利知识库"""
        try:
            if os.path.exists("套利知识库.json"):
                with open("套利知识库.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.套利知识库 = data.get('套利知识库', {})
                print(f"📚 已加载套利知识库，包含 {len(self.套利知识库)} 种套利类型")
        except Exception as e:
            print(f"❌ 加载套利知识库失败: {e}")
    
    def 智能生成搜索关键词(self, 用户输入: str) -> List[str]:
        """
        基于用户输入智能生成搜索关键词
        
        参数:
            用户输入: 用户输入的关键词或描述
            
        返回:
            扩展的搜索关键词列表
        """
        if not self.gemini模型:
            # 如果没有AI模型，使用基础扩展
            基础关键词 = [用户输入]
            if "套利" in 用户输入:
                基础关键词.extend([
                    "价差套利", "跨市场套利", "可转债套利", "分红套利",
                    "期现套利", "统计套利", "配对交易", "套利机会"
                ])
            return 基础关键词
        
        扩展提示 = f"""
用户输入了关键词："{用户输入}"

请智能分析并生成相关的搜索关键词，要求：

1. 如果用户输入的是"套利"，请生成所有套利相关的关键词
2. 如果用户输入的是具体套利类型，请生成该类型的详细关键词
3. 如果用户输入的是股票代码或名称，请生成该标的的套利相关关键词
4. 包含同义词、相关词、专业术语

请以JSON格式返回：
{{
    "核心关键词": ["关键词1", "关键词2"],
    "扩展关键词": ["扩展词1", "扩展词2"],
    "专业术语": ["术语1", "术语2"],
    "相关标的": ["标的1", "标的2"]
}}
"""
        
        try:
            response = self.gemini模型.generate_content(扩展提示)
            response_text = response.text
            
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            else:
                json_text = response_text
            
            关键词数据 = json.loads(json_text)
            
            所有关键词 = []
            所有关键词.extend(关键词数据.get('核心关键词', []))
            所有关键词.extend(关键词数据.get('扩展关键词', []))
            所有关键词.extend(关键词数据.get('专业术语', []))
            所有关键词.extend(关键词数据.get('相关标的', []))
            
            return list(set(所有关键词))  # 去重
            
        except Exception as e:
            print(f"❌ 智能关键词生成失败: {e}")
            return [用户输入]
    
    def 搜索雪球信息(self, 关键词: str, 数量限制: int = 10) -> List[Dict[str, Any]]:
        """
        搜索雪球平台信息
        
        参数:
            关键词: 搜索关键词
            数量限制: 返回结果数量限制
            
        返回:
            搜索结果列表
        """
        print(f"🔍 正在搜索雪球: {关键词}")
        
        try:
            # 构建搜索URL
            搜索URL = self.数据源配置['雪球']['搜索URL'] + 关键词
            请求头 = self.数据源配置['雪球']['请求头']
            
            # 发送请求
            response = requests.get(搜索URL, headers=请求头, timeout=10)
            
            if response.status_code == 200:
                # 这里需要根据雪球的实际API或页面结构来解析
                # 由于雪球可能有反爬虫机制，这里提供基础框架
                
                结果列表 = []
                # 模拟返回结果
                结果列表.append({
                    '标题': f'雪球搜索结果: {关键词}',
                    '内容': f'关于{关键词}的讨论内容...',
                    '链接': 搜索URL,
                    '来源': '雪球',
                    '时间': datetime.now().isoformat(),
                    '关键词': 关键词
                })
                
                print(f"✅ 雪球搜索完成，找到 {len(结果列表)} 条结果")
                return 结果列表[:数量限制]
            else:
                print(f"❌ 雪球搜索失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 雪球搜索异常: {e}")
            return []
    
    def 搜索公众号信息(self, 关键词: str, 数量限制: int = 10) -> List[Dict[str, Any]]:
        """
        搜索微信公众号信息
        
        参数:
            关键词: 搜索关键词
            数量限制: 返回结果数量限制
            
        返回:
            搜索结果列表
        """
        print(f"🔍 正在搜索公众号: {关键词}")
        
        try:
            # 构建搜索URL
            搜索URL = self.数据源配置['微信公众号']['搜索URL'] + 关键词
            请求头 = self.数据源配置['微信公众号']['请求头']
            
            # 发送请求
            response = requests.get(搜索URL, headers=请求头, timeout=10)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                结果列表 = []
                # 解析搜索结果（需要根据实际页面结构调整）
                文章列表 = soup.find_all('div', class_='txt-box')
                
                for 文章 in 文章列表[:数量限制]:
                    try:
                        标题元素 = 文章.find('h3')
                        标题 = 标题元素.get_text().strip() if 标题元素 else "未知标题"
                        
                        链接元素 = 文章.find('a')
                        链接 = 链接元素.get('href') if 链接元素 else ""
                        
                        摘要元素 = 文章.find('p', class_='txt-info')
                        摘要 = 摘要元素.get_text().strip() if 摘要元素 else ""
                        
                        结果列表.append({
                            '标题': 标题,
                            '内容': 摘要,
                            '链接': 链接,
                            '来源': '微信公众号',
                            '时间': datetime.now().isoformat(),
                            '关键词': 关键词
                        })
                    except Exception as e:
                        continue
                
                print(f"✅ 公众号搜索完成，找到 {len(结果列表)} 条结果")
                return 结果列表
            else:
                print(f"❌ 公众号搜索失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 公众号搜索异常: {e}")
            return []
    
    def 智能分析信息(self, 信息列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        使用AI智能分析获取的信息
        
        参数:
            信息列表: 获取的信息列表
            
        返回:
            分析结果
        """
        if not self.gemini模型 or not 信息列表:
            return {}
        
        # 准备分析内容
        分析内容 = ""
        for i, 信息 in enumerate(信息列表, 1):
            分析内容 += f"\n{i}. 标题: {信息['标题']}\n"
            分析内容 += f"   内容: {信息['内容']}\n"
            分析内容 += f"   来源: {信息['来源']}\n"
        
        分析提示 = f"""
请分析以下套利相关信息，提取关键要点：

{分析内容}

请从以下角度进行分析：
1. 套利机会识别
2. 操作策略要点
3. 风险提示
4. 时间敏感信息
5. 相关标的

请以JSON格式返回分析结果：
{{
    "套利机会": ["机会1", "机会2"],
    "操作策略": ["策略1", "策略2"],
    "风险提示": ["风险1", "风险2"],
    "时间敏感": ["事件1", "事件2"],
    "相关标的": ["标的1", "标的2"],
    "总结": "整体分析总结"
}}
"""
        
        try:
            print("🤖 正在进行AI智能分析...")
            response = self.gemini模型.generate_content(分析提示)
            response_text = response.text
            
            if "```json" in response_text:
                json_start = response_text.find("```json") + 7
                json_end = response_text.find("```", json_start)
                json_text = response_text[json_start:json_end].strip()
            else:
                json_text = response_text
            
            分析结果 = json.loads(json_text)
            print("✅ AI分析完成")
            return 分析结果
            
        except Exception as e:
            print(f"❌ AI分析失败: {e}")
            return {}
    
    def 智能信息获取流程(self, 用户输入: str) -> Dict[str, Any]:
        """
        完整的智能信息获取流程
        
        参数:
            用户输入: 用户输入的关键词或描述
            
        返回:
            完整的获取和分析结果
        """
        print(f"🚀 开始智能信息获取流程")
        print(f"🔑 用户输入: {用户输入}")
        print("=" * 60)
        
        # 第一步：智能生成搜索关键词
        print("📖 第一步：智能生成搜索关键词")
        搜索关键词列表 = self.智能生成搜索关键词(用户输入)
        print(f"✅ 生成 {len(搜索关键词列表)} 个搜索关键词")
        
        # 第二步：多数据源信息获取
        print(f"\n🔍 第二步：多数据源信息获取")
        所有信息 = []
        
        for 关键词 in 搜索关键词列表[:5]:  # 限制关键词数量避免过多请求
            print(f"\n  搜索关键词: {关键词}")
            
            # 搜索雪球
            雪球结果 = self.搜索雪球信息(关键词, 3)
            所有信息.extend(雪球结果)
            
            # 搜索公众号
            公众号结果 = self.搜索公众号信息(关键词, 3)
            所有信息.extend(公众号结果)
            
            # 添加延迟避免请求过快
            time.sleep(1)
        
        # 第三步：AI智能分析
        print(f"\n🤖 第三步：AI智能分析")
        分析结果 = self.智能分析信息(所有信息)
        
        # 第四步：整理最终结果
        最终结果 = {
            '用户输入': 用户输入,
            '搜索关键词': 搜索关键词列表,
            '获取信息数量': len(所有信息),
            '信息详情': 所有信息,
            'AI分析结果': 分析结果,
            '处理时间': datetime.now().isoformat()
        }
        
        print(f"\n📊 获取结果统计:")
        print(f"✅ 搜索关键词: {len(搜索关键词列表)} 个")
        print(f"✅ 获取信息: {len(所有信息)} 条")
        print(f"✅ AI分析: {'完成' if 分析结果 else '失败'}")
        
        return 最终结果


    def 显示分析结果(self, 分析结果: Dict[str, Any]):
        """显示AI分析结果"""
        if not 分析结果:
            print("❌ 没有分析结果")
            return

        print(f"\n📊 AI分析结果:")
        print("=" * 50)

        if '套利机会' in 分析结果:
            print(f"💰 套利机会:")
            for i, 机会 in enumerate(分析结果['套利机会'], 1):
                print(f"  {i}. {机会}")

        if '操作策略' in 分析结果:
            print(f"\n📋 操作策略:")
            for i, 策略 in enumerate(分析结果['操作策略'], 1):
                print(f"  {i}. {策略}")

        if '风险提示' in 分析结果:
            print(f"\n⚠️ 风险提示:")
            for i, 风险 in enumerate(分析结果['风险提示'], 1):
                print(f"  {i}. {风险}")

        if '相关标的' in 分析结果:
            print(f"\n🎯 相关标的:")
            for i, 标的 in enumerate(分析结果['相关标的'], 1):
                print(f"  {i}. {标的}")

        if '总结' in 分析结果:
            print(f"\n📝 总结:")
            print(f"  {分析结果['总结']}")


def 主函数():
    """主函数"""
    print("🤖 智能套利信息获取系统")
    print("=" * 60)
    print("💡 只需输入'套利'等关键词，系统将智能学习和获取相关信息")

    系统 = 智能信息获取系统()

    while True:
        print(f"\n📋 请选择操作:")
        print("1. 🎯 智能套利信息获取 (推荐)")
        print("2. 🔍 测试关键词扩展")
        print("3. 📊 显示最近结果")
        print("0. 退出")

        选择 = input("\n请输入选项 (0-3): ").strip()

        if 选择 == "0":
            print("👋 程序退出")
            break
        elif 选择 == "1":
            print(f"\n💡 示例输入:")
            print("  - 套利")
            print("  - 可转债套利")
            print("  - A股港股价差")
            print("  - 分红套利")

            用户输入 = input("\n请输入关键词或描述: ").strip()
            if 用户输入:
                结果 = 系统.智能信息获取流程(用户输入)

                # 显示分析结果
                if 'AI分析结果' in 结果:
                    系统.显示分析结果(结果['AI分析结果'])

                # 保存结果
                结果文件 = f"智能获取结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(结果文件, 'w', encoding='utf-8') as f:
                    json.dump(结果, f, ensure_ascii=False, indent=2)
                print(f"\n💾 完整结果已保存到: {结果文件}")

        elif 选择 == "2":
            关键词 = input("请输入测试关键词: ").strip()
            if 关键词:
                扩展关键词 = 系统.智能生成搜索关键词(关键词)
                print(f"🔍 扩展关键词 ({len(扩展关键词)} 个):")
                for i, 词 in enumerate(扩展关键词, 1):
                    print(f"  {i}. {词}")

        elif 选择 == "3":
            # 显示最近的结果文件
            import glob
            结果文件列表 = glob.glob("智能获取结果_*.json")
            if 结果文件列表:
                最新文件 = max(结果文件列表, key=os.path.getctime)
                print(f"📄 最新结果文件: {最新文件}")

                try:
                    with open(最新文件, 'r', encoding='utf-8') as f:
                        结果 = json.load(f)

                    print(f"🔑 搜索关键词: {结果.get('用户输入', '未知')}")
                    print(f"📊 获取信息: {结果.get('获取信息数量', 0)} 条")

                    if 'AI分析结果' in 结果:
                        系统.显示分析结果(结果['AI分析结果'])

                except Exception as e:
                    print(f"❌ 读取结果文件失败: {e}")
            else:
                print("📄 没有找到历史结果文件")

        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
