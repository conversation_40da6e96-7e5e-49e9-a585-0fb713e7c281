#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试学习系统 - 快速验证学习功能

作者: AI助手
日期: 2025-07-27
功能: 快速测试学习系统，学习周期更短，便于观察
"""

import os
import sys
import time
import schedule
from datetime import datetime

# 添加路径
当前目录 = os.path.dirname(os.path.abspath(__file__))
根目录 = os.path.dirname(当前目录)
sys.path.append(os.path.join(当前目录, '核心程序'))
sys.path.append(os.path.join(根目录, '配置文件'))

from 智能学习配置 import 获取配置
from 简化全自动系统 import 简化全自动套利系统


class 测试学习系统(简化全自动套利系统):
    """测试版学习系统，学习周期更短"""
    
    def __init__(self):
        super().__init__()
        # 使用测试周期
        self.学习周期 = {
            '基础学习': 0.1,    # 6分钟
            '信息收集': 0.05,   # 3分钟
            '知识整理': 0.2     # 12分钟
        }
        print("🧪 测试模式启动 - 学习周期已缩短")
        print(f"  基础学习: 每 {self.学习周期['基础学习']*60:.0f} 分钟")
        print(f"  信息收集: 每 {self.学习周期['信息收集']*60:.0f} 分钟")
        print(f"  知识整理: 每 {self.学习周期['知识整理']*60:.0f} 分钟")
    
    def 启动测试学习(self):
        """启动测试学习系统"""
        print("🚀 启动测试学习系统")
        print("💡 您将看到更频繁的学习活动")
        
        # 设置定时任务（使用分钟而不是小时）
        schedule.every(int(self.学习周期['基础学习']*60)).minutes.do(self.基础学习任务)
        schedule.every(int(self.学习周期['信息收集']*60)).minutes.do(self.信息收集任务)
        schedule.every(int(self.学习周期['知识整理']*60)).minutes.do(self.知识整理任务)
        
        # 立即执行一次
        print("执行初始化学习...")
        self.基础学习任务()
        time.sleep(10)
        self.信息收集任务()
        
        self.运行状态 = True
        self._保存系统状态()
        
        print("✅ 测试系统启动完成")
        print("📊 学习活动监控:")
        
        # 主循环 - 每30秒检查一次
        try:
            while self.运行状态:
                schedule.run_pending()
                
                # 显示下次学习时间
                下次任务 = schedule.next_run()
                if 下次任务:
                    剩余时间 = 下次任务 - datetime.now()
                    剩余秒数 = int(剩余时间.total_seconds())
                    print(f"\r⏰ 下次学习: {剩余秒数}秒后", end="", flush=True)
                
                time.sleep(30)  # 每30秒检查一次
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号，正在关闭测试系统...")
            self.停止自动学习()
    
    def 基础学习任务(self):
        """重写基础学习任务，添加更多输出"""
        print(f"\n🧠 [{datetime.now().strftime('%H:%M:%S')}] 开始基础学习任务")
        
        try:
            学习数量 = 0
            新关键词 = set()
            
            # 显示当前关键词池状态
            print(f"📊 当前关键词池大小: {len(self.动态关键词池)}")
            
            # 模拟学习过程
            for i, 基础词 in enumerate(list(self.动态关键词池)[:3], 1):  # 处理3个关键词
                print(f"  {i}. 学习关键词: {基础词}")
                相关词汇 = self._生成相关关键词(基础词)
                新关键词.update(相关词汇)
                学习数量 += len(相关词汇)
                
                # 更新知识库
                if 基础词 not in self.套利知识库:
                    self.套利知识库[基础词] = {
                        '类型': '套利策略',
                        '相关词汇': list(相关词汇),
                        '学习时间': datetime.now().isoformat(),
                        '学习次数': 1
                    }
                else:
                    self.套利知识库[基础词]['学习次数'] += 1
                    self.套利知识库[基础词]['最后学习'] = datetime.now().isoformat()
                
                print(f"     → 发现 {len(相关词汇)} 个相关词汇")
            
            # 更新动态关键词池
            原始大小 = len(self.动态关键词池)
            self.动态关键词池.update(新关键词)
            新增大小 = len(self.动态关键词池) - 原始大小
            
            # 记录学习历史
            学习记录 = {
                '时间': datetime.now().isoformat(),
                '类型': '基础学习',
                '学习数量': 学习数量,
                '新关键词数量': len(新关键词),
                '关键词池大小': len(self.动态关键词池)
            }
            self.学习历史.append(学习记录)
            
            print(f"✅ 基础学习完成: 学习 {学习数量} 个概念，关键词池 {原始大小} → {len(self.动态关键词池)} (+{新增大小})")
            
        except Exception as e:
            print(f"❌ 基础学习任务失败: {e}")
    
    def 信息收集任务(self):
        """重写信息收集任务，添加更多输出"""
        print(f"\n🔍 [{datetime.now().strftime('%H:%M:%S')}] 开始信息收集任务")
        
        try:
            # 从关键词池中选择关键词
            选择关键词 = list(self.动态关键词池)[:2]  # 选择2个关键词
            print(f"📋 选择关键词: {', '.join(选择关键词)}")
            
            收集结果 = []
            收集数量 = 0
            
            for i, 关键词 in enumerate(选择关键词, 1):
                print(f"  {i}. 收集关键词: {关键词}")
                信息 = self._模拟信息收集(关键词)
                收集结果.extend(信息)
                收集数量 += len(信息)
                print(f"     → 收集到 {len(信息)} 条信息")
                time.sleep(1)
            
            # 保存收集结果
            if 收集结果:
                from 智能学习配置 import 获取文件路径
                结果文件 = 获取文件路径('信息收集结果', keyword='测试')
                
                import json
                with open(结果文件, 'w', encoding='utf-8') as f:
                    json.dump(收集结果, f, ensure_ascii=False, indent=2)
                print(f"💾 结果已保存: {os.path.basename(结果文件)}")
            
            # 记录学习历史
            学习记录 = {
                '时间': datetime.now().isoformat(),
                '类型': '信息收集',
                '收集数量': 收集数量,
                '关键词': 选择关键词,
                '结果文件': 结果文件 if 收集结果 else None
            }
            self.学习历史.append(学习记录)
            
            print(f"✅ 信息收集完成: 收集 {收集数量} 条信息")
            
        except Exception as e:
            print(f"❌ 信息收集任务失败: {e}")
    
    def 知识整理任务(self):
        """重写知识整理任务，添加更多输出"""
        print(f"\n📚 [{datetime.now().strftime('%H:%M:%S')}] 开始知识整理任务")
        
        try:
            # 显示统计信息
            print(f"📊 系统状态:")
            print(f"  关键词池大小: {len(self.动态关键词池)}")
            print(f"  知识库大小: {len(self.套利知识库)}")
            print(f"  学习历史: {len(self.学习历史)} 条记录")
            
            # 生成学习报告
            self._生成学习报告()
            
            # 保存系统状态
            self._保存系统状态()
            
            print("✅ 知识整理完成")
            
        except Exception as e:
            print(f"❌ 知识整理任务失败: {e}")


def 主函数():
    """主函数"""
    print("🧪 测试学习系统")
    print("=" * 60)
    print("💡 这是一个测试版本，学习周期更短，便于观察学习过程")
    print("⚠️  仅用于测试，不建议长期运行")
    
    while True:
        print(f"\n📋 请选择操作:")
        print("1. 🚀 启动测试学习系统")
        print("2. 🧪 执行单次学习测试")
        print("3. 📊 显示系统状态")
        print("0. 退出")
        
        选择 = input("\n请输入选项 (0-3): ").strip()
        
        if 选择 == "0":
            print("👋 程序退出")
            break
        elif 选择 == "1":
            系统 = 测试学习系统()
            print("🚀 启动测试学习系统...")
            print("💡 您将看到每几分钟就有学习活动")
            print("💡 按 Ctrl+C 可停止系统")
            系统.启动测试学习()
        elif 选择 == "2":
            print("🧪 执行单次学习测试...")
            系统 = 测试学习系统()
            系统.基础学习任务()
            系统.信息收集任务()
            系统.知识整理任务()
            print("✅ 单次测试完成")
        elif 选择 == "3":
            try:
                系统 = 测试学习系统()
                系统.显示系统状态()
            except Exception as e:
                print(f"❌ 显示状态失败: {e}")
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
