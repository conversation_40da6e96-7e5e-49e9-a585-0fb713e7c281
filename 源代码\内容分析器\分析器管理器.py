# -*- coding: utf-8 -*-
"""
内容分析器管理器

作者: AI助手
日期: 2025-07-27
功能: 统一管理所有内容分析器，协调分析任务
"""

import asyncio
from typing import Dict, Any, List, Optional

from .文本分析器 import 文本分析器
from .质量评估器 import 质量评估器
from ..工具模块.日志管理器 import 日志混入类
from ..工具模块.数据库管理器 import 数据库管理器, 文章表


class 分析器管理器(日志混入类):
    """内容分析器管理器"""
    
    def __init__(self, 配置: Dict[str, Any], 数据库管理器实例: 数据库管理器):
        """
        初始化分析器管理器
        
        参数:
            配置: 系统配置
            数据库管理器实例: 数据库管理器
        """
        self.配置 = 配置
        self.数据库管理器 = 数据库管理器实例
        self.分析器配置 = 配置['内容分析']
        
        # 初始化各个分析器
        self.文本分析器 = 文本分析器(
            self.分析器配置,
            数据库管理器实例,
            self.分析器配置['AI模型']
        )
        
        self.质量评估器 = 质量评估器(self.分析器配置)
        
        self.质量阈值 = self.分析器配置.get('质量阈值', 0.7)
    
    async def 分析单篇文章(self, 文章ID: int) -> Optional[Dict[str, Any]]:
        """
        分析单篇文章
        
        参数:
            文章ID: 文章ID
            
        返回:
            完整分析结果
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            文章 = 会话.query(文章表).filter(文章表.id == 文章ID).first()
            if not 文章:
                self.日志器.warning(f"文章不存在: {文章ID}")
                会话.close()
                return None
            
            文章数据 = {
                'id': 文章.id,
                '标题': 文章.标题,
                '内容': 文章.内容,
                '摘要': 文章.摘要,
                '作者': 文章.作者,
                '来源账号': 文章.来源账号
            }
            
            会话.close()
            
            self.日志器.info(f"开始分析文章: {文章.标题}")
            
            # 1. 文本分析
            文本分析结果 = await self.文本分析器.分析文章内容(文章数据)
            
            # 2. 质量评估
            质量评估结果 = self.质量评估器.评估文章质量(文章数据, 文本分析结果)
            
            # 3. 综合分析结果
            综合结果 = {
                '文章ID': 文章ID,
                '文本分析': 文本分析结果,
                '质量评估': 质量评估结果,
                '最终评分': max(文本分析结果.get('质量评分', 0.0), 质量评估结果.get('总体评分', 0.0)),
                '是否通过': False,
                '分析时间': asyncio.get_event_loop().time()
            }
            
            # 判断是否通过质量阈值
            综合结果['是否通过'] = 综合结果['最终评分'] >= self.质量阈值
            
            # 保存分析结果
            保存结果 = {
                '关键词': 文本分析结果.get('关键词', []) + 文本分析结果.get('AI关键词', []),
                '质量评分': 综合结果['最终评分'],
                'AI摘要': 文本分析结果.get('AI摘要', ''),
                '情感倾向': 文本分析结果.get('情感倾向', '中性'),
                '主题分类': 文本分析结果.get('主题分类', '未分类'),
                '质量等级': 质量评估结果.get('质量等级', '待改进'),
                '改进建议': 质量评估结果.get('改进建议', [])
            }
            
            if self.文本分析器.保存分析结果(文章ID, 保存结果):
                self.日志器.info(f"文章分析完成: {文章.标题}, 最终评分: {综合结果['最终评分']:.2f}")
                return 综合结果
            else:
                self.日志器.error(f"保存分析结果失败: {文章ID}")
                return None
                
        except Exception as 错误:
            self.日志器.error(f"分析单篇文章失败: {错误}")
            return None
    
    async def 批量分析文章(self, 状态过滤: str = '已收集', 最大数量: int = 50) -> Dict[str, Any]:
        """
        批量分析文章
        
        参数:
            状态过滤: 要分析的文章状态
            最大数量: 最大分析数量
            
        返回:
            批量分析结果统计
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            # 获取待分析文章
            待分析文章 = 会话.query(文章表).filter(
                文章表.状态 == 状态过滤
            ).limit(最大数量).all()
            
            会话.close()
            
            统计结果 = {
                '总文章数': len(待分析文章),
                '成功分析数': 0,
                '失败分析数': 0,
                '高质量文章数': 0,
                '各质量等级统计': {},
                '分析详情': []
            }
            
            self.日志器.info(f"开始批量分析 {len(待分析文章)} 篇文章")
            
            # 并发分析（限制并发数）
            信号量 = asyncio.Semaphore(3)  # 最多3个并发
            
            async def 分析单个文章(文章):
                async with 信号量:
                    try:
                        分析结果 = await self.分析单篇文章(文章.id)
                        if 分析结果:
                            统计结果['成功分析数'] += 1
                            if 分析结果['是否通过']:
                                统计结果['高质量文章数'] += 1
                            
                            # 统计质量等级
                            质量等级 = 分析结果['质量评估'].get('质量等级', '未知')
                            统计结果['各质量等级统计'][质量等级] = 统计结果['各质量等级统计'].get(质量等级, 0) + 1
                            
                            统计结果['分析详情'].append({
                                '文章ID': 文章.id,
                                '标题': 文章.标题,
                                '最终评分': 分析结果['最终评分'],
                                '质量等级': 质量等级,
                                '是否通过': 分析结果['是否通过']
                            })
                        else:
                            统计结果['失败分析数'] += 1
                            
                    except Exception as 错误:
                        self.日志器.error(f"分析文章 {文章.id} 失败: {错误}")
                        统计结果['失败分析数'] += 1
            
            # 执行并发分析
            任务列表 = [分析单个文章(文章) for 文章 in 待分析文章]
            await asyncio.gather(*任务列表)
            
            self.日志器.info(f"批量分析完成: 成功 {统计结果['成功分析数']} 篇, 失败 {统计结果['失败分析数']} 篇, 高质量 {统计结果['高质量文章数']} 篇")
            
            return 统计结果
            
        except Exception as 错误:
            self.日志器.error(f"批量分析文章失败: {错误}")
            return {}
    
    async def 启动定时分析任务(self):
        """启动定时分析任务"""
        分析间隔 = self.分析器配置.get('分析间隔', 30)  # 默认30分钟
        
        self.日志器.info(f"启动定时分析任务，间隔: {分析间隔} 分钟")
        
        while True:
            try:
                # 分析新收集的文章
                await self.批量分析文章('已收集')
                
                await asyncio.sleep(分析间隔 * 60)  # 转换为秒
                
            except Exception as 错误:
                self.日志器.error(f"定时分析任务异常: {错误}")
                await asyncio.sleep(60)  # 出错时等待1分钟后重试
    
    def 获取分析统计(self) -> Dict[str, Any]:
        """
        获取分析统计信息
        
        返回:
            统计信息
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            # 基础统计
            统计信息 = {
                '总文章数': 会话.query(文章表).count(),
                '已分析文章数': 会话.query(文章表).filter(文章表.状态 == '已分析').count(),
                '高质量文章数': 会话.query(文章表).filter(
                    文章表.质量评分 >= self.质量阈值
                ).count(),
                '待分析文章数': 会话.query(文章表).filter(文章表.状态 == '已收集').count(),
                '平均质量评分': 0.0,
                '质量分布': {},
                '来源统计': {}
            }
            
            # 计算平均质量评分
            已分析文章 = 会话.query(文章表).filter(
                文章表.状态 == '已分析'
            ).all()
            
            if 已分析文章:
                总评分 = sum(文章.质量评分 for 文章 in 已分析文章)
                统计信息['平均质量评分'] = 总评分 / len(已分析文章)
            
            # 质量分布统计
            质量区间 = [(0.0, 0.3, '低质量'), (0.3, 0.6, '中低质量'), (0.6, 0.8, '中等质量'), (0.8, 1.0, '高质量')]
            for 最小值, 最大值, 标签 in 质量区间:
                数量 = 会话.query(文章表).filter(
                    文章表.质量评分 >= 最小值,
                    文章表.质量评分 < 最大值
                ).count()
                统计信息['质量分布'][标签] = 数量
            
            # 来源统计
            from sqlalchemy import func
            来源统计 = 会话.query(
                文章表.来源账号,
                func.count(文章表.id).label('文章数'),
                func.avg(文章表.质量评分).label('平均评分')
            ).filter(
                文章表.状态 == '已分析'
            ).group_by(文章表.来源账号).all()
            
            for 来源, 文章数, 平均评分 in 来源统计:
                统计信息['来源统计'][来源] = {
                    '文章数': 文章数,
                    '平均评分': float(平均评分) if 平均评分 else 0.0
                }
            
            会话.close()
            return 统计信息
            
        except Exception as 错误:
            self.日志器.error(f"获取分析统计失败: {错误}")
            return {}
    
    def 获取高质量文章(self, 数量限制: int = 20) -> List[Dict[str, Any]]:
        """
        获取高质量文章列表
        
        参数:
            数量限制: 返回文章数量限制
            
        返回:
            高质量文章列表
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            高质量文章 = 会话.query(文章表).filter(
                文章表.质量评分 >= self.质量阈值,
                文章表.状态 == '已分析'
            ).order_by(文章表.质量评分.desc()).limit(数量限制).all()
            
            文章列表 = []
            for 文章 in 高质量文章:
                文章信息 = {
                    'id': 文章.id,
                    '标题': 文章.标题,
                    '摘要': 文章.摘要,
                    '作者': 文章.作者,
                    '来源账号': 文章.来源账号,
                    '质量评分': 文章.质量评分,
                    '字数': 文章.字数,
                    '收集时间': 文章.收集时间.isoformat() if 文章.收集时间 else None
                }
                文章列表.append(文章信息)
            
            会话.close()
            return 文章列表
            
        except Exception as 错误:
            self.日志器.error(f"获取高质量文章失败: {错误}")
            return []
