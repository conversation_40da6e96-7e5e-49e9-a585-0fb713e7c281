# -*- coding: utf-8 -*-
"""
内容分析器基类

作者: AI助手
日期: 2025-07-27
功能: 定义内容分析器的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import json

from ..工具模块.日志管理器 import 日志混入类
from ..工具模块.数据库管理器 import 数据库管理器, 文章表


class 基础分析器(ABC, 日志混入类):
    """内容分析器基类"""
    
    def __init__(self, 配置: Dict[str, Any], 数据库管理器实例: 数据库管理器):
        """
        初始化分析器
        
        参数:
            配置: 分析器配置
            数据库管理器实例: 数据库管理器
        """
        self.配置 = 配置
        self.数据库管理器 = 数据库管理器实例
        self.质量阈值 = 配置.get('质量阈值', 0.7)
    
    @abstractmethod
    async def 分析文章内容(self, 文章数据: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析文章内容（抽象方法，子类必须实现）
        
        参数:
            文章数据: 文章数据字典
            
        返回:
            分析结果字典
        """
        pass
    
    def 计算文本基础指标(self, 内容: str) -> Dict[str, Any]:
        """
        计算文本基础指标
        
        参数:
            内容: 文章内容
            
        返回:
            基础指标字典
        """
        指标 = {
            '字数': len(内容),
            '段落数': len([段 for 段 in 内容.split('\n') if 段.strip()]),
            '句子数': len([句 for 句 in 内容.split('。') if 句.strip()]),
            '平均句长': 0,
            '包含标点': any(标点 in 内容 for 标点 in '，。！？；：'),
            '包含数字': any(字符.isdigit() for 字符 in 内容),
            '包含英文': any(字符.isalpha() and ord(字符) < 128 for 字符 in 内容)
        }
        
        # 计算平均句长
        句子列表 = [句.strip() for 句 in 内容.split('。') if 句.strip()]
        if 句子列表:
            指标['平均句长'] = sum(len(句) for 句 in 句子列表) / len(句子列表)
        
        return 指标
    
    def 评估内容质量(self, 文章数据: Dict[str, Any], 分析结果: Dict[str, Any]) -> float:
        """
        评估内容质量
        
        参数:
            文章数据: 文章数据
            分析结果: 分析结果
            
        返回:
            质量评分 (0.0-1.0)
        """
        评分 = 0.0
        
        # 基础指标评分 (30%)
        基础指标 = 分析结果.get('基础指标', {})
        字数 = 基础指标.get('字数', 0)
        段落数 = 基础指标.get('段落数', 0)
        
        # 字数评分
        if 500 <= 字数 <= 3000:
            字数评分 = 1.0
        elif 字数 < 500:
            字数评分 = 字数 / 500
        else:
            字数评分 = max(0.5, 3000 / 字数)
        
        # 结构评分
        结构评分 = min(1.0, 段落数 / 5) if 段落数 > 0 else 0.0
        
        评分 += (字数评分 * 0.2 + 结构评分 * 0.1)
        
        # 内容丰富度评分 (30%)
        关键词数量 = len(分析结果.get('关键词', []))
        关键词评分 = min(1.0, 关键词数量 / 10)
        
        包含标点 = 基础指标.get('包含标点', False)
        包含数字 = 基础指标.get('包含数字', False)
        
        丰富度评分 = 关键词评分 * 0.2 + (0.05 if 包含标点 else 0) + (0.05 if 包含数字 else 0)
        评分 += 丰富度评分
        
        # 可读性评分 (20%)
        平均句长 = 基础指标.get('平均句长', 0)
        if 10 <= 平均句长 <= 30:
            可读性评分 = 0.2
        else:
            可读性评分 = 0.1
        
        评分 += 可读性评分
        
        # AI评分 (20%)
        AI评分 = 分析结果.get('AI评分', 0.5)
        评分 += AI评分 * 0.2
        
        return min(1.0, 评分)
    
    def 保存分析结果(self, 文章ID: int, 分析结果: Dict[str, Any]) -> bool:
        """
        保存分析结果到数据库
        
        参数:
            文章ID: 文章ID
            分析结果: 分析结果
            
        返回:
            是否保存成功
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            文章 = 会话.query(文章表).filter(文章表.id == 文章ID).first()
            if not 文章:
                self.日志器.warning(f"文章不存在: {文章ID}")
                会话.close()
                return False
            
            # 更新文章分析结果
            文章.关键词 = json.dumps(分析结果.get('关键词', []), ensure_ascii=False)
            文章.质量评分 = 分析结果.get('质量评分', 0.0)
            文章.状态 = '已分析'
            文章.是否已处理 = 分析结果.get('质量评分', 0.0) >= self.质量阈值
            
            会话.commit()
            会话.close()
            
            self.日志器.info(f"保存分析结果成功: 文章ID {文章ID}, 质量评分 {分析结果.get('质量评分', 0.0):.2f}")
            return True
            
        except Exception as 错误:
            self.日志器.error(f"保存分析结果失败: {错误}")
            if '会话' in locals():
                会话.rollback()
                会话.close()
            return False
    
    async def 分析单篇文章(self, 文章ID: int) -> Optional[Dict[str, Any]]:
        """
        分析单篇文章
        
        参数:
            文章ID: 文章ID
            
        返回:
            分析结果，如果失败返回None
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            文章 = 会话.query(文章表).filter(文章表.id == 文章ID).first()
            if not 文章:
                self.日志器.warning(f"文章不存在: {文章ID}")
                会话.close()
                return None
            
            文章数据 = {
                'id': 文章.id,
                '标题': 文章.标题,
                '内容': 文章.内容,
                '摘要': 文章.摘要,
                '作者': 文章.作者,
                '来源账号': 文章.来源账号
            }
            
            会话.close()
            
            # 执行分析
            分析结果 = await self.分析文章内容(文章数据)
            
            # 保存结果
            if self.保存分析结果(文章ID, 分析结果):
                return 分析结果
            else:
                return None
                
        except Exception as 错误:
            self.日志器.error(f"分析单篇文章失败: {错误}")
            return None
    
    async def 批量分析文章(self, 状态过滤: str = '已收集') -> Dict[str, Any]:
        """
        批量分析文章
        
        参数:
            状态过滤: 要分析的文章状态
            
        返回:
            批量分析结果统计
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            # 获取待分析文章
            待分析文章 = 会话.query(文章表).filter(
                文章表.状态 == 状态过滤
            ).all()
            
            会话.close()
            
            统计结果 = {
                '总文章数': len(待分析文章),
                '成功分析数': 0,
                '失败分析数': 0,
                '高质量文章数': 0,
                '分析详情': []
            }
            
            for 文章 in 待分析文章:
                try:
                    分析结果 = await self.分析单篇文章(文章.id)
                    if 分析结果:
                        统计结果['成功分析数'] += 1
                        if 分析结果.get('质量评分', 0.0) >= self.质量阈值:
                            统计结果['高质量文章数'] += 1
                        
                        统计结果['分析详情'].append({
                            '文章ID': 文章.id,
                            '标题': 文章.标题,
                            '质量评分': 分析结果.get('质量评分', 0.0),
                            '关键词数量': len(分析结果.get('关键词', []))
                        })
                    else:
                        统计结果['失败分析数'] += 1
                        
                except Exception as 错误:
                    self.日志器.error(f"分析文章 {文章.id} 失败: {错误}")
                    统计结果['失败分析数'] += 1
            
            self.日志器.info(f"批量分析完成: 成功 {统计结果['成功分析数']} 篇, 失败 {统计结果['失败分析数']} 篇")
            return 统计结果
            
        except Exception as 错误:
            self.日志器.error(f"批量分析文章失败: {错误}")
            return {}
    
    def 获取分析统计(self) -> Dict[str, Any]:
        """
        获取分析统计信息
        
        返回:
            统计信息
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            统计信息 = {
                '总文章数': 会话.query(文章表).count(),
                '已分析文章数': 会话.query(文章表).filter(文章表.状态 == '已分析').count(),
                '高质量文章数': 会话.query(文章表).filter(
                    文章表.质量评分 >= self.质量阈值
                ).count(),
                '待分析文章数': 会话.query(文章表).filter(文章表.状态 == '已收集').count(),
                '平均质量评分': 0.0
            }
            
            # 计算平均质量评分
            已分析文章 = 会话.query(文章表).filter(
                文章表.状态 == '已分析'
            ).all()
            
            if 已分析文章:
                总评分 = sum(文章.质量评分 for 文章 in 已分析文章)
                统计信息['平均质量评分'] = 总评分 / len(已分析文章)
            
            会话.close()
            return 统计信息
            
        except Exception as 错误:
            self.日志器.error(f"获取分析统计失败: {错误}")
            return {}
