# -*- coding: utf-8 -*-
"""
文本分析器

作者: AI助手
日期: 2025-07-27
功能: 使用AI模型分析文本内容，提取关键信息和评估质量
"""

import asyncio
import aiohttp
import json
import jieba
from typing import Dict, Any, List, Optional
import re

from .基础分析器 import 基础分析器


class 文本分析器(基础分析器):
    """文本分析器，集成AI模型进行深度分析"""
    
    def __init__(self, 配置: Dict[str, Any], 数据库管理器实例, AI配置: Dict[str, Any]):
        """
        初始化文本分析器
        
        参数:
            配置: 分析器配置
            数据库管理器实例: 数据库管理器
            AI配置: AI模型配置
        """
        super().__init__(配置, 数据库管理器实例)
        self.AI配置 = AI配置
        self.服务商 = AI配置.get('服务商', 'gemini')
        self.API密钥 = AI配置.get('API密钥', '')
        self.模型名称 = AI配置.get('模型名称', 'gemini-pro')
        
        # 初始化jieba分词
        jieba.initialize()
    
    async def 分析文章内容(self, 文章数据: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析文章内容
        
        参数:
            文章数据: 文章数据字典
            
        返回:
            分析结果字典
        """
        标题 = 文章数据.get('标题', '')
        内容 = 文章数据.get('内容', '')
        
        self.日志器.info(f"开始分析文章: {标题}")
        
        # 计算基础指标
        基础指标 = self.计算文本基础指标(内容)
        
        # 提取关键词
        关键词 = self.提取关键词(标题 + ' ' + 内容)
        
        # AI分析
        AI分析结果 = await self.AI分析内容(标题, 内容)
        
        # 综合分析结果
        分析结果 = {
            '基础指标': 基础指标,
            '关键词': 关键词,
            'AI评分': AI分析结果.get('质量评分', 0.5),
            'AI关键词': AI分析结果.get('关键词', []),
            'AI摘要': AI分析结果.get('摘要', ''),
            '情感倾向': AI分析结果.get('情感倾向', '中性'),
            '主题分类': AI分析结果.get('主题分类', '未分类')
        }
        
        # 计算最终质量评分
        分析结果['质量评分'] = self.评估内容质量(文章数据, 分析结果)
        
        self.日志器.info(f"文章分析完成: {标题}, 质量评分: {分析结果['质量评分']:.2f}")
        
        return 分析结果
    
    def 提取关键词(self, 文本: str, 最大数量: int = 10) -> List[str]:
        """
        提取关键词
        
        参数:
            文本: 要分析的文本
            最大数量: 最大关键词数量
            
        返回:
            关键词列表
        """
        try:
            # 使用jieba进行分词
            词语列表 = jieba.cut(文本)
            
            # 过滤停用词和短词
            停用词 = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            
            关键词统计 = {}
            for 词 in 词语列表:
                词 = 词.strip()
                if len(词) >= 2 and 词 not in 停用词 and not 词.isdigit():
                    关键词统计[词] = 关键词统计.get(词, 0) + 1
            
            # 按频率排序并返回前N个
            排序关键词 = sorted(关键词统计.items(), key=lambda x: x[1], reverse=True)
            return [词 for 词, 频率 in 排序关键词[:最大数量]]
            
        except Exception as 错误:
            self.日志器.error(f"提取关键词失败: {错误}")
            return []
    
    async def AI分析内容(self, 标题: str, 内容: str) -> Dict[str, Any]:
        """
        使用AI模型分析内容
        
        参数:
            标题: 文章标题
            内容: 文章内容
            
        返回:
            AI分析结果
        """
        if self.服务商 == 'gemini':
            return await self._Gemini分析(标题, 内容)
        elif self.服务商 == 'openai':
            return await self._OpenAI分析(标题, 内容)
        else:
            return await self._模拟AI分析(标题, 内容)
    
    async def _Gemini分析(self, 标题: str, 内容: str) -> Dict[str, Any]:
        """
        使用Gemini API分析内容
        
        参数:
            标题: 文章标题
            内容: 文章内容
            
        返回:
            分析结果
        """
        try:
            if not self.API密钥:
                self.日志器.warning("Gemini API密钥未配置，使用模拟分析")
                return await self._模拟AI分析(标题, 内容)
            
            # 构建分析提示
            提示 = f"""请分析以下文章的内容质量和特征：

标题：{标题}

内容：{内容[:1000]}...

请从以下几个方面进行分析并以JSON格式返回结果：
1. 质量评分（0.0-1.0）
2. 关键词（5-10个）
3. 摘要（50-100字）
4. 情感倾向（正面/负面/中性）
5. 主题分类（科技/商业/教育/娱乐/其他）

返回格式：
{{
    "质量评分": 0.8,
    "关键词": ["关键词1", "关键词2"],
    "摘要": "文章摘要",
    "情感倾向": "正面",
    "主题分类": "科技"
}}"""

            # 调用Gemini 2.5 Pro API
            url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"
            headers = {
                "Content-Type": "application/json",
                "x-goog-api-key": self.API密钥
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": 提示
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.3,
                    "maxOutputTokens": 1000
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        结果 = await response.json()
                        生成内容 = 结果.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
                        
                        # 解析JSON结果
                        try:
                            # 提取JSON部分
                            json_match = re.search(r'\{.*\}', 生成内容, re.DOTALL)
                            if json_match:
                                分析结果 = json.loads(json_match.group())
                                return 分析结果
                            else:
                                raise ValueError("无法找到JSON格式的结果")
                        except json.JSONDecodeError:
                            self.日志器.warning("Gemini返回结果格式错误，使用模拟分析")
                            return await self._模拟AI分析(标题, 内容)
                    else:
                        self.日志器.error(f"Gemini API调用失败: {response.status}")
                        return await self._模拟AI分析(标题, 内容)
                        
        except Exception as 错误:
            self.日志器.error(f"Gemini分析失败: {错误}")
            return await self._模拟AI分析(标题, 内容)
    
    async def _OpenAI分析(self, 标题: str, 内容: str) -> Dict[str, Any]:
        """
        使用OpenAI API分析内容
        
        参数:
            标题: 文章标题
            内容: 文章内容
            
        返回:
            分析结果
        """
        try:
            if not self.API密钥:
                self.日志器.warning("OpenAI API密钥未配置，使用模拟分析")
                return await self._模拟AI分析(标题, 内容)
            
            # 这里可以实现OpenAI API调用
            # 暂时使用模拟分析
            self.日志器.info("OpenAI分析功能待实现，使用模拟分析")
            return await self._模拟AI分析(标题, 内容)
            
        except Exception as 错误:
            self.日志器.error(f"OpenAI分析失败: {错误}")
            return await self._模拟AI分析(标题, 内容)
    
    async def _模拟AI分析(self, 标题: str, 内容: str) -> Dict[str, Any]:
        """
        模拟AI分析（用于测试）
        
        参数:
            标题: 文章标题
            内容: 文章内容
            
        返回:
            模拟分析结果
        """
        # 模拟分析延迟
        await asyncio.sleep(0.5)
        
        # 基于简单规则的模拟分析
        字数 = len(内容)
        
        # 质量评分基于字数和结构
        if 字数 >= 1000:
            质量评分 = 0.8
        elif 字数 >= 500:
            质量评分 = 0.6
        else:
            质量评分 = 0.4
        
        # 包含特定关键词提高评分
        高质量关键词 = ['分析', '研究', '发展', '创新', '技术', '未来', '趋势', '深度']
        for 关键词 in 高质量关键词:
            if 关键词 in 标题 or 关键词 in 内容:
                质量评分 += 0.05
        
        质量评分 = min(1.0, 质量评分)
        
        # 模拟关键词提取
        模拟关键词 = ['人工智能', '技术发展', '创新应用', '未来趋势', '行业分析']
        
        # 模拟主题分类
        if any(词 in 标题 + 内容 for 词 in ['科技', '技术', 'AI', '人工智能']):
            主题分类 = '科技'
        elif any(词 in 标题 + 内容 for 词 in ['商业', '投资', '创业', '市场']):
            主题分类 = '商业'
        else:
            主题分类 = '综合'
        
        return {
            '质量评分': 质量评分,
            '关键词': 模拟关键词[:5],
            '摘要': f'这是一篇关于{主题分类}的文章，内容丰富，具有一定的参考价值。',
            '情感倾向': '正面',
            '主题分类': 主题分类
        }
