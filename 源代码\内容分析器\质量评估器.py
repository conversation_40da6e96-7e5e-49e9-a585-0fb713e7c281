# -*- coding: utf-8 -*-
"""
内容质量评估器

作者: AI助手
日期: 2025-07-27
功能: 专门负责评估文章内容的质量和价值
"""

from typing import Dict, Any, List
import re
import math

from ..工具模块.日志管理器 import 日志混入类


class 质量评估器(日志混入类):
    """内容质量评估器"""
    
    def __init__(self, 配置: Dict[str, Any]):
        """
        初始化质量评估器
        
        参数:
            配置: 评估器配置
        """
        self.配置 = 配置
        self.质量阈值 = 配置.get('质量阈值', 0.7)
        
        # 质量评估权重
        self.权重配置 = {
            '内容长度': 0.15,
            '结构完整性': 0.20,
            '语言质量': 0.15,
            '信息密度': 0.20,
            '原创性': 0.15,
            '可读性': 0.15
        }
    
    def 评估文章质量(self, 文章数据: Dict[str, Any], 分析结果: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合评估文章质量
        
        参数:
            文章数据: 文章数据
            分析结果: 分析结果
            
        返回:
            质量评估结果
        """
        标题 = 文章数据.get('标题', '')
        内容 = 文章数据.get('内容', '')
        基础指标 = 分析结果.get('基础指标', {})
        
        评估结果 = {
            '总体评分': 0.0,
            '各项评分': {},
            '评估详情': {},
            '改进建议': []
        }
        
        # 1. 内容长度评估
        长度评分 = self._评估内容长度(基础指标)
        评估结果['各项评分']['内容长度'] = 长度评分
        
        # 2. 结构完整性评估
        结构评分 = self._评估结构完整性(标题, 内容, 基础指标)
        评估结果['各项评分']['结构完整性'] = 结构评分
        
        # 3. 语言质量评估
        语言评分 = self._评估语言质量(内容, 基础指标)
        评估结果['各项评分']['语言质量'] = 语言评分
        
        # 4. 信息密度评估
        信息密度评分 = self._评估信息密度(内容, 分析结果)
        评估结果['各项评分']['信息密度'] = 信息密度评分
        
        # 5. 原创性评估
        原创性评分 = self._评估原创性(内容, 分析结果)
        评估结果['各项评分']['原创性'] = 原创性评分
        
        # 6. 可读性评估
        可读性评分 = self._评估可读性(内容, 基础指标)
        评估结果['各项评分']['可读性'] = 可读性评分
        
        # 计算总体评分
        总评分 = 0.0
        for 项目, 评分 in 评估结果['各项评分'].items():
            权重 = self.权重配置.get(项目, 0.0)
            总评分 += 评分 * 权重
        
        评估结果['总体评分'] = min(1.0, 总评分)
        
        # 生成改进建议
        评估结果['改进建议'] = self._生成改进建议(评估结果['各项评分'])
        
        # 质量等级判定
        评估结果['质量等级'] = self._判定质量等级(评估结果['总体评分'])
        
        return 评估结果
    
    def _评估内容长度(self, 基础指标: Dict[str, Any]) -> float:
        """评估内容长度"""
        字数 = 基础指标.get('字数', 0)
        
        if 800 <= 字数 <= 2500:
            return 1.0
        elif 500 <= 字数 < 800:
            return 0.7 + (字数 - 500) / 300 * 0.3
        elif 2500 < 字数 <= 4000:
            return 1.0 - (字数 - 2500) / 1500 * 0.3
        elif 字数 < 500:
            return 字数 / 500 * 0.7
        else:
            return 0.4
    
    def _评估结构完整性(self, 标题: str, 内容: str, 基础指标: Dict[str, Any]) -> float:
        """评估文章结构完整性"""
        评分 = 0.0
        
        # 标题质量 (20%)
        if 标题:
            标题长度 = len(标题)
            if 10 <= 标题长度 <= 50:
                评分 += 0.2
            elif 5 <= 标题长度 < 10 or 50 < 标题长度 <= 80:
                评分 += 0.1
        
        # 段落结构 (30%)
        段落数 = 基础指标.get('段落数', 0)
        if 段落数 >= 3:
            评分 += 0.3
        elif 段落数 == 2:
            评分 += 0.2
        elif 段落数 == 1:
            评分 += 0.1
        
        # 是否有标题层次 (25%)
        if re.search(r'#+\s+|第[一二三四五六七八九十\d]+[章节部分]|[一二三四五六七八九十\d]+[、．]', 内容):
            评分 += 0.25
        
        # 是否有总结或结论 (25%)
        结论关键词 = ['总结', '结论', '总之', '综上', '最后', '因此', '所以']
        if any(关键词 in 内容[-200:] for 关键词 in 结论关键词):
            评分 += 0.25
        
        return min(1.0, 评分)
    
    def _评估语言质量(self, 内容: str, 基础指标: Dict[str, Any]) -> float:
        """评估语言质量"""
        评分 = 0.0
        
        # 句子长度合理性 (30%)
        平均句长 = 基础指标.get('平均句长', 0)
        if 15 <= 平均句长 <= 35:
            评分 += 0.3
        elif 10 <= 平均句长 < 15 or 35 < 平均句长 <= 50:
            评分 += 0.2
        else:
            评分 += 0.1
        
        # 标点符号使用 (20%)
        if 基础指标.get('包含标点', False):
            评分 += 0.2
        
        # 语言流畅性 (30%)
        # 检查重复词语
        词语 = re.findall(r'[\u4e00-\u9fff]+', 内容)
        if 词语:
            重复率 = 1 - len(set(词语)) / len(词语)
            if 重复率 < 0.3:
                评分 += 0.3
            elif 重复率 < 0.5:
                评分 += 0.2
            else:
                评分 += 0.1
        
        # 语法错误检查 (20%)
        # 简单的语法错误检查
        语法错误 = 0
        if '的的' in 内容:
            语法错误 += 1
        if '了了' in 内容:
            语法错误 += 1
        if re.search(r'[，。！？；：][，。！？；：]', 内容):
            语法错误 += 1
        
        if 语法错误 == 0:
            评分 += 0.2
        elif 语法错误 <= 2:
            评分 += 0.1
        
        return min(1.0, 评分)
    
    def _评估信息密度(self, 内容: str, 分析结果: Dict[str, Any]) -> float:
        """评估信息密度"""
        评分 = 0.0
        
        # 关键词密度 (40%)
        关键词数量 = len(分析结果.get('关键词', []))
        字数 = len(内容)
        if 字数 > 0:
            关键词密度 = 关键词数量 / (字数 / 100)  # 每100字的关键词数
            if 1 <= 关键词密度 <= 3:
                评分 += 0.4
            elif 0.5 <= 关键词密度 < 1 or 3 < 关键词密度 <= 5:
                评分 += 0.3
            else:
                评分 += 0.2
        
        # 数据和事实 (30%)
        数字模式 = r'\d+[%％万亿千百十]|\d+\.\d+|\d+年|\d+月|\d+日'
        数字匹配 = re.findall(数字模式, 内容)
        if len(数字匹配) >= 5:
            评分 += 0.3
        elif len(数字匹配) >= 2:
            评分 += 0.2
        elif len(数字匹配) >= 1:
            评分 += 0.1
        
        # 专业术语 (30%)
        专业词汇 = ['技术', '方法', '系统', '模型', '算法', '分析', '研究', '开发', '应用', '创新']
        专业词汇数量 = sum(1 for 词汇 in 专业词汇 if 词汇 in 内容)
        if 专业词汇数量 >= 5:
            评分 += 0.3
        elif 专业词汇数量 >= 3:
            评分 += 0.2
        elif 专业词汇数量 >= 1:
            评分 += 0.1
        
        return min(1.0, 评分)
    
    def _评估原创性(self, 内容: str, 分析结果: Dict[str, Any]) -> float:
        """评估原创性"""
        # 这里可以实现更复杂的原创性检测
        # 暂时基于简单规则
        评分 = 0.7  # 基础分
        
        # 检查是否有引用标记
        if '引用' in 内容 or '来源' in 内容 or '参考' in 内容:
            评分 += 0.1
        
        # 检查是否有个人观点
        观点词汇 = ['我认为', '个人觉得', '在我看来', '我的观点', '我们认为']
        if any(词汇 in 内容 for 词汇 in 观点词汇):
            评分 += 0.1
        
        # 检查是否有分析和总结
        分析词汇 = ['分析', '总结', '归纳', '推断', '得出']
        if any(词汇 in 内容 for 词汇 in 分析词汇):
            评分 += 0.1
        
        return min(1.0, 评分)
    
    def _评估可读性(self, 内容: str, 基础指标: Dict[str, Any]) -> float:
        """评估可读性"""
        评分 = 0.0
        
        # 段落长度合理性 (40%)
        段落 = [段.strip() for 段 in 内容.split('\n') if 段.strip()]
        if 段落:
            平均段落长度 = sum(len(段) for 段 in 段落) / len(段落)
            if 100 <= 平均段落长度 <= 300:
                评分 += 0.4
            elif 50 <= 平均段落长度 < 100 or 300 < 平均段落长度 <= 500:
                评分 += 0.3
            else:
                评分 += 0.2
        
        # 句子长度分布 (30%)
        句子 = [句.strip() for 句 in 内容.split('。') if 句.strip()]
        if 句子:
            句长分布 = [len(句) for 句 in 句子]
            句长方差 = sum((长度 - sum(句长分布)/len(句长分布))**2 for 长度 in 句长分布) / len(句长分布)
            if 句长方差 < 200:  # 句长分布较均匀
                评分 += 0.3
            elif 句长方差 < 400:
                评分 += 0.2
            else:
                评分 += 0.1
        
        # 连接词使用 (30%)
        连接词 = ['但是', '然而', '因此', '所以', '而且', '另外', '此外', '同时', '首先', '其次', '最后']
        连接词数量 = sum(1 for 词 in 连接词 if 词 in 内容)
        if 连接词数量 >= 3:
            评分 += 0.3
        elif 连接词数量 >= 1:
            评分 += 0.2
        
        return min(1.0, 评分)
    
    def _生成改进建议(self, 各项评分: Dict[str, float]) -> List[str]:
        """生成改进建议"""
        建议列表 = []
        
        for 项目, 评分 in 各项评分.items():
            if 评分 < 0.6:
                if 项目 == '内容长度':
                    建议列表.append("建议调整文章长度，保持在800-2500字之间")
                elif 项目 == '结构完整性':
                    建议列表.append("建议完善文章结构，增加标题层次和总结部分")
                elif 项目 == '语言质量':
                    建议列表.append("建议优化语言表达，注意句子长度和语法规范")
                elif 项目 == '信息密度':
                    建议列表.append("建议增加具体数据和专业术语，提高信息含量")
                elif 项目 == '原创性':
                    建议列表.append("建议增加个人观点和独特见解，提高原创性")
                elif 项目 == '可读性':
                    建议列表.append("建议优化段落结构和句子长度，提高可读性")
        
        if not 建议列表:
            建议列表.append("文章质量良好，继续保持")
        
        return 建议列表
    
    def _判定质量等级(self, 总评分: float) -> str:
        """判定质量等级"""
        if 总评分 >= 0.9:
            return "优秀"
        elif 总评分 >= 0.8:
            return "良好"
        elif 总评分 >= 0.7:
            return "中等"
        elif 总评分 >= 0.6:
            return "及格"
        else:
            return "待改进"
