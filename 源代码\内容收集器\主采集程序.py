#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主采集程序 - 统一的公众号文章采集工具

作者: AI助手
日期: 2025-07-27
功能: 整合所有采集功能，提供简单易用的接口
"""

import os
import sys
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

try:
    from 简化文章采集器 import 简化文章采集器
    from 提取套利信息 import 初始化Gemini, 提取套利信息, 生成套利总结

    # 导入配置文件（从配置文件夹）
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "配置文件"))
    from 公众号配置 import 获取公众号列表, 获取示例链接, 获取采集配置, 显示配置信息
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有必要文件都在正确位置")
    print("配置文件应在: 配置文件/公众号配置.py")
    sys.exit(1)


class 主采集程序:
    """主采集程序类"""
    
    def __init__(self):
        """初始化主采集程序"""
        self.采集器 = 简化文章采集器()
        self.配置 = 获取采集配置()
        self.gemini模型 = None
        
        print("🚀 主采集程序初始化完成")
        print(f"📁 文章保存目录: {self.采集器.保存目录}")
    
    def 初始化AI模型(self):
        """初始化AI模型"""
        print("🤖 正在初始化Gemini AI模型...")
        self.gemini模型 = 初始化Gemini()
        if self.gemini模型:
            print("✅ Gemini AI模型初始化成功")
        else:
            print("❌ Gemini AI模型初始化失败，套利信息提取功能将不可用")
    
    def 采集单篇文章(self, 文章链接: str, 来源账号: str = "未知", 提取套利信息=False) -> Optional[str]:
        """
        采集单篇文章
        
        参数:
            文章链接: 文章链接
            来源账号: 来源账号名称
            提取套利信息: 是否提取套利信息
            
        返回:
            保存的文件路径
        """
        print(f"\n📖 开始采集文章...")
        print(f"🔗 链接: {文章链接}")
        print(f"📺 来源: {来源账号}")
        
        # 采集文章
        文章数据 = self.采集器.采集文章(文章链接, 来源账号)
        
        if not 文章数据:
            print("❌ 文章采集失败")
            return None
        
        # 保存文章
        文件路径 = self.采集器.保存文章到文件(文章数据)
        
        if not 文件路径:
            print("❌ 文章保存失败")
            return None
        
        # 提取套利信息（如果需要且模型可用）
        if 提取套利信息 and self.gemini模型:
            print("\n🔍 正在提取套利信息...")
            try:
                套利信息 = 提取套利信息(self.gemini模型, 文章数据)
                套利总结 = 生成套利总结(self.gemini模型, 套利信息)
                
                # 保存套利信息
                套利文件路径 = 文件路径.replace('.md', '_套利分析.md')
                with open(套利文件路径, 'w', encoding='utf-8') as f:
                    f.write(f"# 套利信息分析报告\n\n")
                    f.write(f"**原文章**: {文章数据['标题']}\n")
                    f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("## 详细套利信息\n\n")
                    f.write(套利信息)
                    f.write("\n\n## 结构化总结\n\n")
                    f.write(套利总结)
                
                print(f"💡 套利信息已保存: {套利文件路径}")
                
            except Exception as 错误:
                print(f"❌ 套利信息提取失败: {错误}")
        
        return 文件路径
    
    def 批量采集示例文章(self, 提取套利信息=False) -> List[str]:
        """
        批量采集配置中的示例文章
        
        参数:
            提取套利信息: 是否提取套利信息
            
        返回:
            保存的文件路径列表
        """
        示例链接列表 = 获取示例链接()
        
        if not 示例链接列表:
            print("❌ 没有找到示例链接")
            return []
        
        print(f"🚀 开始批量采集 {len(示例链接列表)} 篇示例文章")
        
        保存文件列表 = []
        
        for i, 链接信息 in enumerate(示例链接列表, 1):
            print(f"\n{'='*60}")
            print(f"📖 采集第 {i}/{len(示例链接列表)} 篇文章")
            
            文件路径 = self.采集单篇文章(
                链接信息['链接'],
                链接信息['来源账号'],
                提取套利信息
            )
            
            if 文件路径:
                保存文件列表.append(文件路径)
                print(f"✅ 第 {i} 篇文章采集成功")
            else:
                print(f"❌ 第 {i} 篇文章采集失败")
            
            # 添加延迟
            if i < len(示例链接列表):
                延迟时间 = self.配置['请求延迟']
                print(f"⏳ 等待 {延迟时间} 秒...")
                time.sleep(延迟时间)
        
        print(f"\n🎉 批量采集完成！")
        print(f"✅ 成功采集: {len(保存文件列表)} 篇")
        print(f"❌ 失败: {len(示例链接列表) - len(保存文件列表)} 篇")
        
        return 保存文件列表
    
    def 自定义批量采集(self, 文章链接列表: List[Dict[str, str]], 提取套利信息=False) -> List[str]:
        """
        自定义批量采集
        
        参数:
            文章链接列表: [{'链接': 'url', '来源账号': 'name'}, ...]
            提取套利信息: 是否提取套利信息
            
        返回:
            保存的文件路径列表
        """
        print(f"🚀 开始自定义批量采集 {len(文章链接列表)} 篇文章")
        
        保存文件列表 = []
        
        for i, 链接信息 in enumerate(文章链接列表, 1):
            print(f"\n{'='*60}")
            print(f"📖 采集第 {i}/{len(文章链接列表)} 篇文章")
            
            文件路径 = self.采集单篇文章(
                链接信息['链接'],
                链接信息.get('来源账号', '未知'),
                提取套利信息
            )
            
            if 文件路径:
                保存文件列表.append(文件路径)
                print(f"✅ 第 {i} 篇文章采集成功")
            else:
                print(f"❌ 第 {i} 篇文章采集失败")
            
            # 添加延迟
            if i < len(文章链接列表):
                延迟时间 = self.配置['请求延迟']
                print(f"⏳ 等待 {延迟时间} 秒...")
                time.sleep(延迟时间)
        
        print(f"\n🎉 自定义批量采集完成！")
        print(f"✅ 成功采集: {len(保存文件列表)} 篇")
        print(f"❌ 失败: {len(文章链接列表) - len(保存文件列表)} 篇")
        
        return 保存文件列表
    
    def 显示统计信息(self):
        """显示采集统计信息"""
        try:
            文件列表 = os.listdir(self.采集器.保存目录)
            文章文件 = [f for f in 文件列表 if f.endswith('.md') and not f.endswith('_套利分析.md')]
            套利文件 = [f for f in 文件列表 if f.endswith('_套利分析.md')]
            
            print(f"\n📊 采集统计信息")
            print(f"📁 保存目录: {self.采集器.保存目录}")
            print(f"📄 文章总数: {len(文章文件)}")
            print(f"💡 套利分析: {len(套利文件)}")
            print(f"📦 总文件数: {len(文件列表)}")
            
            if 文章文件:
                print(f"\n📋 最近采集的文章:")
                # 按修改时间排序，显示最新的5篇
                文章文件.sort(key=lambda x: os.path.getmtime(os.path.join(self.采集器.保存目录, x)), reverse=True)
                for i, 文件名 in enumerate(文章文件[:5], 1):
                    print(f"  {i}. {文件名}")
                    
        except Exception as 错误:
            print(f"❌ 获取统计信息失败: {错误}")


def 主函数():
    """主函数"""
    print("🚀 公众号文章采集系统")
    print("=" * 60)
    
    # 显示配置信息
    显示配置信息()
    
    # 创建主采集程序
    主程序 = 主采集程序()
    
    while True:
        print(f"\n{'='*60}")
        print("📋 请选择操作:")
        print("1. 采集单篇文章")
        print("2. 批量采集示例文章")
        print("3. 自定义批量采集")
        print("4. 初始化AI模型（套利信息提取）")
        print("5. 显示采集统计")
        print("6. 显示配置信息")
        print("0. 退出程序")
        
        选择 = input("\n请输入选项 (0-6): ").strip()
        
        if 选择 == "0":
            print("👋 程序退出")
            break
        elif 选择 == "1":
            链接 = input("请输入文章链接: ").strip()
            来源 = input("请输入来源账号 (可选): ").strip() or "未知"
            提取套利 = input("是否提取套利信息? (y/n): ").strip().lower() == 'y'
            
            if 提取套利 and not 主程序.gemini模型:
                主程序.初始化AI模型()
            
            主程序.采集单篇文章(链接, 来源, 提取套利)
            
        elif 选择 == "2":
            提取套利 = input("是否提取套利信息? (y/n): ").strip().lower() == 'y'
            
            if 提取套利 and not 主程序.gemini模型:
                主程序.初始化AI模型()
            
            主程序.批量采集示例文章(提取套利)
            
        elif 选择 == "3":
            print("请输入文章链接，每行一个，输入空行结束:")
            链接列表 = []
            while True:
                链接 = input().strip()
                if not 链接:
                    break
                来源 = input(f"请输入 {链接} 的来源账号: ").strip() or "未知"
                链接列表.append({'链接': 链接, '来源账号': 来源})
            
            if 链接列表:
                提取套利 = input("是否提取套利信息? (y/n): ").strip().lower() == 'y'
                
                if 提取套利 and not 主程序.gemini模型:
                    主程序.初始化AI模型()
                
                主程序.自定义批量采集(链接列表, 提取套利)
            else:
                print("❌ 没有输入任何链接")
                
        elif 选择 == "4":
            主程序.初始化AI模型()
            
        elif 选择 == "5":
            主程序.显示统计信息()
            
        elif 选择 == "6":
            显示配置信息()
            
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as 错误:
        print(f"\n❌ 程序异常: {错误}")
        import traceback
        traceback.print_exc()
