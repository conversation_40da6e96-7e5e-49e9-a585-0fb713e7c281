# 📖 公众号文章采集器使用指南

## 🎯 功能概述

经过简化整合，内容收集器现在提供以下核心功能：

✅ **微信公众号文章采集** - 支持文字和图片内容提取  
✅ **智能文件保存** - 自动保存到 `数据存储/原文章` 目录  
✅ **AI套利信息提取** - 使用Gemini 2.5 Flash智能分析  
✅ **批量采集支持** - 支持多篇文章批量处理  
✅ **配置化管理** - 统一的公众号和采集配置  

## 📁 文件保存位置

**所有采集的文章都保存在：**
```
C:\Users\<USER>\Desktop\公众号全自动\数据存储\原文章\
```

**文件格式：**
- 格式：Markdown (.md)
- 命名：`时间戳_来源账号_标题.md`
- 编码：UTF-8
- 包含：文字内容 + 图片链接 + 元数据

## 🚀 快速使用

### 方法1：运行主采集程序（推荐）

```bash
cd 源代码\内容收集器
python 主采集程序.py
```

**功能菜单：**
1. 采集单篇文章 - 输入链接即可采集
2. 批量采集示例文章 - 采集配置中的示例链接
3. 自定义批量采集 - 自定义多个链接
4. 初始化AI模型 - 启用套利信息提取
5. 显示采集统计 - 查看已采集文章
6. 显示配置信息 - 查看公众号配置

### 方法2：直接使用简化采集器

```bash
cd 源代码\内容收集器
python 简化文章采集器.py
```

这会直接采集示例文章到指定目录。

## 📋 配置管理

### 公众号配置

在 `公众号配置.py` 中管理公众号信息：

```python
公众号配置 = {
    'taotiehai_investment': {
        '名称': '饕餮海投资',
        '描述': '专注跨市场套利、价差套利、可转债套利等投资策略',
        '关键词': ['跨市场套利', '价差套利', '可转债套利'],
        '优先级': 1,
        '是否启用': True,
        '示例链接': ['https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ']
    }
}
```

### 采集配置

```python
采集器配置 = {
    '保存目录': '数据存储/原文章',
    '每次抓取数量': 5,
    '请求延迟': 2,   # 秒
    '超时时间': 30,  # 秒
    '过滤规则': {
        '最小字数': 500,
        '最大字数': 50000
    }
}
```

## 🤖 AI套利信息提取

### 配置要求

1. **设置环境变量：**
   ```bash
   set GEMINI_API_KEY=your_api_key_here
   ```

2. **安装依赖：**
   ```bash
   pip install google-generativeai python-dotenv
   ```

### 使用方法

在主采集程序中选择"初始化AI模型"，然后在采集时选择"是否提取套利信息"。

**输出文件：**
- 原文章：`时间戳_来源账号_标题.md`
- 套利分析：`时间戳_来源账号_标题_套利分析.md`

## 📊 文件结构说明

### 🎯 最终简化的文件结构（只有3个核心文件）

```
内容收集器/
├── 主采集程序.py          # 🎯 主程序入口（推荐使用）
├── 简化文章采集器.py       # 🔧 核心采集功能
├── 提取套利信息.py        # 🤖 AI套利信息提取（可选）
└── 使用指南.md           # 📖 本使用指南

配置文件/
└── 公众号配置.py          # ⚙️ 配置管理（已移动到配置文件夹）
```

**已删除的重复文件：**
- ❌ 基础收集器.py - 抽象基类，功能已整合
- ❌ 微信收集器.py - 功能已整合到简化采集器
- ❌ 收集器管理器.py - 管理功能已整合到主程序
- ❌ README.md - 与使用指南重复

**已移动的配置文件：**
- ✅ 公众号配置.py - 移动到 `配置文件/` 文件夹

### 核心文件说明

- **主采集程序.py** - 主要入口，提供完整的交互界面
- **简化文章采集器.py** - 核心采集逻辑，可独立使用
- **公众号配置.py** - 统一管理所有配置信息
- **提取套利信息.py** - AI分析功能，需要API密钥

## 💡 使用技巧

### 1. 快速采集单篇文章

```python
from 简化文章采集器 import 简化文章采集器

采集器 = 简化文章采集器()
文章数据 = 采集器.采集文章(
    "https://mp.weixin.qq.com/s/xxxxx",
    "饕餮海投资"
)
文件路径 = 采集器.保存文章到文件(文章数据)
```

### 2. 批量采集多篇文章

```python
from 主采集程序 import 主采集程序

主程序 = 主采集程序()
文章列表 = [
    {'链接': 'https://mp.weixin.qq.com/s/xxxxx', '来源账号': '饕餮海投资'},
    {'链接': 'https://mp.weixin.qq.com/s/yyyyy', '来源账号': '鑫爷低风险投资'}
]
保存文件 = 主程序.自定义批量采集(文章列表)
```

### 3. 添加新的公众号

在 `公众号配置.py` 中添加：

```python
'new_account_id': {
    '名称': '新公众号名称',
    '描述': '公众号描述',
    '关键词': ['关键词1', '关键词2'],
    '优先级': 1,
    '是否启用': True,
    '示例链接': ['示例链接']
}
```

## ⚠️ 注意事项

1. **网络环境** - 确保能正常访问微信公众号文章
2. **请求频率** - 默认设置2秒延迟，避免被限制
3. **文件权限** - 确保对保存目录有写入权限
4. **API配额** - Gemini API有使用限制，注意配额

## 🔧 故障排除

### 常见问题

**Q: 文章采集失败？**
A: 检查网络连接和文章链接有效性

**Q: 文件保存失败？**
A: 检查目录权限和磁盘空间

**Q: AI分析不工作？**
A: 检查GEMINI_API_KEY环境变量设置

**Q: 中文文件名乱码？**
A: 系统已设置UTF-8编码，应该不会出现此问题

## 📈 功能优势

### ✅ 相比原版本的改进

1. **简化文件结构** - 减少重复代码，提高维护性
2. **统一保存目录** - 所有文章保存到指定位置
3. **增强错误处理** - 更好的异常处理和用户提示
4. **改进用户界面** - 清晰的菜单和操作提示
5. **优化配置管理** - 集中化的配置文件
6. **完整功能保留** - 保留所有原有功能

### 🎯 核心特性

- **智能采集** - 自动提取文字、图片、元数据
- **格式规范** - 统一的Markdown格式保存
- **批量处理** - 支持多篇文章同时采集
- **AI增强** - 可选的套利信息智能提取
- **配置灵活** - 易于添加新的公众号和调整参数

---

**🎉 现在您可以开始使用简化整合的公众号文章采集器了！**

推荐从运行 `python 主采集程序.py` 开始体验完整功能。
