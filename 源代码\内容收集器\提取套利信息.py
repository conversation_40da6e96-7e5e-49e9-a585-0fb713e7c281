#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取公众号文章中的套利信息
使用Gemini 2.5 Flash专门提取和总结套利相关信息
整合了简化文章采集器的功能

作者: AI助手
日期: 2025-07-27
"""

import os
import google.generativeai as genai
from datetime import datetime
from dotenv import load_dotenv
try:
    from 简化文章采集器 import 简化文章采集器
except ImportError:
    print("❌ 无法导入简化文章采集器，请确保文件在同一目录下")
    简化文章采集器 = None

def 初始化Gemini():
    """初始化Gemini 2.5 Flash API"""
    load_dotenv()
    api_key = os.getenv('GEMINI_API_KEY')

    if not api_key:
        print("❌ 未找到 GEMINI_API_KEY")
        return None

    print(f"🔑 使用API密钥: {api_key[:10]}...{api_key[-4:]}")

    try:
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
        print("✅ Gemini 2.5 Flash API 初始化成功")
        return model
    except Exception as e:
        print(f"❌ Gemini API 初始化失败: {e}")
        return None

def 提取套利信息(model, article_data):
    """使用Gemini 2.5 Flash提取套利相关信息"""
    
    提取提示 = f"""请专门提取以下文章中的套利相关信息，不需要对文章进行评价分析。

## 文章信息
标题：{article_data['标题']}
作者：{article_data['作者']}

## 文章内容：
{article_data['内容']}

请按以下格式提取套利信息：

### 1. 套利机会识别
- 提到的具体套利机会（如A股港股价差、可转债套利、分红套利等）
- 涉及的具体标的名称和代码
- 套利的基本逻辑

### 2. 具体操作策略
- 买入/卖出的具体条件
- 价格区间或估值水平
- 操作时机

### 3. 数据信息
- 文章中提到的具体数据（价格、溢价率、涨幅等）
- 估值水平
- 市场数据

### 4. 风险因素
- 提到的风险点
- 止损条件
- 注意事项

### 5. 时间敏感信息
- 即将发生的事件（如新股上市、转债上市等）
- 时间节点
- 预期表现

请只提取文章中明确提到的信息，不要添加额外的分析或评价。如果某个类别没有相关信息，请标注"无相关信息"。"""

    try:
        print("🔍 正在使用Gemini 2.5 Flash提取套利信息...")
        response = model.generate_content(提取提示)
        return response.text
    except Exception as e:
        return f"❌ 信息提取失败: {e}"

def 生成套利总结(model, 套利信息):
    """生成套利信息的结构化总结"""
    
    总结提示 = f"""基于以下提取的套利信息，请生成一个简洁的结构化总结：

提取的套利信息：
{套利信息}

请按以下JSON格式输出总结：

```json
{{
    "套利机会总数": "数字",
    "主要套利类型": ["类型1", "类型2", "类型3"],
    "重点关注标的": [
        {{
            "标的名称": "名称",
            "标的代码": "代码",
            "套利类型": "类型",
            "关键数据": "数据",
            "操作建议": "建议"
        }}
    ],
    "时间敏感事件": [
        {{
            "事件": "事件描述",
            "时间": "时间",
            "预期": "预期表现"
        }}
    ],
    "关键风险点": ["风险1", "风险2", "风险3"],
    "核心策略": "主要策略总结"
}}
```

请确保输出有效的JSON格式。"""

    try:
        print("📊 正在生成套利信息总结...")
        response = model.generate_content(总结提示)
        return response.text
    except Exception as e:
        return f"❌ 总结生成失败: {e}"

def 主函数():
    """主函数"""
    print("🚀 开始提取公众号文章套利信息")
    print("=" * 60)
    print("🤖 使用模型: Gemini 2.5 Flash")
    print("=" * 60)
    
    # 目标文章URL
    article_url = "https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ"
    
    try:
        # 1. 初始化Gemini 2.5 Flash
        print("🔧 1. 初始化Gemini 2.5 Flash API...")
        model = 初始化Gemini()
        if not model:
            return
        
        # 2. 采集文章内容
        print("\n📥 2. 采集文章内容...")
        if 简化文章采集器 is None:
            print("❌ 简化文章采集器不可用，程序退出")
            return
        采集器 = 简化文章采集器()
        article_data = 采集器.采集文章(article_url, "套利分析")
        
        if not article_data:
            print("❌ 文章采集失败，程序退出")
            return
        
        # 3. 提取套利信息
        print("\n🔍 3. 提取套利信息...")
        套利信息 = 提取套利信息(model, article_data)
        print("✅ 套利信息提取完成")
        
        # 4. 生成结构化总结
        print("\n📊 4. 生成套利总结...")
        套利总结 = 生成套利总结(model, 套利信息)
        print("✅ 套利总结生成完成")
        
        # 5. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细套利信息
        详细信息文件 = f"套利信息提取_{timestamp}.md"
        with open(详细信息文件, 'w', encoding='utf-8') as f:
            f.write(f"# 套利信息提取报告\n\n")
            f.write(f"**提取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**使用模型**: Gemini 2.5 Flash\n")
            f.write(f"**文章链接**: {article_url}\n")
            f.write(f"**文章标题**: {article_data['标题']}\n")
            f.write(f"**文章作者**: {article_data['作者']}\n\n")
            f.write("---\n\n")
            f.write("## 📊 提取的套利信息\n\n")
            f.write(套利信息)
            f.write("\n\n---\n\n")
            f.write("## 📋 结构化总结\n\n")
            f.write(套利总结)
        
        # 6. 显示结果
        print("\n" + "=" * 60)
        print("🎉 套利信息提取完成！")
        print("=" * 60)
        print(f"📄 详细报告保存: {详细信息文件}")
        print(f"📝 文章标题: {article_data['标题']}")
        print(f"👤 文章作者: {article_data['作者']}")
        print(f"📊 文章字数: {article_data['字数']}")
        
        # 显示套利信息预览
        print(f"\n📋 套利信息预览:")
        print("-" * 40)
        print(套利信息[:800] + "..." if len(套利信息) > 800 else 套利信息)
        
        print(f"\n💡 完整套利信息请查看: {详细信息文件}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
