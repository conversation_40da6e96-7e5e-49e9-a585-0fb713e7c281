# -*- coding: utf-8 -*-
"""
简化文章采集器 - 整合所有采集功能

作者: AI助手
日期: 2025-07-27
功能: 简化的公众号文章采集工具，支持真实文章采集和文件保存
"""

import os
import re
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from typing import Dict, Any, List, Optional


class 简化文章采集器:
    """简化的文章采集器"""
    
    def __init__(self, 保存目录: str = None):
        """
        初始化采集器

        参数:
            保存目录: 文章保存目录，如果为None则使用默认目录
        """
        if 保存目录 is None:
            # 默认保存到数据存储/原文章目录
            当前文件目录 = os.path.dirname(os.path.abspath(__file__))
            根目录 = os.path.dirname(os.path.dirname(当前文件目录))
            self.保存目录 = os.path.join(根目录, "数据存储", "原文章")
        else:
            self.保存目录 = 保存目录
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self._创建保存目录()
    
    def _创建保存目录(self):
        """创建文章保存目录"""
        try:
            if not os.path.exists(self.保存目录):
                os.makedirs(self.保存目录)
                print(f"✅ 创建文章保存目录: {self.保存目录}")
        except Exception as 错误:
            print(f"❌ 创建保存目录失败: {错误}")
    
    def 采集文章(self, 文章链接: str, 来源账号: str = "未知") -> Optional[Dict[str, Any]]:
        """
        采集微信文章内容
        
        参数:
            文章链接: 微信文章链接
            来源账号: 来源公众号名称
            
        返回:
            文章数据字典
        """
        print(f"🔍 正在采集文章: {文章链接}")
        
        try:
            response = requests.get(文章链接, headers=self.headers, timeout=30)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取标题
                title_elem = soup.find('h1', class_='rich_media_title')
                标题 = title_elem.get_text().strip() if title_elem else "未知标题"
                
                # 提取作者
                author_elem = soup.find('a', class_='rich_media_meta_link')
                if not author_elem:
                    author_elem = soup.find('span', class_='rich_media_meta_text')
                作者 = author_elem.get_text().strip() if author_elem else 来源账号
                
                # 提取发布时间
                time_elem = soup.find('em', id='publish_time')
                发布时间 = time_elem.get_text().strip() if time_elem else datetime.now().strftime('%Y-%m-%d')
                
                # 提取正文内容
                content_elem = soup.find('div', class_='rich_media_content')
                if content_elem:
                    # 移除脚本和样式
                    for script in content_elem(["script", "style"]):
                        script.decompose()
                    
                    # 获取文本内容
                    内容 = content_elem.get_text(separator='\n', strip=True)
                    # 清理多余的空行
                    内容 = re.sub(r'\n\s*\n', '\n\n', 内容)
                    内容 = re.sub(r'\n{3,}', '\n\n', 内容)
                    
                    # 提取图片
                    图片列表 = []
                    for img in content_elem.find_all('img'):
                        img_src = img.get('data-src') or img.get('src')
                        if img_src and 'http' in img_src:
                            图片列表.append(img_src)
                else:
                    内容 = "无法提取正文内容"
                    图片列表 = []
                
                文章数据 = {
                    '标题': 标题,
                    '内容': 内容,
                    '作者': 作者,
                    '来源账号': 来源账号,
                    '链接': 文章链接,
                    '发布时间': 发布时间,
                    '采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '图片列表': 图片列表,
                    '字数': len(内容),
                    '图片数量': len(图片列表)
                }
                
                print(f"✅ 文章采集成功")
                print(f"📝 标题: {标题}")
                print(f"👤 作者: {作者}")
                print(f"📊 字数: {len(内容)}")
                print(f"🖼️ 图片数量: {len(图片列表)}")
                
                return 文章数据
                
            else:
                print(f"❌ 采集失败，状态码: {response.status_code}")
                return None
                
        except Exception as 错误:
            print(f"❌ 采集文章失败: {错误}")
            return None
    
    def 保存文章到文件(self, 文章数据: Dict[str, Any]) -> Optional[str]:
        """
        保存文章到文件系统
        
        参数:
            文章数据: 文章数据字典
            
        返回:
            保存的文件路径，如果保存失败返回None
        """
        try:
            # 创建文件名（使用时间戳和标题）
            时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S")
            安全标题 = re.sub(r'[<>:"/\\|?*]', '_', 文章数据['标题'])[:50]  # 限制长度并替换非法字符
            来源账号 = re.sub(r'[<>:"/\\|?*]', '_', 文章数据.get('来源账号', '未知'))
            文件名 = f"{时间戳}_{来源账号}_{安全标题}.md"
            文件路径 = os.path.join(self.保存目录, 文件名)
            
            # 准备文章内容
            文章内容 = f"""# {文章数据['标题']}

**作者**: {文章数据.get('作者', '未知')}
**来源**: {文章数据.get('来源账号', '未知')}
**发布时间**: {文章数据.get('发布时间', '未知')}
**采集时间**: {文章数据.get('采集时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}
**原文链接**: {文章数据.get('链接', '')}
**字数**: {文章数据.get('字数', 0)}
**图片数量**: {文章数据.get('图片数量', 0)}

---

## 文章内容

{文章数据.get('内容', '')}

"""

            # 如果有图片，添加图片信息
            图片列表 = 文章数据.get('图片列表', [])
            if 图片列表:
                文章内容 += "\n---\n\n## 文章图片\n\n"
                for i, 图片链接 in enumerate(图片列表, 1):
                    文章内容 += f"{i}. ![图片{i}]({图片链接})\n"
                    文章内容 += f"   链接: {图片链接}\n\n"
            
            文章内容 += f"\n---\n\n**采集工具**: 简化文章采集器\n**采集时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 保存到文件
            with open(文件路径, 'w', encoding='utf-8') as f:
                f.write(文章内容)
            
            print(f"💾 文章已保存到文件: {文件路径}")
            return 文件路径
            
        except Exception as 错误:
            print(f"❌ 保存文章到文件失败: {错误}")
            return None
    
    def 批量采集文章(self, 文章链接列表: List[Dict[str, str]]) -> List[str]:
        """
        批量采集文章
        
        参数:
            文章链接列表: [{'链接': 'url', '来源账号': 'name'}, ...]
            
        返回:
            保存的文件路径列表
        """
        保存文件列表 = []
        
        print(f"🚀 开始批量采集 {len(文章链接列表)} 篇文章")
        
        for i, 文章信息 in enumerate(文章链接列表, 1):
            print(f"\n📖 采集第 {i}/{len(文章链接列表)} 篇文章")
            
            文章数据 = self.采集文章(
                文章信息['链接'], 
                文章信息.get('来源账号', '未知')
            )
            
            if 文章数据:
                文件路径 = self.保存文章到文件(文章数据)
                if 文件路径:
                    保存文件列表.append(文件路径)
            
            # 添加延迟避免请求过快
            if i < len(文章链接列表):
                print("⏳ 等待 2 秒...")
                import time
                time.sleep(2)
        
        print(f"\n🎉 批量采集完成！成功保存 {len(保存文件列表)} 篇文章")
        return 保存文件列表


def 主函数():
    """主函数 - 演示使用"""
    print("🚀 简化文章采集器")
    print("=" * 50)

    # 创建采集器
    采集器 = 简化文章采集器()

    # 从配置文件获取示例链接
    try:
        import sys
        配置文件路径 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "配置文件")
        sys.path.append(配置文件路径)
        from 公众号配置 import 获取示例链接

        示例链接列表 = 获取示例链接()
        if 示例链接列表:
            示例链接 = 示例链接列表[0]['链接']
            来源账号 = 示例链接列表[0]['来源账号']
        else:
            示例链接 = "https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ"
            来源账号 = "饕餮海投资"
    except:
        示例链接 = "https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ"
        来源账号 = "饕餮海投资"

    print("📖 采集示例文章...")
    文章数据 = 采集器.采集文章(示例链接, 来源账号)

    if 文章数据:
        文件路径 = 采集器.保存文章到文件(文章数据)
        if 文件路径:
            print(f"✅ 采集完成！文章已保存到: {文件路径}")
        else:
            print("❌ 文章保存失败")
    else:
        print("❌ 文章采集失败")


if __name__ == "__main__":
    主函数()
