# -*- coding: utf-8 -*-
"""
内容整合器基类

作者: AI助手
日期: 2025-07-27
功能: 定义内容整合器的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

from ..工具模块.日志管理器 import 日志混入类
from ..工具模块.数据库管理器 import 数据库管理器, 文章表, 生成文章表


class 基础整合器(ABC, 日志混入类):
    """内容整合器基类"""
    
    def __init__(self, 配置: Dict[str, Any], 数据库管理器实例: 数据库管理器):
        """
        初始化整合器
        
        参数:
            配置: 整合器配置
            数据库管理器实例: 数据库管理器
        """
        self.配置 = 配置
        self.数据库管理器 = 数据库管理器实例
        self.相似度阈值 = 配置.get('内容改写', {}).get('相似度阈值', 0.3)
        self.最小长度 = 配置.get('文章生成', {}).get('最小长度', 800)
        self.最大长度 = 配置.get('文章生成', {}).get('最大长度', 2000)
    
    async def 整合文章内容(self, 源文章列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        整合文章内容（基础实现）

        参数:
            源文章列表: 源文章数据列表

        返回:
            整合后的文章数据
        """
        if not 源文章列表:
            return {}

        # 基础整合逻辑
        合并关键词 = self.合并关键词(源文章列表)
        生成标题 = self.生成文章标题(合并关键词)

        # 简单的内容整合
        整合内容 = f"""基于{len(源文章列表)}篇源文章的深度分析，我们发现了以下重要观点：

"""

        for i, 文章 in enumerate(源文章列表, 1):
            整合内容 += f"## 观点{i}：{文章.get('标题', '未知标题')}\n\n"
            整合内容 += f"{文章.get('摘要', '暂无摘要')}\n\n"

        整合内容 += """## 综合分析

通过对以上观点的综合分析，我们可以得出以下结论：

1. 市场机会与挑战并存
2. 投资策略需要灵活调整
3. 风险控制至关重要

## 投资建议

基于以上分析，建议投资者：
- 保持理性投资心态
- 分散投资风险
- 关注长期价值

**风险提示**：投资有风险，决策需谨慎。"""

        return {
            '标题': 生成标题,
            '内容': 整合内容,
            '摘要': f'基于{len(源文章列表)}篇文章的综合分析',
            '关键词': 合并关键词,
            '质量评分': 0.75,
            '原创性评分': 0.80
        }
    
    def 选择源文章(self, 主题: str = None, 数量: int = 5, 质量阈值: float = 0.7) -> List[Dict[str, Any]]:
        """
        选择用于整合的源文章
        
        参数:
            主题: 主题关键词
            数量: 选择文章数量
            质量阈值: 质量评分阈值
            
        返回:
            选中的源文章列表
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            # 构建查询条件
            查询 = 会话.query(文章表).filter(
                文章表.状态 == '已分析',
                文章表.质量评分 >= 质量阈值,
                文章表.是否已处理 == True
            )
            
            # 如果指定主题，按关键词过滤
            if 主题:
                查询 = 查询.filter(
                    文章表.关键词.contains(主题)
                )
            
            # 按质量评分降序排列
            源文章 = 查询.order_by(文章表.质量评分.desc()).limit(数量 * 2).all()
            
            会话.close()
            
            # 转换为字典格式
            文章列表 = []
            for 文章 in 源文章[:数量]:
                文章数据 = {
                    'id': 文章.id,
                    '标题': 文章.标题,
                    '内容': 文章.内容,
                    '摘要': 文章.摘要,
                    '作者': 文章.作者,
                    '来源账号': 文章.来源账号,
                    '关键词': json.loads(文章.关键词) if 文章.关键词 else [],
                    '质量评分': 文章.质量评分,
                    '字数': 文章.字数
                }
                文章列表.append(文章数据)
            
            self.日志器.info(f"选择了 {len(文章列表)} 篇源文章用于整合")
            return 文章列表
            
        except Exception as 错误:
            self.日志器.error(f"选择源文章失败: {错误}")
            return []
    
    def 提取核心观点(self, 文章列表: List[Dict[str, Any]]) -> List[str]:
        """
        从文章列表中提取核心观点
        
        参数:
            文章列表: 文章数据列表
            
        返回:
            核心观点列表
        """
        核心观点 = []
        
        for 文章 in 文章列表:
            内容 = 文章.get('内容', '')
            
            # 提取包含关键词的句子作为核心观点
            关键词 = 文章.get('关键词', [])
            句子列表 = [句.strip() for 句 in 内容.split('。') if 句.strip()]
            
            for 句子 in 句子列表:
                if len(句子) > 20 and any(关键词 in 句子 for 关键词 in 关键词[:3]):
                    核心观点.append(句子)
                    if len(核心观点) >= 10:  # 限制观点数量
                        break
        
        return 核心观点[:10]
    
    def 合并关键词(self, 文章列表: List[Dict[str, Any]]) -> List[str]:
        """
        合并所有文章的关键词
        
        参数:
            文章列表: 文章数据列表
            
        返回:
            合并后的关键词列表
        """
        关键词统计 = {}
        
        for 文章 in 文章列表:
            关键词列表 = 文章.get('关键词', [])
            for 关键词 in 关键词列表:
                关键词统计[关键词] = 关键词统计.get(关键词, 0) + 1
        
        # 按频率排序，返回前10个
        排序关键词 = sorted(关键词统计.items(), key=lambda x: x[1], reverse=True)
        return [关键词 for 关键词, 频率 in 排序关键词[:10]]
    
    def 生成文章标题(self, 关键词列表: List[str], 主题: str = None) -> str:
        """
        生成文章标题
        
        参数:
            关键词列表: 关键词列表
            主题: 主题词
            
        返回:
            生成的标题
        """
        if not 关键词列表:
            return "深度分析：行业发展趋势与未来展望"
        
        主要关键词 = 关键词列表[0] if 关键词列表 else "行业发展"
        
        标题模板 = [
            f"深度解析：{主要关键词}的发展趋势与机遇",
            f"{主要关键词}行业观察：现状分析与未来展望",
            f"全面解读{主要关键词}：从理论到实践的思考",
            f"{主要关键词}发展报告：关键趋势与战略建议",
            f"探索{主要关键词}：创新应用与价值实现"
        ]
        
        # 简单选择第一个模板
        return 标题模板[0]
    
    def 保存生成文章(self, 文章数据: Dict[str, Any], 源文章ID列表: List[int]) -> Optional[int]:
        """
        保存生成的文章到数据库
        
        参数:
            文章数据: 生成的文章数据
            源文章ID列表: 源文章ID列表
            
        返回:
            生成文章ID，如果保存失败返回None
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            # 创建生成文章记录
            生成文章 = 生成文章表(
                标题=文章数据['标题'],
                内容=文章数据['内容'],
                摘要=文章数据.get('摘要', ''),
                源文章列表=json.dumps(源文章ID列表),
                关键词=json.dumps(文章数据.get('关键词', []), ensure_ascii=False),
                质量评分=文章数据.get('质量评分', 0.0),
                原创性评分=文章数据.get('原创性评分', 0.0)
            )
            
            会话.add(生成文章)
            会话.commit()
            
            文章ID = 生成文章.id
            会话.close()
            
            self.日志器.info(f"保存生成文章成功: {文章数据['标题']} (ID: {文章ID})")
            return 文章ID
            
        except Exception as 错误:
            self.日志器.error(f"保存生成文章失败: {错误}")
            if '会话' in locals():
                会话.rollback()
                会话.close()
            return None
    
    async def 整合并生成文章(self, 主题: str = None, 源文章数量: int = 5) -> Optional[Dict[str, Any]]:
        """
        整合并生成新文章
        
        参数:
            主题: 主题关键词
            源文章数量: 使用的源文章数量
            
        返回:
            生成的文章数据
        """
        try:
            # 1. 选择源文章
            源文章列表 = self.选择源文章(主题, 源文章数量)
            if not 源文章列表:
                self.日志器.warning("没有找到合适的源文章")
                return None
            
            # 2. 整合内容
            整合结果 = await self.整合文章内容(源文章列表)
            if not 整合结果:
                self.日志器.error("文章整合失败")
                return None
            
            # 3. 保存生成文章
            源文章ID列表 = [文章['id'] for 文章 in 源文章列表]
            文章ID = self.保存生成文章(整合结果, 源文章ID列表)
            
            if 文章ID:
                整合结果['生成文章ID'] = 文章ID
                整合结果['源文章数量'] = len(源文章列表)
                整合结果['生成时间'] = datetime.now().isoformat()
                return 整合结果
            else:
                return None
                
        except Exception as 错误:
            self.日志器.error(f"整合并生成文章失败: {错误}")
            return None
    
    def 获取整合统计(self) -> Dict[str, Any]:
        """
        获取整合统计信息
        
        返回:
            统计信息
        """
        try:
            会话 = self.数据库管理器.获取会话()
            
            统计信息 = {
                '生成文章总数': 会话.query(生成文章表).count(),
                '待审核文章数': 会话.query(生成文章表).filter(生成文章表.状态 == '已生成').count(),
                '已发布文章数': 会话.query(生成文章表).filter(生成文章表.是否已发布 == True).count(),
                '平均质量评分': 0.0,
                '平均原创性评分': 0.0
            }
            
            # 计算平均评分
            所有生成文章 = 会话.query(生成文章表).all()
            if 所有生成文章:
                总质量评分 = sum(文章.质量评分 for 文章 in 所有生成文章)
                总原创性评分 = sum(文章.原创性评分 for 文章 in 所有生成文章)
                统计信息['平均质量评分'] = 总质量评分 / len(所有生成文章)
                统计信息['平均原创性评分'] = 总原创性评分 / len(所有生成文章)
            
            会话.close()
            return 统计信息
            
        except Exception as 错误:
            self.日志器.error(f"获取整合统计失败: {错误}")
            return {}

    async def 生成套利策略文章(self, 套利分析结果列表: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        基于套利分析结果生成原创策略文章

        参数:
            套利分析结果列表: 套利分析结果

        返回:
            生成的原创文章
        """
        try:
            # 筛选有价值的套利机会
            有效套利机会 = []
            for 分析结果 in 套利分析结果列表:
                实用性评分 = 分析结果.get('实用性评分', 0)
                # 确保评分是数字类型
                if isinstance(实用性评分, str):
                    try:
                        实用性评分 = float(实用性评分)
                    except:
                        实用性评分 = 0

                if 分析结果.get('包含套利信息') and 实用性评分 >= 5:
                    有效套利机会.extend(分析结果.get('套利机会列表', []))

            if not 有效套利机会:
                self.日志器.warning("没有发现有价值的套利机会")
                return None

            # 生成原创文章
            文章内容 = await self._生成套利文章内容(有效套利机会)

            if 文章内容:
                return {
                    '标题': 文章内容['标题'],
                    '内容': 文章内容['正文'],
                    '摘要': 文章内容['摘要'],
                    '关键词': 文章内容['关键词'],
                    '套利机会数量': len(有效套利机会),
                    '质量评分': 0.85,  # 套利文章通常质量较高
                    '原创性评分': 0.90,  # 基于分析生成，原创性高
                    '文章类型': '套利策略分析'
                }

            return None

        except Exception as 错误:
            self.日志器.error(f"生成套利策略文章失败: {错误}")
            return None
