# -*- coding: utf-8 -*-
"""
数据库管理器

作者: AI助手
日期: 2025-07-27
功能: 负责数据库连接、表结构管理和数据操作
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

Base = declarative_base()


class 文章表(Base):
    """文章数据模型"""
    __tablename__ = '文章表'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    标题 = Column(String(500), nullable=False)
    内容 = Column(Text, nullable=False)
    摘要 = Column(Text)
    作者 = Column(String(100))
    来源账号 = Column(String(100), nullable=False)
    来源链接 = Column(String(500))
    发布时间 = Column(DateTime)
    收集时间 = Column(DateTime, default=datetime.now)
    字数 = Column(Integer)
    阅读数 = Column(Integer, default=0)
    点赞数 = Column(Integer, default=0)
    关键词 = Column(Text)  # JSON格式存储关键词
    质量评分 = Column(Float, default=0.0)
    状态 = Column(String(20), default='已收集')  # 已收集, 已分析, 已整合, 已发布
    是否已处理 = Column(Boolean, default=False)


class 生成文章表(Base):
    """生成文章数据模型"""
    __tablename__ = '生成文章表'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    标题 = Column(String(500), nullable=False)
    内容 = Column(Text, nullable=False)
    摘要 = Column(Text)
    源文章列表 = Column(Text)  # JSON格式存储源文章ID列表
    关键词 = Column(Text)  # JSON格式存储关键词
    质量评分 = Column(Float, default=0.0)
    原创性评分 = Column(Float, default=0.0)
    创建时间 = Column(DateTime, default=datetime.now)
    发布时间 = Column(DateTime)
    状态 = Column(String(20), default='已生成')  # 已生成, 已审核, 已发布
    是否已发布 = Column(Boolean, default=False)


class 源账号表(Base):
    """源公众号数据模型"""
    __tablename__ = '源账号表'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    名称 = Column(String(100), nullable=False)
    账号ID = Column(String(100), unique=True, nullable=False)
    描述 = Column(Text)
    关键词 = Column(Text)  # JSON格式存储关键词
    优先级 = Column(Integer, default=1)
    最后抓取时间 = Column(DateTime)
    文章总数 = Column(Integer, default=0)
    是否启用 = Column(Boolean, default=True)
    创建时间 = Column(DateTime, default=datetime.now)


class 系统日志表(Base):
    """系统日志数据模型"""
    __tablename__ = '系统日志表'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    级别 = Column(String(20), nullable=False)
    模块 = Column(String(50), nullable=False)
    消息 = Column(Text, nullable=False)
    详情 = Column(Text)  # JSON格式存储详细信息
    创建时间 = Column(DateTime, default=datetime.now)


class 数据库管理器:
    """数据库管理器类"""
    
    def __init__(self, 数据库配置: Dict[str, Any]):
        """
        初始化数据库管理器
        
        参数:
            数据库配置: 数据库配置字典
        """
        self.配置 = 数据库配置
        self.引擎 = None
        self.会话工厂 = None
        self._设置数据库()
    
    def _设置数据库(self):
        """设置数据库连接"""
        数据库类型 = self.配置.get('类型', 'sqlite')
        
        if 数据库类型 == 'sqlite':
            数据库路径 = self.配置['sqlite配置']['文件路径']
            # 确保数据库目录存在
            Path(数据库路径).parent.mkdir(parents=True, exist_ok=True)
            数据库连接字符串 = f"sqlite:///{数据库路径}"
        elif 数据库类型 == 'postgresql':
            pg配置 = self.配置['postgresql配置']
            数据库连接字符串 = (
                f"postgresql://{pg配置['用户名']}:{pg配置['密码']}"
                f"@{pg配置['主机']}:{pg配置['端口']}/{pg配置['数据库名']}"
            )
        else:
            raise ValueError(f"不支持的数据库类型: {数据库类型}")
        
        self.引擎 = create_engine(数据库连接字符串, echo=False)
        self.会话工厂 = sessionmaker(autocommit=False, autoflush=False, bind=self.引擎)
        
        # 创建表
        Base.metadata.create_all(bind=self.引擎)
    
    def 获取会话(self) -> Session:
        """获取数据库会话"""
        return self.会话工厂()
    
    def 关闭连接(self):
        """关闭数据库连接"""
        if self.引擎:
            self.引擎.dispose()
    
    def 初始化数据库(self):
        """初始化数据库表结构"""
        try:
            Base.metadata.create_all(bind=self.引擎)
            return True
        except Exception as 错误:
            print(f"初始化数据库失败: {错误}")
            return False
    
    def 清空表数据(self, 表名: str = None):
        """
        清空表数据（谨慎使用）
        
        参数:
            表名: 要清空的表名，如果为None则清空所有表
        """
        会话 = self.获取会话()
        try:
            if 表名:
                if 表名 == '文章表':
                    会话.query(文章表).delete()
                elif 表名 == '生成文章表':
                    会话.query(生成文章表).delete()
                elif 表名 == '源账号表':
                    会话.query(源账号表).delete()
                elif 表名 == '系统日志表':
                    会话.query(系统日志表).delete()
            else:
                # 清空所有表
                会话.query(系统日志表).delete()
                会话.query(生成文章表).delete()
                会话.query(文章表).delete()
                会话.query(源账号表).delete()
            
            会话.commit()
            return True
        except Exception as 错误:
            会话.rollback()
            print(f"清空表数据失败: {错误}")
            return False
        finally:
            会话.close()
    
    def 获取统计信息(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        会话 = self.获取会话()
        try:
            统计信息 = {
                '文章总数': 会话.query(文章表).count(),
                '生成文章数': 会话.query(生成文章表).count(),
                '源账号数': 会话.query(源账号表).count(),
                '日志条数': 会话.query(系统日志表).count(),
                '已处理文章数': 会话.query(文章表).filter(文章表.是否已处理 == True).count(),
                '已发布文章数': 会话.query(生成文章表).filter(生成文章表.是否已发布 == True).count()
            }
            return 统计信息
        except Exception as 错误:
            print(f"获取统计信息失败: {错误}")
            return {}
        finally:
            会话.close()


def 初始化数据库():
    """独立的数据库初始化函数"""
    from .配置加载器 import 配置加载器
    
    try:
        配置加载器实例 = 配置加载器()
        系统配置 = 配置加载器实例.加载配置()
        
        数据库管理器实例 = 数据库管理器(系统配置['数据库'])
        
        if 数据库管理器实例.初始化数据库():
            print("✅ 数据库初始化成功")
            return True
        else:
            print("❌ 数据库初始化失败")
            return False
            
    except Exception as 错误:
        print(f"❌ 数据库初始化异常: {错误}")
        return False


if __name__ == "__main__":
    # 直接运行此文件时初始化数据库
    初始化数据库()
