# -*- coding: utf-8 -*-
"""
日志管理器

作者: AI助手
日期: 2025-07-27
功能: 负责系统日志的配置和管理
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def 设置日志系统(
    日志级别: str = "INFO",
    日志文件: Optional[str] = None,
    最大文件大小: str = "10MB",
    备份文件数: int = 5
) -> logger:
    """
    设置日志系统
    
    参数:
        日志级别: 日志级别
        日志文件: 日志文件路径
        最大文件大小: 最大文件大小
        备份文件数: 备份文件数量
        
    返回:
        配置好的logger实例
    """
    # 移除默认的控制台处理器
    logger.remove()
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        level=日志级别,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 添加文件处理器
    if 日志文件:
        # 确保日志目录存在
        日志路径 = Path(日志文件)
        日志路径.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            日志文件,
            level=日志级别,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation=最大文件大小,
            retention=备份文件数,
            compression="zip",
            encoding="utf-8"
        )
    
    return logger


def 获取日志器(名称: str = None) -> logger:
    """
    获取logger实例
    
    参数:
        名称: logger名称
        
    返回:
        logger实例
    """
    if 名称:
        return logger.bind(name=名称)
    return logger


class 日志混入类:
    """日志混入类，为其他类提供日志功能"""
    
    @property
    def 日志器(self):
        """获取当前类的logger"""
        return 获取日志器(self.__class__.__name__)
