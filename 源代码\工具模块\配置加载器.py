# -*- coding: utf-8 -*-
"""
配置文件加载器

作者: AI助手
日期: 2025-07-27
功能: 负责加载和管理系统配置文件
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv


class 配置加载器:
    """配置文件加载器类"""
    
    def __init__(self, 配置文件路径: str = None):
        """
        初始化配置加载器
        
        参数:
            配置文件路径: 配置文件的路径，默认为 配置文件/系统配置.yaml
        """
        self.项目根目录 = Path(__file__).parent.parent.parent
        self.配置文件路径 = 配置文件路径 or self.项目根目录 / "配置文件" / "系统配置.yaml"
        self.环境变量文件路径 = self.项目根目录 / ".env"
        
        # 加载环境变量
        if self.环境变量文件路径.exists():
            load_dotenv(self.环境变量文件路径)
    
    def 加载配置(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        返回:
            配置字典
        """
        try:
            with open(self.配置文件路径, 'r', encoding='utf-8') as 文件:
                配置数据 = yaml.safe_load(文件)
            
            # 用环境变量覆盖配置
            配置数据 = self._用环境变量覆盖配置(配置数据)
            
            return 配置数据
            
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件不存在: {self.配置文件路径}")
        except yaml.YAMLError as 错误:
            raise ValueError(f"配置文件格式错误: {错误}")
    
    def _用环境变量覆盖配置(self, 配置数据: Dict[str, Any]) -> Dict[str, Any]:
        """
        用环境变量覆盖配置
        
        参数:
            配置数据: 原始配置字典
            
        返回:
            更新后的配置字典
        """
        # 微信配置
        if os.getenv('WECHAT_APP_ID'):
            配置数据['微信配置']['目标账号']['应用ID'] = os.getenv('WECHAT_APP_ID')
        if os.getenv('WECHAT_APP_SECRET'):
            配置数据['微信配置']['目标账号']['应用密钥'] = os.getenv('WECHAT_APP_SECRET')
        
        # AI服务配置
        if os.getenv('GEMINI_API_KEY'):
            配置数据['内容分析']['AI模型']['API密钥'] = os.getenv('GEMINI_API_KEY')
        if os.getenv('OPENAI_API_KEY'):
            配置数据['内容分析']['AI模型']['API密钥'] = os.getenv('OPENAI_API_KEY')
        
        # 数据库配置
        if os.getenv('DATABASE_URL'):
            配置数据['数据库']['连接字符串'] = os.getenv('DATABASE_URL')
        
        # 加密密钥
        if os.getenv('ENCRYPTION_KEY'):
            配置数据['安全设置']['加密密钥'] = os.getenv('ENCRYPTION_KEY')
        
        # 邮件配置
        if os.getenv('EMAIL_USERNAME'):
            配置数据['通知设置']['邮件通知']['用户名'] = os.getenv('EMAIL_USERNAME')
        if os.getenv('EMAIL_PASSWORD'):
            配置数据['通知设置']['邮件通知']['密码'] = os.getenv('EMAIL_PASSWORD')
        
        # Webhook配置
        if os.getenv('WEBHOOK_URL'):
            配置数据['通知设置']['Webhook通知']['网址'] = os.getenv('WEBHOOK_URL')
        
        # 系统配置
        if os.getenv('DEBUG'):
            配置数据['API服务']['调试模式'] = os.getenv('DEBUG').lower() == 'true'
        if os.getenv('LOG_LEVEL'):
            配置数据['系统管理']['日志设置']['日志级别'] = os.getenv('LOG_LEVEL')
        if os.getenv('API_HOST'):
            配置数据['API服务']['主机'] = os.getenv('API_HOST')
        if os.getenv('API_PORT'):
            配置数据['API服务']['端口'] = int(os.getenv('API_PORT'))
        
        return 配置数据
    
    def 保存配置(self, 配置数据: Dict[str, Any]) -> None:
        """
        保存配置文件
        
        参数:
            配置数据: 配置字典
        """
        try:
            with open(self.配置文件路径, 'w', encoding='utf-8') as 文件:
                yaml.dump(配置数据, 文件, default_flow_style=False, allow_unicode=True)
        except Exception as 错误:
            raise ValueError(f"保存配置文件失败: {错误}")
    
    def 获取数据目录(self) -> Path:
        """获取数据存储目录路径"""
        return self.项目根目录 / "数据存储"
    
    def 获取日志目录(self) -> Path:
        """获取日志文件目录路径"""
        return self.项目根目录 / "日志文件"
    
    def 确保目录存在(self) -> None:
        """确保必要的目录存在"""
        目录列表 = [
            self.获取数据目录(),
            self.获取日志目录(),
            self.项目根目录 / "数据存储" / "备份文件"
        ]
        
        for 目录 in 目录列表:
            目录.mkdir(parents=True, exist_ok=True)
