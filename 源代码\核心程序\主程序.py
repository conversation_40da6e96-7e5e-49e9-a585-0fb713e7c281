#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号内容自动化工作流系统
主程序入口文件

作者: AI助手
日期: 2025-07-27
版本: 1.0.0
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
项目根目录 = Path(__file__).parent
sys.path.insert(0, str(项目根目录))

from 源代码.系统管理器.系统管理器 import 系统管理器
from 源代码.工具模块.日志管理器 import 设置日志系统
from 源代码.工具模块.配置加载器 import 配置加载器


def 主函数():
    """主程序入口函数"""
    try:
        # 设置日志系统
        日志器 = 设置日志系统()
        日志器.info("=== 微信公众号自动化系统启动 ===")
        
        # 加载配置文件
        配置加载器实例 = 配置加载器()
        系统配置 = 配置加载器实例.加载配置()
        
        # 初始化系统管理器
        系统管理器实例 = 系统管理器(系统配置)
        
        # 启动系统
        日志器.info("正在启动系统管理器...")
        系统管理器实例.启动系统()
        
        日志器.info("系统启动成功！")
        日志器.info("按 Ctrl+C 停止系统")
        
        # 保持程序运行
        try:
            while True:
                asyncio.sleep(1)
        except KeyboardInterrupt:
            日志器.info("接收到停止信号，正在关闭系统...")
            系统管理器实例.停止系统()
            日志器.info("系统已安全关闭")
            
    except Exception as 错误:
        print(f"系统启动失败: {错误}")
        sys.exit(1)


if __name__ == "__main__":
    主函数()
