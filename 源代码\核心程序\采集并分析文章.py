#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采集并分析指定公众号文章
使用多种方法采集文章内容，然后用Gemini 2.5 Flash进行深度分析

作者: AI助手
日期: 2025-07-27
"""

import os
import json
import asyncio
import aiohttp
import requests
from bs4 import BeautifulSoup
import google.generativeai as genai
from datetime import datetime
from dotenv import load_dotenv
import re

def 初始化Gemini():
    """初始化Gemini API"""
    load_dotenv()
    api_key = os.getenv('GEMINI_API_KEY')
    
    if not api_key:
        print("❌ 未找到 GEMINI_API_KEY")
        return None
    
    print(f"🔑 API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    try:
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-flash')
        print("✅ Gemini API 初始化成功")
        return model
    except Exception as e:
        print(f"❌ Gemini API 初始化失败: {e}")
        return None

def 采集微信文章(url):
    """尝试采集微信文章内容"""
    print(f"🔍 正在采集文章: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取标题
            title_elem = soup.find('h1', class_='rich_media_title')
            title = title_elem.get_text().strip() if title_elem else "未知标题"
            
            # 提取作者
            author_elem = soup.find('a', class_='rich_media_meta_link')
            if not author_elem:
                author_elem = soup.find('span', class_='rich_media_meta_text')
            author = author_elem.get_text().strip() if author_elem else "未知作者"
            
            # 提取发布时间
            time_elem = soup.find('em', id='publish_time')
            if not time_elem:
                time_elem = soup.find('span', class_='rich_media_meta_text')
            publish_time = time_elem.get_text().strip() if time_elem else "未知时间"
            
            # 提取正文内容
            content_elem = soup.find('div', class_='rich_media_content')
            if content_elem:
                # 移除脚本和样式
                for script in content_elem(["script", "style"]):
                    script.decompose()
                
                # 获取文本内容
                content = content_elem.get_text(separator='\n', strip=True)
                # 清理多余的空行
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = re.sub(r'\n{3,}', '\n\n', content)
            else:
                content = "无法提取正文内容"
            
            article_data = {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'content': content,
                'url': url,
                'word_count': len(content),
                'status': 'success'
            }
            
            print(f"✅ 文章采集成功")
            print(f"📝 标题: {title}")
            print(f"👤 作者: {author}")
            print(f"📅 发布时间: {publish_time}")
            print(f"📊 字数: {len(content)}")
            
            return article_data
            
        else:
            print(f"❌ 采集失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 采集过程中出错: {e}")
        return None

def 保存文章内容(article_data, filename):
    """保存文章内容为可读文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# {article_data['title']}\n\n")
            f.write(f"**作者**: {article_data['author']}\n")
            f.write(f"**发布时间**: {article_data['publish_time']}\n")
            f.write(f"**文章链接**: {article_data['url']}\n")
            f.write(f"**字数**: {article_data['word_count']}\n\n")
            f.write("---\n\n")
            f.write(article_data['content'])
        
        print(f"💾 文章内容已保存: {filename}")
        return True
    except Exception as e:
        print(f"❌ 保存文章失败: {e}")
        return False

def 深度分析文章(model, article_data):
    """使用Gemini 2.5 Flash深度分析文章"""
    
    分析提示 = f"""请作为一名资深的投资分析师和金融内容专家，对以下公众号文章进行全面深度分析：

## 文章信息
- 标题：{article_data['title']}
- 作者：{article_data['author']}
- 发布时间：{article_data['publish_time']}
- 字数：{article_data['word_count']}

## 文章内容：
{article_data['content']}

请从以下维度进行详细分析：

### 1. 文章概览
- 主题定位和核心观点
- 目标读者群体
- 文章类型（分析、教程、观点等）

### 2. 内容结构分析
- 标题吸引力和准确性评估
- 逻辑结构是否清晰
- 段落组织是否合理
- 开头、主体、结尾的效果

### 3. 投资策略专业性分析
- 提到的投资策略是否可行
- 分析方法是否专业严谨
- 数据支撑是否充分
- 风险提示是否到位

### 4. 金融专业性评估
- 金融术语使用是否准确
- 投资逻辑是否合理
- 是否存在误导性表述
- 专业深度如何

### 5. 实用性和可操作性
- 对读者的实际价值
- 操作建议是否具体
- 是否提供了可执行的方案
- 适用性如何

### 6. 合规性检查
- 是否存在过度承诺收益
- 风险提示是否充分
- 是否有违规宣传
- 免责声明情况

### 7. 写作质量评估
- 语言表达是否流畅
- 逻辑是否严密
- 可读性如何
- 专业性与通俗性的平衡

### 8. 市场时机和相关性
- 内容是否符合当前市场环境
- 时效性如何
- 是否抓住了市场热点
- 前瞻性如何

### 9. 内容价值评分（1-10分）
请为以下方面打分：
- 专业性：
- 实用性：
- 可读性：
- 合规性：
- 创新性：
- 时效性：
- 综合评分：

### 10. 具体改进建议
- 内容方面需要改进的地方
- 结构优化建议
- 专业性提升建议
- 风险提示完善建议

请提供详细、客观、专业的分析意见，重点关注投资内容的准确性和合规性。"""

    try:
        print("🔍 正在使用Gemini 2.5 Flash进行深度分析...")
        response = model.generate_content(分析提示)
        return response.text
    except Exception as e:
        return f"❌ 分析失败: {e}"

def 生成套利策略建议(model, article_data, analysis_result):
    """基于文章内容生成套利策略优化建议"""
    
    策略提示 = f"""基于以下文章内容和分析结果，请生成专业的套利策略优化建议：

## 原文章信息
标题：{article_data['title']}
内容：{article_data['content'][:2000]}...

## 分析结果
{analysis_result}

请提供以下方面的专业建议：

### 1. 套利策略完善建议
- 如何提升策略的可操作性
- 如何完善风险控制措施
- 如何优化收益预期设定
- 如何增强策略的时效性

### 2. 数据支撑优化
- 需要补充哪些关键数据
- 如何提供更有说服力的案例
- 如何增加量化分析内容
- 如何提升数据的时效性

### 3. 风险管理强化
- 如何完善风险提示
- 如何量化潜在风险
- 如何提供风险应对方案
- 如何避免误导性表述

### 4. 合规性提升
- 如何符合金融内容监管要求
- 如何完善免责声明
- 如何平衡收益宣传和风险提示
- 如何避免违规表述

### 5. 读者价值提升
- 如何增强内容的实用性
- 如何提供更具体的操作指导
- 如何帮助读者建立正确的投资理念
- 如何提升内容的教育价值

### 6. 后续内容规划
- 相关主题的深度挖掘建议
- 系列文章的规划思路
- 读者关注点的进一步探索
- 差异化内容策略

请提供具体、专业、可执行的优化建议。"""

    try:
        print("📝 正在生成套利策略优化建议...")
        response = model.generate_content(策略提示)
        return response.text
    except Exception as e:
        return f"❌ 策略建议生成失败: {e}"

def 主函数():
    """主函数"""
    print("🚀 开始采集并分析公众号文章")
    print("=" * 60)
    
    # 目标文章URL
    article_url = "https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ"
    
    try:
        # 1. 初始化Gemini
        print("🔧 1. 初始化Gemini API...")
        model = 初始化Gemini()
        if not model:
            return
        
        # 2. 采集文章内容
        print("\n📥 2. 采集文章内容...")
        article_data = 采集微信文章(article_url)
        
        if not article_data:
            print("❌ 文章采集失败，程序退出")
            return
        
        # 3. 保存原文章内容
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        article_filename = f"原始文章_{timestamp}.md"
        
        print("\n💾 3. 保存文章内容...")
        if not 保存文章内容(article_data, article_filename):
            return
        
        # 4. 深度分析文章
        print("\n🔍 4. 深度分析文章...")
        analysis_result = 深度分析文章(model, article_data)
        print("✅ 文章分析完成")
        
        # 5. 生成套利策略建议
        print("\n📝 5. 生成套利策略优化建议...")
        strategy_suggestions = 生成套利策略建议(model, article_data, analysis_result)
        print("✅ 策略建议生成完成")
        
        # 6. 保存分析结果
        analysis_filename = f"文章深度分析_{timestamp}.md"
        
        print(f"\n💾 6. 保存分析结果...")
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            f.write(f"# 公众号文章深度分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**文章链接**: {article_url}\n")
            f.write(f"**原文保存**: {article_filename}\n\n")
            f.write("---\n\n")
            f.write("## 📊 深度分析结果\n\n")
            f.write(analysis_result)
            f.write("\n\n---\n\n")
            f.write("## 💡 套利策略优化建议\n\n")
            f.write(strategy_suggestions)
        
        # 7. 显示结果摘要
        print("\n" + "=" * 60)
        print("🎉 采集和分析完成！")
        print("=" * 60)
        print(f"📄 原文章保存: {article_filename}")
        print(f"📊 分析报告保存: {analysis_filename}")
        print(f"📝 文章标题: {article_data['title']}")
        print(f"👤 文章作者: {article_data['author']}")
        print(f"📊 文章字数: {article_data['word_count']}")
        
        # 显示分析结果预览
        print(f"\n📋 分析结果预览:")
        print("-" * 40)
        print(analysis_result[:500] + "..." if len(analysis_result) > 500 else analysis_result)
        
        print(f"\n💡 完整分析报告请查看: {analysis_filename}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        主函数()
    except KeyboardInterrupt:
        print("\n❌ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
