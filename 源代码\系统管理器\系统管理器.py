# -*- coding: utf-8 -*-
"""
系统管理器

作者: AI助手
日期: 2025-07-27
功能: 统一管理和协调所有功能模块
"""

import asyncio
from typing import Dict, Any
from datetime import datetime

from ..工具模块.日志管理器 import 日志混入类, 设置日志系统
from ..工具模块.数据库管理器 import 数据库管理器
from ..内容收集器.收集器管理器 import 收集器管理器
from ..内容分析器.分析器管理器 import 分析器管理器


class 系统管理器(日志混入类):
    """系统管理器，统一管理所有功能模块"""
    
    def __init__(self, 配置: Dict[str, Any]):
        """
        初始化系统管理器
        
        参数:
            配置: 系统配置
        """
        self.配置 = 配置
        self.运行状态 = False
        
        # 设置日志系统
        日志配置 = 配置.get('系统管理', {}).get('日志设置', {})
        设置日志系统(
            日志级别=日志配置.get('日志级别', 'INFO'),
            日志文件=日志配置.get('日志文件', '日志文件/系统日志.log'),
            最大文件大小=日志配置.get('最大文件大小', '10MB'),
            备份文件数=日志配置.get('备份文件数', 5)
        )
        
        # 初始化数据库管理器
        self.数据库管理器 = 数据库管理器(配置['数据库'])
        
        # 初始化各功能模块
        self.收集器管理器 = 收集器管理器(配置, self.数据库管理器)
        self.分析器管理器 = 分析器管理器(配置, self.数据库管理器)
        
        # 任务列表
        self.运行任务 = []
        
        self.日志器.info("系统管理器初始化完成")
    
    def 启动系统(self):
        """启动系统"""
        try:
            self.日志器.info("正在启动微信公众号自动化系统...")
            
            # 确保数据库初始化
            if not self.数据库管理器.初始化数据库():
                raise Exception("数据库初始化失败")
            
            self.运行状态 = True
            
            # 启动异步任务循环
            asyncio.run(self._运行主循环())
            
        except Exception as 错误:
            self.日志器.error(f"系统启动失败: {错误}")
            raise
    
    def 停止系统(self):
        """停止系统"""
        self.日志器.info("正在停止系统...")
        self.运行状态 = False
        
        # 取消所有运行中的任务
        for 任务 in self.运行任务:
            if not 任务.done():
                任务.cancel()
        
        # 关闭数据库连接
        self.数据库管理器.关闭连接()
        
        self.日志器.info("系统已停止")
    
    async def _运行主循环(self):
        """运行主循环"""
        try:
            self.日志器.info("系统主循环启动")
            
            # 启动各个模块的定时任务
            收集任务 = asyncio.create_task(self._运行收集任务())
            分析任务 = asyncio.create_task(self._运行分析任务())
            监控任务 = asyncio.create_task(self._运行监控任务())
            
            self.运行任务.extend([收集任务, 分析任务, 监控任务])
            
            # 等待所有任务完成或系统停止
            while self.运行状态:
                await asyncio.sleep(1)
                
                # 检查任务状态
                for 任务 in self.运行任务:
                    if 任务.done() and not 任务.cancelled():
                        异常 = 任务.exception()
                        if 异常:
                            self.日志器.error(f"任务异常: {异常}")
            
        except Exception as 错误:
            self.日志器.error(f"主循环异常: {错误}")
        finally:
            # 清理资源
            for 任务 in self.运行任务:
                if not 任务.done():
                    任务.cancel()
    
    async def _运行收集任务(self):
        """运行内容收集任务"""
        收集间隔 = self.配置.get('内容收集', {}).get('抓取间隔', 60)
        
        while self.运行状态:
            try:
                self.日志器.info("开始执行内容收集任务")
                
                # 执行内容收集
                收集结果 = await self.收集器管理器.收集所有源内容()
                
                总收集数 = sum(收集结果.values())
                self.日志器.info(f"内容收集完成，共收集 {总收集数} 篇文章")
                
                # 等待下次执行
                await asyncio.sleep(收集间隔 * 60)
                
            except asyncio.CancelledError:
                break
            except Exception as 错误:
                self.日志器.error(f"收集任务异常: {错误}")
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    async def _运行分析任务(self):
        """运行内容分析任务"""
        分析间隔 = self.配置.get('内容分析', {}).get('分析间隔', 30)
        
        while self.运行状态:
            try:
                self.日志器.info("开始执行内容分析任务")
                
                # 执行内容分析
                分析结果 = await self.分析器管理器.批量分析文章('已收集')
                
                成功数 = 分析结果.get('成功分析数', 0)
                高质量数 = 分析结果.get('高质量文章数', 0)
                self.日志器.info(f"内容分析完成，成功分析 {成功数} 篇，高质量文章 {高质量数} 篇")
                
                # 等待下次执行
                await asyncio.sleep(分析间隔 * 60)
                
            except asyncio.CancelledError:
                break
            except Exception as 错误:
                self.日志器.error(f"分析任务异常: {错误}")
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    async def _运行监控任务(self):
        """运行系统监控任务"""
        监控间隔 = 300  # 5分钟监控一次
        
        while self.运行状态:
            try:
                # 获取系统统计信息
                统计信息 = self.获取系统统计()
                
                # 记录系统状态
                self.日志器.info(f"系统状态监控 - 总文章数: {统计信息.get('总文章数', 0)}, "
                               f"已分析: {统计信息.get('已分析文章数', 0)}, "
                               f"高质量: {统计信息.get('高质量文章数', 0)}")
                
                # 等待下次监控
                await asyncio.sleep(监控间隔)
                
            except asyncio.CancelledError:
                break
            except Exception as 错误:
                self.日志器.error(f"监控任务异常: {错误}")
                await asyncio.sleep(60)
    
    def 获取系统统计(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            # 获取数据库统计
            数据库统计 = self.数据库管理器.获取统计信息()
            
            # 获取收集统计
            收集统计 = self.收集器管理器.获取收集统计()
            
            # 获取分析统计
            分析统计 = self.分析器管理器.获取分析统计()
            
            # 合并统计信息
            系统统计 = {
                '系统状态': '运行中' if self.运行状态 else '已停止',
                '启动时间': datetime.now().isoformat(),
                '数据库统计': 数据库统计,
                '收集统计': 收集统计,
                '分析统计': 分析统计,
                **数据库统计  # 将数据库统计的字段提升到顶层
            }
            
            return 系统统计
            
        except Exception as 错误:
            self.日志器.error(f"获取系统统计失败: {错误}")
            return {}
    
    async def 手动执行收集(self, 账号ID: str = None) -> Dict[str, Any]:
        """
        手动执行内容收集
        
        参数:
            账号ID: 指定账号ID，如果为None则收集所有账号
            
        返回:
            收集结果
        """
        try:
            if 账号ID:
                收集数量 = await self.收集器管理器.收集单个源内容(账号ID)
                return {'账号ID': 账号ID, '收集数量': 收集数量}
            else:
                收集结果 = await self.收集器管理器.收集所有源内容()
                return 收集结果
                
        except Exception as 错误:
            self.日志器.error(f"手动执行收集失败: {错误}")
            return {}
    
    async def 手动执行分析(self, 文章ID: int = None) -> Dict[str, Any]:
        """
        手动执行内容分析
        
        参数:
            文章ID: 指定文章ID，如果为None则批量分析
            
        返回:
            分析结果
        """
        try:
            if 文章ID:
                分析结果 = await self.分析器管理器.分析单篇文章(文章ID)
                return 分析结果 or {}
            else:
                分析结果 = await self.分析器管理器.批量分析文章('已收集')
                return 分析结果
                
        except Exception as 错误:
            self.日志器.error(f"手动执行分析失败: {错误}")
            return {}
    
    def 获取高质量文章列表(self, 数量: int = 10) -> List[Dict[str, Any]]:
        """
        获取高质量文章列表
        
        参数:
            数量: 返回文章数量
            
        返回:
            高质量文章列表
        """
        try:
            return self.分析器管理器.获取高质量文章(数量)
        except Exception as 错误:
            self.日志器.error(f"获取高质量文章列表失败: {错误}")
            return []
