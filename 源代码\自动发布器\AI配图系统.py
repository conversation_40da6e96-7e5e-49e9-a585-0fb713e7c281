# -*- coding: utf-8 -*-
"""
AI自动配图系统

作者: AI助手
日期: 2025-07-28
功能: 根据文章内容自动生成配图，支持多种AI图像生成服务
"""

import os
import sys
import re
import json
import requests
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))

class AI配图生成器:
    """AI配图生成器类"""
    
    def __init__(self):
        self.支持的服务 = {
            'baidu_search': '百度图片搜索 (免费) - 推荐',
            'bing_search': '必应图片搜索 (免费)',
            'unsplash': 'Unsplash免费图库 (免费)',
            'pixabay': 'Pixabay免费图库 (免费)',
            'local_ai': '本地占位符 (免费)',
            'ai_generate': 'AI生成 (付费)'
        }

        # 加载环境变量
        load_dotenv()

        # 配置信息
        self.配置 = {
            # 图片搜索配置
            'default_service': 'baidu_search',  # 默认使用百度图片搜索
            'image_size': 'large',  # 图片尺寸: small, medium, large
            'image_type': 'photo',  # 图片类型: photo, clipart, lineart
            'safe_search': 'moderate',  # 安全搜索: strict, moderate, off
            'max_images': 5,  # 每个关键词最多下载图片数

            # 免费图库API密钥（可选）
            'unsplash_access_key': os.getenv('UNSPLASH_ACCESS_KEY', ''),
            'pixabay_api_key': os.getenv('PIXABAY_API_KEY', ''),

            # 搜索引擎Headers（模拟浏览器）
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',

            # 图片过滤设置
            'min_width': 800,  # 最小宽度
            'min_height': 600,  # 最小高度
            'max_file_size': 5 * 1024 * 1024,  # 最大文件大小 5MB
            'allowed_formats': ['jpg', 'jpeg', 'png', 'webp']
        }

        # 初始化日志
        self._初始化日志()

    def _初始化日志(self):
        """初始化日志系统"""
        import logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger('AI配图生成器')
    
    def 分析文章内容(self, 文章内容: str, 标题: str = "") -> Dict:
        """分析文章内容，提取配图关键信息"""
        try:
            self.logger.info("🔍 分析文章内容，提取配图关键词...")
            
            # 1. 提取关键词
            关键词列表 = self._提取关键词(文章内容, 标题)
            
            # 2. 分析文章主题
            主题分类 = self._分析主题(文章内容, 标题)
            
            # 3. 确定配图风格
            配图风格 = self._确定配图风格(主题分类, 关键词列表)
            
            # 4. 生成图片描述提示词
            图片提示词 = self._生成图片提示词(关键词列表, 主题分类, 配图风格)
            
            分析结果 = {
                '关键词': 关键词列表,
                '主题分类': 主题分类,
                '配图风格': 配图风格,
                '图片提示词': 图片提示词,
                '建议图片数量': self._计算建议图片数量(文章内容),
                '分析时间': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ 文章分析完成，主题: {主题分类}, 风格: {配图风格}")
            return 分析结果
            
        except Exception as e:
            self.logger.error(f"❌ 文章分析失败: {str(e)}")
            raise
    
    def _提取关键词(self, 内容: str, 标题: str) -> List[str]:
        """提取文章关键词"""
        # 简化的关键词提取（实际项目中可以使用更复杂的NLP技术）
        关键词映射 = {
            '人工智能|AI|机器学习|深度学习': ['artificial intelligence', 'technology', 'robot', 'neural network', 'digital'],
            '医疗|健康|医院|诊断': ['medical', 'healthcare', 'hospital', 'doctor', 'health'],
            '教育|学习|学校|知识': ['education', 'learning', 'school', 'knowledge', 'study'],
            '金融|投资|银行|经济': ['finance', 'money', 'banking', 'investment', 'economy'],
            '科技|技术|创新|未来': ['technology', 'innovation', 'future', 'digital', 'modern'],
            '商业|企业|管理|营销': ['business', 'corporate', 'management', 'marketing', 'office'],
            '环境|绿色|可持续|生态': ['environment', 'green', 'nature', 'sustainable', 'eco'],
            '旅游|旅行|风景|文化': ['travel', 'tourism', 'landscape', 'culture', 'destination']
        }
        
        全文 = f"{标题} {内容}".lower()
        提取的关键词 = []
        
        for 模式, 英文关键词 in 关键词映射.items():
            if re.search(模式, 全文):
                提取的关键词.extend(英文关键词)
        
        # 如果没有匹配到特定关键词，使用通用关键词
        if not 提取的关键词:
            提取的关键词 = ['abstract', 'modern', 'professional', 'clean', 'minimalist']
        
        return list(set(提取的关键词))[:10]  # 限制关键词数量
    
    def _分析主题(self, 内容: str, 标题: str) -> str:
        """分析文章主题分类"""
        主题映射 = {
            '科技': ['人工智能', 'AI', '机器学习', '深度学习', '科技', '技术', '创新', '数字化'],
            '医疗': ['医疗', '健康', '医院', '诊断', '治疗', '药物', '医生'],
            '教育': ['教育', '学习', '学校', '知识', '培训', '课程', '学生'],
            '商业': ['商业', '企业', '管理', '营销', '投资', '经济', '金融'],
            '生活': ['生活', '日常', '家庭', '社会', '文化', '娱乐'],
            '环境': ['环境', '绿色', '可持续', '生态', '气候', '自然']
        }
        
        全文 = f"{标题} {内容}"
        主题得分 = {}
        
        for 主题, 关键词列表 in 主题映射.items():
            得分 = sum(1 for 关键词 in 关键词列表 if 关键词 in 全文)
            主题得分[主题] = 得分
        
        # 返回得分最高的主题
        最佳主题 = max(主题得分, key=主题得分.get) if max(主题得分.values()) > 0 else '通用'
        return 最佳主题
    
    def _确定配图风格(self, 主题: str, 关键词: List[str]) -> str:
        """根据主题和关键词确定配图风格"""
        风格映射 = {
            '科技': 'futuristic, high-tech, digital, blue and white color scheme, clean lines',
            '医疗': 'clean, professional, medical, white and blue colors, modern',
            '教育': 'bright, educational, colorful, inspiring, academic',
            '商业': 'professional, corporate, modern, sophisticated, business-like',
            '生活': 'warm, friendly, lifestyle, natural colors, relatable',
            '环境': 'natural, green, eco-friendly, sustainable, organic',
            '通用': 'modern, clean, professional, minimalist, abstract'
        }
        
        return 风格映射.get(主题, 风格映射['通用'])
    
    def _生成图片提示词(self, 关键词: List[str], 主题: str, 风格: str) -> List[str]:
        """生成AI图片生成的提示词"""
        基础提示词 = f"A professional, high-quality image representing {主题.lower()} theme"
        
        # 主图提示词（封面图）
        主图提示词 = f"{基础提示词}, {风格}, featuring {', '.join(关键词[:3])}, suitable for article cover, 16:9 aspect ratio, modern design"
        
        # 内容配图提示词
        内容图提示词列表 = []
        for i, 关键词组 in enumerate([关键词[i:i+2] for i in range(0, len(关键词), 2)][:3]):
            内容图提示词 = f"Illustration of {', '.join(关键词组)}, {风格}, clean background, professional quality"
            内容图提示词列表.append(内容图提示词)
        
        return [主图提示词] + 内容图提示词列表
    
    def _计算建议图片数量(self, 内容: str) -> int:
        """根据文章长度计算建议的图片数量"""
        字数 = len(内容)
        if 字数 < 500:
            return 1  # 只需要封面图
        elif 字数 < 1500:
            return 2  # 封面图 + 1张内容图
        elif 字数 < 3000:
            return 3  # 封面图 + 2张内容图
        else:
            return 4  # 封面图 + 3张内容图
    
    def 搜索图片(self, 关键词: str, 服务: str = 'smart_free', **kwargs) -> Optional[str]:
        """使用指定的服务搜索图片"""
        try:
            if 服务 == 'smart_free':
                return self._智能免费配图(关键词, **kwargs)
            elif 服务 == 'picsum':
                return self._Picsum随机图片(**kwargs)
            elif 服务 == 'unsplash':
                return self._Unsplash图片搜索(关键词, **kwargs)
            elif 服务 == 'baidu_search':
                return self._百度图片搜索(关键词, **kwargs)
            elif 服务 == 'bing_search':
                return self._必应图片搜索(关键词, **kwargs)
            elif 服务 == 'local_ai':
                return self._创建本地配图(关键词, **kwargs)
            else:
                self.logger.warning(f"⚠️  不支持的图片服务: {服务}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 图片搜索失败: {str(e)}")
            # 自动降级到本地配图
            return self._创建本地配图(关键词, **kwargs)

    def _智能免费配图(self, 关键词: str, **kwargs) -> Optional[str]:
        """智能免费配图 - 根据关键词选择最佳免费服务"""
        try:
            self.logger.info(f"🧠 智能免费配图: {关键词}")

            # 1. 优先使用Pollinations AI生成（完全免费，根据关键词生成相关图片）
            if self._检查网络连接("https://image.pollinations.ai"):
                self.logger.info("🌸 优先使用Pollinations AI生成...")
                图片路径 = self._使用Pollinations生成图片(关键词, **kwargs)
                if 图片路径 and os.path.exists(图片路径):
                    self.logger.info("✅ Pollinations AI生成成功，获得关键词相关图片")
                    return 图片路径

            # 2. 尝试HuggingFace生成（免费，但可能有限制）
            if self._检查网络连接("https://api-inference.huggingface.co"):
                self.logger.info("🤗 尝试HuggingFace生成...")
                图片路径 = self._使用HuggingFace生成图片(关键词, **kwargs)
                if 图片路径 and os.path.exists(图片路径):
                    self.logger.info("✅ HuggingFace生成成功，获得关键词相关图片")
                    return 图片路径

            # 3. 降级到主题化Picsum图片（真实图片，主题相关）
            self.logger.info("🎯 降级到主题化Picsum图片...")
            图片路径 = self._主题化Picsum图片(关键词, **kwargs)
            if 图片路径 and os.path.exists(图片路径):
                self.logger.info("✅ 主题化Picsum成功，获得主题相关图片")
                return 图片路径

            # 4. 降级到普通Picsum随机图片（保证有真实图片）
            self.logger.info("🎲 降级到普通Picsum随机图片...")
            图片路径 = self._Picsum随机图片(**kwargs)
            if 图片路径 and os.path.exists(图片路径):
                self.logger.info("✅ Picsum随机图片成功，获得真实图片")
                return 图片路径

            # 5. 最后降级到本地配图
            self.logger.info("💻 最终降级到本地配图...")
            return self._创建本地配图(关键词, **kwargs)

        except Exception as e:
            self.logger.error(f"❌ 智能免费配图失败: {str(e)}")
            return self._创建本地配图(关键词, **kwargs)

    def _主题化Picsum图片(self, 关键词: str, **kwargs) -> Optional[str]:
        """根据关键词主题选择合适的Picsum图片"""
        try:
            self.logger.info(f"🎨 创建主题化Picsum图片: {关键词}")

            # 获取图片尺寸
            宽度 = kwargs.get('width', 1200)
            高度 = kwargs.get('height', 800)

            # 根据关键词选择合适的图片ID范围
            主题映射 = {
                '科技': [1, 2, 3, 4, 5, 180, 181, 182, 183, 184],
                '商务': [7, 8, 9, 10, 11, 185, 186, 187, 188, 189],
                '自然': [12, 13, 14, 15, 16, 190, 191, 192, 193, 194],
                '建筑': [17, 18, 19, 20, 21, 195, 196, 197, 198, 199],
                '人物': [22, 23, 24, 25, 26, 200, 201, 202, 203, 204],
                '食物': [27, 28, 29, 30, 31, 205, 206, 207, 208, 209],
                '艺术': [32, 33, 34, 35, 36, 210, 211, 212, 213, 214],
                '运动': [37, 38, 39, 40, 41, 215, 216, 217, 218, 219]
            }

            # 根据关键词匹配主题
            选中主题 = '科技'  # 默认主题
            for 主题, ids in 主题映射.items():
                if 主题 in 关键词 or any(keyword in 关键词 for keyword in self._获取主题关键词(主题)):
                    选中主题 = 主题
                    break

            # 随机选择该主题的图片ID
            import random
            图片ID = random.choice(主题映射[选中主题])

            # 构建API URL
            api_url = f"https://picsum.photos/id/{图片ID}/{宽度}/{高度}"

            headers = {'User-Agent': self.配置['user_agent']}
            response = requests.get(api_url, headers=headers, timeout=15)

            if response.status_code == 200:
                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                文件名 = f"themed_picsum_{选中主题}_{图片ID}_{时间戳}.jpg"

                图片目录 = os.path.join(os.path.dirname(__file__), 'downloaded_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(response.content)

                文件大小 = len(response.content)
                self.logger.info(f"✅ 主题化Picsum图片下载成功: {文件路径} ({文件大小/1024:.1f} KB)")
                self.logger.info(f"🎯 匹配主题: {选中主题}, 图片ID: {图片ID}")
                return 文件路径
            else:
                self.logger.error(f"❌ 主题化Picsum API调用失败: {response.status_code}")
                # 降级到普通随机图片
                return self._Picsum随机图片(**kwargs)

        except Exception as e:
            self.logger.error(f"❌ 主题化Picsum图片获取失败: {str(e)}")
            # 降级到普通随机图片
            return self._Picsum随机图片(**kwargs)

    def _获取主题关键词(self, 主题: str) -> list:
        """获取主题相关的关键词"""
        主题关键词 = {
            '科技': ['技术', '电脑', '手机', '网络', '互联网', 'AI', '人工智能', '数据', '软件', '编程'],
            '商务': ['商业', '办公', '会议', '工作', '企业', '管理', '金融', '投资', '市场', '销售'],
            '自然': ['自然', '风景', '山', '海', '树', '花', '动物', '植物', '天空', '云'],
            '建筑': ['建筑', '房子', '城市', '桥', '塔', '楼', '街道', '广场', '公园', '景观'],
            '人物': ['人', '人物', '肖像', '面部', '表情', '手', '眼睛', '微笑', '团队', '家庭'],
            '食物': ['食物', '美食', '餐厅', '咖啡', '茶', '水果', '蔬菜', '面包', '甜点', '饮料'],
            '艺术': ['艺术', '绘画', '雕塑', '设计', '创意', '色彩', '抽象', '现代', '古典', '文化'],
            '运动': ['运动', '健身', '跑步', '游泳', '球类', '瑜伽', '户外', '活动', '比赛', '锻炼']
        }
        return 主题关键词.get(主题, [])

    def _检查网络连接(self, url: str) -> bool:
        """检查网络连接是否可用"""
        try:
            response = requests.head(url, timeout=5)
            return response.status_code < 400
        except:
            return False

    def _Picsum随机图片(self, **kwargs) -> Optional[str]:
        """使用Lorem Picsum获取随机高质量图片"""
        try:
            self.logger.info("🎲 使用Lorem Picsum获取随机图片...")

            # 获取图片尺寸
            宽度 = kwargs.get('width', 1200)
            高度 = kwargs.get('height', 800)

            # Lorem Picsum API
            api_url = f"https://picsum.photos/{宽度}/{高度}"

            headers = {'User-Agent': self.配置['user_agent']}
            response = requests.get(api_url, headers=headers, timeout=15)

            if response.status_code == 200:
                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                文件名 = f"picsum_{宽度}x{高度}_{时间戳}.jpg"

                图片目录 = os.path.join(os.path.dirname(__file__), 'downloaded_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(response.content)

                文件大小 = len(response.content)
                self.logger.info(f"✅ Picsum图片下载成功: {文件路径} ({文件大小/1024:.1f} KB)")
                return 文件路径
            else:
                self.logger.error(f"❌ Picsum API调用失败: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Picsum图片获取失败: {str(e)}")
            return None

    def _Unsplash图片搜索(self, 关键词: str, **kwargs) -> Optional[str]:
        """使用Unsplash免费API搜索图片"""
        try:
            self.logger.info(f"📷 Unsplash搜索: {关键词}")

            # 获取图片尺寸
            宽度 = kwargs.get('width', 1200)
            高度 = kwargs.get('height', 800)

            # Unsplash免费API（无需密钥）
            import urllib.parse
            encoded_keyword = urllib.parse.quote(关键词)
            api_url = f"https://source.unsplash.com/{宽度}x{高度}/?{encoded_keyword}"

            headers = {'User-Agent': self.配置['user_agent']}
            response = requests.get(api_url, headers=headers, timeout=15)

            if response.status_code == 200:
                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                文件名 = f"unsplash_{关键词}_{时间戳}.jpg"

                图片目录 = os.path.join(os.path.dirname(__file__), 'downloaded_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(response.content)

                文件大小 = len(response.content)
                self.logger.info(f"✅ Unsplash图片下载成功: {文件路径} ({文件大小/1024:.1f} KB)")
                return 文件路径
            else:
                self.logger.error(f"❌ Unsplash API调用失败: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Unsplash图片搜索失败: {str(e)}")
            return None

    def _创建本地配图(self, 关键词: str, **kwargs) -> Optional[str]:
        """创建本地配图 - 最后的保底方案，优先尝试获取真实图片"""
        try:
            self.logger.info(f"💻 创建本地配图: {关键词}")

            # 最后尝试获取一张真实的随机图片
            try:
                self.logger.info("🎲 最后尝试获取Picsum随机图片...")
                图片路径 = self._Picsum随机图片(**kwargs)
                if 图片路径 and os.path.exists(图片路径):
                    self.logger.info("✅ 成功获取Picsum随机图片作为本地配图")
                    return 图片路径
            except:
                pass

            # 如果真实图片获取失败，才创建本地生成的配图
            self.logger.info("🎨 创建本地生成配图...")

            from PIL import Image, ImageDraw, ImageFont

            # 获取图片尺寸
            宽度 = kwargs.get('width', 1200)
            高度 = kwargs.get('height', 800)

            # 创建图片
            图片 = Image.new('RGB', (宽度, 高度), color='#f0f8ff')
            绘制 = ImageDraw.Draw(图片)

            # 添加渐变背景
            for y in range(高度):
                r = int(240 + (y / 高度) * 15)
                g = int(248 + (y / 高度) * 7)
                b = 255
                绘制.line([(0, y), (宽度, y)], fill=(r, g, b))

            # 添加文本
            try:
                标题字体 = ImageFont.truetype("arial.ttf", min(48, 宽度//25))
                副标题字体 = ImageFont.truetype("arial.ttf", min(24, 宽度//50))
            except:
                标题字体 = ImageFont.load_default()
                副标题字体 = ImageFont.load_default()

            # 主标题
            主标题 = f"📷 {关键词}"
            标题框 = 绘制.textbbox((0, 0), 主标题, font=标题字体)
            标题宽度 = 标题框[2] - 标题框[0]
            标题x = (宽度 - 标题宽度) // 2
            绘制.text((标题x, 高度//3), 主标题, fill='#2563eb', font=标题字体)

            # 副标题
            副标题 = "免费配图 • 自动生成"
            副标题框 = 绘制.textbbox((0, 0), 副标题, font=副标题字体)
            副标题宽度 = 副标题框[2] - 副标题框[0]
            副标题x = (宽度 - 副标题宽度) // 2
            绘制.text((副标题x, 高度//3 + 80), 副标题, fill='#64748b', font=副标题字体)

            # 添加装饰元素
            装饰大小 = min(100, 宽度//12)
            绘制.ellipse([(装饰大小, 装饰大小), (装饰大小*2, 装饰大小*2)], outline='#3b82f6', width=3)
            绘制.ellipse([(宽度-装饰大小*2, 高度-装饰大小*2), (宽度-装饰大小, 高度-装饰大小)], outline='#3b82f6', width=3)

            # 保存图片
            时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
            文件名 = f"local_{关键词}_{时间戳}.png"

            图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
            os.makedirs(图片目录, exist_ok=True)

            文件路径 = os.path.join(图片目录, 文件名)
            图片.save(文件路径)

            self.logger.info(f"✅ 本地配图创建成功: {文件路径}")
            return 文件路径

        except Exception as e:
            self.logger.error(f"❌ 本地配图创建失败: {str(e)}")
            return None

    def _百度图片搜索(self, 关键词: str, **kwargs) -> Optional[str]:
        """使用百度图片搜索获取配图"""
        try:
            self.logger.info(f"🔍 百度图片搜索: {关键词}")

            # 构建搜索URL
            import urllib.parse
            encoded_keyword = urllib.parse.quote(关键词)
            search_url = f"https://image.baidu.com/search/index?tn=baiduimage&word={encoded_keyword}&pn=0&rn=20"

            headers = {
                'User-Agent': self.配置['user_agent'],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # 搜索图片
            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()

            # 解析图片URL（简化版本）
            import re
            # 查找图片URL模式
            img_pattern = r'"objURL":"([^"]+)"'
            img_urls = re.findall(img_pattern, response.text)

            if not img_urls:
                self.logger.warning(f"⚠️  未找到图片: {关键词}")
                return self._创建占位符图片(关键词)

            # 尝试下载前几张图片
            for i, img_url in enumerate(img_urls[:5]):
                try:
                    # 解码URL
                    img_url = img_url.replace('\\u003d', '=').replace('\\u0026', '&')

                    # 下载图片
                    img_response = requests.get(img_url, headers=headers, timeout=15)
                    img_response.raise_for_status()

                    # 检查图片大小
                    if len(img_response.content) < 1024:  # 小于1KB，可能是无效图片
                        continue

                    # 检查文件大小
                    if len(img_response.content) > self.配置['max_file_size']:
                        continue

                    # 保存图片
                    时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                    文件扩展名 = self._获取图片扩展名(img_response.headers.get('content-type', ''))
                    文件名 = f"baidu_search_{关键词}_{时间戳}_{i}.{文件扩展名}"

                    图片目录 = os.path.join(os.path.dirname(__file__), 'downloaded_images')
                    os.makedirs(图片目录, exist_ok=True)

                    文件路径 = os.path.join(图片目录, 文件名)

                    with open(文件路径, 'wb') as f:
                        f.write(img_response.content)

                    # 验证图片
                    if self._验证图片(文件路径):
                        self.logger.info(f"✅ 百度图片下载成功: {文件路径}")
                        return 文件路径
                    else:
                        os.remove(文件路径)
                        continue

                except Exception as e:
                    self.logger.debug(f"图片下载失败: {str(e)}")
                    continue

            # 如果所有图片都下载失败，创建占位符
            self.logger.warning(f"⚠️  所有图片下载失败，使用占位符: {关键词}")
            return self._创建占位符图片(关键词)

        except Exception as e:
            self.logger.error(f"❌ 百度图片搜索失败: {str(e)}")
            return self._创建占位符图片(关键词)

    def _必应图片搜索(self, 关键词: str, **kwargs) -> Optional[str]:
        """使用必应图片搜索获取配图"""
        try:
            self.logger.info(f"🔍 必应图片搜索: {关键词}")

            # 构建搜索URL
            import urllib.parse
            encoded_keyword = urllib.parse.quote(关键词)
            search_url = f"https://cn.bing.com/images/search?q={encoded_keyword}&form=HDRSC2&first=1&tsc=ImageBasicHover"

            headers = {
                'User-Agent': self.配置['user_agent'],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3'
            }

            # 搜索图片
            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()

            # 解析图片URL
            import re
            img_pattern = r'"murl":"([^"]+)"'
            img_urls = re.findall(img_pattern, response.text)

            if not img_urls:
                self.logger.warning(f"⚠️  必应未找到图片: {关键词}")
                return self._创建占位符图片(关键词)

            # 尝试下载图片
            for i, img_url in enumerate(img_urls[:3]):
                try:
                    img_response = requests.get(img_url, headers=headers, timeout=15)
                    img_response.raise_for_status()

                    if len(img_response.content) < 1024:
                        continue

                    # 保存图片
                    时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                    文件扩展名 = self._获取图片扩展名(img_response.headers.get('content-type', ''))
                    文件名 = f"bing_search_{关键词}_{时间戳}_{i}.{文件扩展名}"

                    图片目录 = os.path.join(os.path.dirname(__file__), 'downloaded_images')
                    os.makedirs(图片目录, exist_ok=True)

                    文件路径 = os.path.join(图片目录, 文件名)

                    with open(文件路径, 'wb') as f:
                        f.write(img_response.content)

                    if self._验证图片(文件路径):
                        self.logger.info(f"✅ 必应图片下载成功: {文件路径}")
                        return 文件路径
                    else:
                        os.remove(文件路径)

                except Exception as e:
                    self.logger.debug(f"必应图片下载失败: {str(e)}")
                    continue

            return self._创建占位符图片(关键词)

        except Exception as e:
            self.logger.error(f"❌ 必应图片搜索失败: {str(e)}")
            return self._创建占位符图片(关键词)

    def _获取图片扩展名(self, content_type: str) -> str:
        """根据content-type获取图片扩展名"""
        type_map = {
            'image/jpeg': 'jpg',
            'image/jpg': 'jpg',
            'image/png': 'png',
            'image/webp': 'webp',
            'image/gif': 'gif'
        }
        return type_map.get(content_type.lower(), 'jpg')

    def _验证图片(self, 文件路径: str) -> bool:
        """验证图片文件是否有效"""
        try:
            from PIL import Image
            with Image.open(文件路径) as img:
                # 检查图片尺寸
                width, height = img.size
                if width < self.配置['min_width'] or height < self.配置['min_height']:
                    return False

                # 检查图片格式
                if img.format.lower() not in ['jpeg', 'jpg', 'png', 'webp']:
                    return False

                return True
        except Exception:
            return False

    def _使用HuggingFace生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用HuggingFace免费API生成图片"""
        try:
            self.logger.info("🤗 使用HuggingFace生成图片...")

            # HuggingFace Inference API (免费)
            api_url = "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5"

            headers = {}
            if self.配置['huggingface_token']:
                headers["Authorization"] = f"Bearer {self.配置['huggingface_token']}"

            # 优化提示词为英文
            英文提示词 = self._翻译为英文(提示词)

            payload = {
                "inputs": 英文提示词,
                "parameters": {
                    "num_inference_steps": 20,
                    "guidance_scale": 7.5,
                    "width": 512,
                    "height": 512
                }
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                文件名 = f"huggingface_{时间戳}.png"

                图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(response.content)

                self.logger.info(f"✅ HuggingFace图片生成成功: {文件路径}")
                return 文件路径
            else:
                self.logger.error(f"❌ HuggingFace API调用失败: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"❌ HuggingFace图片生成失败: {str(e)}")
            return None

    def _使用Pollinations生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用Pollinations免费API生成图片"""
        try:
            self.logger.info(f"🌸 使用Pollinations生成图片: {提示词}")

            # 获取图片尺寸
            宽度 = kwargs.get('width', 1024)
            高度 = kwargs.get('height', 1024)

            # Pollinations AI (完全免费，无需API密钥)
            英文提示词 = self._翻译为英文(提示词)
            self.logger.info(f"🔤 翻译提示词: {提示词} → {英文提示词}")

            # URL编码提示词
            import urllib.parse
            encoded_prompt = urllib.parse.quote(英文提示词)

            # Pollinations API - 添加更多参数以获得更好的图片
            api_url = f"https://image.pollinations.ai/prompt/{encoded_prompt}?width={宽度}&height={高度}&seed=-1&enhance=true&nologo=true"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(api_url, headers=headers, timeout=60)

            if response.status_code == 200 and len(response.content) > 1000:  # 确保不是错误页面
                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                # 使用原始关键词作为文件名的一部分
                安全关键词 = "".join(c for c in 提示词 if c.isalnum() or c in (' ', '-', '_')).rstrip()[:20]
                文件名 = f"pollinations_{安全关键词}_{时间戳}.png"

                图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(response.content)

                文件大小 = len(response.content) / 1024
                self.logger.info(f"✅ Pollinations图片生成成功: {文件路径} ({文件大小:.1f} KB)")
                self.logger.info(f"🎨 生成内容: {英文提示词}")
                return 文件路径
            else:
                self.logger.error(f"❌ Pollinations API调用失败: {response.status_code}, 内容长度: {len(response.content) if response.content else 0}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Pollinations图片生成失败: {str(e)}")
            return None

    def _使用免费StableDiffusion生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用免费的Stable Diffusion API生成图片"""
        try:
            self.logger.info("🎨 使用免费Stable Diffusion生成图片...")

            # 使用免费的Stable Diffusion API
            api_url = "https://api.stability.ai/v1/generation/stable-diffusion-v1-6/text-to-image"

            # 注意：这个需要API密钥，如果没有就回退到占位符
            api_key = os.getenv('STABILITY_API_KEY', '')
            if not api_key:
                self.logger.warning("⚠️  未配置Stability API密钥，使用占位符")
                return self._创建占位符图片(提示词)

            英文提示词 = self._翻译为英文(提示词)

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "text_prompts": [{"text": 英文提示词}],
                "cfg_scale": 7,
                "height": 1024,
                "width": 1024,
                "samples": 1,
                "steps": 20
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=60)

            if response.status_code == 200:
                data = response.json()

                # 解码base64图片
                import base64
                image_data = base64.b64decode(data["artifacts"][0]["base64"])

                # 保存图片
                时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
                文件名 = f"stability_{时间戳}.png"

                图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
                os.makedirs(图片目录, exist_ok=True)

                文件路径 = os.path.join(图片目录, 文件名)

                with open(文件路径, 'wb') as f:
                    f.write(image_data)

                self.logger.info(f"✅ Stability AI图片生成成功: {文件路径}")
                return 文件路径
            else:
                self.logger.error(f"❌ Stability AI调用失败: {response.status_code}")
                return self._创建占位符图片(提示词)

        except Exception as e:
            self.logger.error(f"❌ Stability AI图片生成失败: {str(e)}")
            return self._创建占位符图片(提示词)

    def _翻译为英文(self, 中文文本: str) -> str:
        """简单的中英文翻译（用于图片生成）"""
        # 简单的关键词映射
        翻译映射 = {
            '科技': 'technology, futuristic, digital',
            '人工智能': 'artificial intelligence, AI, robot',
            '医疗': 'medical, healthcare, hospital',
            '教育': 'education, learning, school',
            '商业': 'business, corporate, office',
            '环境': 'environment, nature, green',
            '未来': 'future, modern, advanced',
            '专业': 'professional, clean, minimalist',
            '封面图': 'cover image, banner, header',
            '配图': 'illustration, graphic, visual'
        }

        英文文本 = 中文文本
        for 中文, 英文 in 翻译映射.items():
            if 中文 in 中文文本:
                英文文本 = 英文文本.replace(中文, 英文)

        # 如果还有中文，添加通用描述
        if any('\u4e00' <= char <= '\u9fff' for char in 英文文本):
            英文文本 = f"professional illustration, modern design, clean style, {英文文本}"

        return 英文文本

    def _使用豆包生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用豆包生成图片描述并创建配图"""
        if not self.配置['doubao_api_key']:
            self.logger.warning("⚠️  未配置豆包API密钥，使用占位符图片")
            return self._创建占位符图片(提示词)

        try:
            # 使用豆包优化图片描述
            优化提示 = f"""请根据以下图片生成需求，创建一个更详细、更具体的图片描述，用于AI图像生成：

原始需求：{提示词}

请提供：
1. 详细的视觉描述
2. 色彩搭配建议
3. 构图建议
4. 风格特点

要求：
- 描述要具体且富有视觉感
- 适合用于AI图像生成
- 保持专业和美观
- 字数控制在200字以内
- 用中文回答"""

            # 调用豆包API
            headers = {
                'Authorization': f'Bearer {self.配置["doubao_api_key"]}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': self.配置['doubao_model'],
                'messages': [
                    {
                        'role': 'user',
                        'content': 优化提示
                    }
                ],
                'temperature': 0.7,
                'max_tokens': 500
            }

            response = requests.post(
                f"{self.配置['doubao_base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                优化后的描述 = result['choices'][0]['message']['content']

                self.logger.info(f"✅ 豆包优化图片描述成功")
                self.logger.debug(f"优化后描述: {优化后的描述[:100]}...")

                # 创建带有豆包描述的占位符图片
                return self._创建豆包占位符图片(提示词, 优化后的描述)
            else:
                self.logger.error(f"❌ 豆包API调用失败: {response.status_code} - {response.text}")
                return self._创建占位符图片(提示词)

        except Exception as e:
            self.logger.error(f"❌ 豆包图片生成失败: {str(e)}")
            return self._创建占位符图片(提示词)

    def _使用DALLE生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用DALL-E生成图片"""
        if not self.配置['openai_api_key']:
            self.logger.warning("⚠️  未配置OpenAI API密钥，跳过DALL-E生成")
            return None
        
        try:
            headers = {
                'Authorization': f"Bearer {self.配置['openai_api_key']}",
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': 'dall-e-3',
                'prompt': 提示词,
                'n': 1,
                'size': kwargs.get('size', self.配置['image_size']),
                'quality': kwargs.get('quality', self.配置['image_quality']),
                'style': kwargs.get('style', self.配置['style'])
            }
            
            response = requests.post(
                'https://api.openai.com/v1/images/generations',
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                image_url = result['data'][0]['url']
                
                # 下载图片到本地
                本地路径 = self._下载图片(image_url, 'dalle')
                self.logger.info(f"✅ DALL-E图片生成成功: {本地路径}")
                return 本地路径
            else:
                self.logger.error(f"❌ DALL-E API调用失败: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ DALL-E生成异常: {str(e)}")
            return None
    
    def _使用StableDiffusion生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用Stable Diffusion生成图片（示例实现）"""
        self.logger.info("🎨 使用Stable Diffusion生成图片（模拟）")
        # 这里可以集成Stable Diffusion API或本地模型
        # 目前返回None，表示未实现
        return None
    
    def _使用本地AI生成图片(self, 提示词: str, **kwargs) -> Optional[str]:
        """使用本地AI模型生成图片（占位符实现）"""
        self.logger.info("🖼️  使用本地AI生成图片（模拟）")
        # 这里可以集成本地的图像生成模型
        # 目前返回一个占位符图片路径
        return self._创建占位符图片(提示词)
    
    def _下载图片(self, 图片URL: str, 服务名称: str) -> str:
        """下载图片到本地"""
        try:
            response = requests.get(图片URL, timeout=30)
            response.raise_for_status()
            
            # 生成文件名
            时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
            文件名 = f"{服务名称}_{时间戳}_{hashlib.md5(图片URL.encode()).hexdigest()[:8]}.png"
            
            # 确保目录存在
            图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
            os.makedirs(图片目录, exist_ok=True)
            
            文件路径 = os.path.join(图片目录, 文件名)
            
            with open(文件路径, 'wb') as f:
                f.write(response.content)
            
            return 文件路径
            
        except Exception as e:
            self.logger.error(f"❌ 图片下载失败: {str(e)}")
            raise
    
    def _创建占位符图片(self, 提示词: str) -> str:
        """创建占位符图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建图片
            宽度, 高度 = 1024, 1024
            图片 = Image.new('RGB', (宽度, 高度), color='#f0f0f0')
            绘制 = ImageDraw.Draw(图片)
            
            # 添加文本
            try:
                字体 = ImageFont.truetype("arial.ttf", 40)
            except:
                字体 = ImageFont.load_default()
            
            文本 = "AI Generated Image\n(Placeholder)"
            文本框 = 绘制.textbbox((0, 0), 文本, font=字体)
            文本宽度 = 文本框[2] - 文本框[0]
            文本高度 = 文本框[3] - 文本框[1]
            
            x = (宽度 - 文本宽度) // 2
            y = (高度 - 文本高度) // 2
            
            绘制.text((x, y), 文本, fill='#666666', font=字体)
            
            # 保存图片
            时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
            文件名 = f"placeholder_{时间戳}.png"
            
            图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
            os.makedirs(图片目录, exist_ok=True)
            
            文件路径 = os.path.join(图片目录, 文件名)
            图片.save(文件路径)
            
            return 文件路径
            
        except Exception as e:
            self.logger.error(f"❌ 占位符图片创建失败: {str(e)}")
            raise

    def _创建豆包占位符图片(self, 原始提示词: str, 豆包描述: str) -> str:
        """创建带有豆包描述的占位符图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # 创建图片
            宽度, 高度 = 1024, 1024
            图片 = Image.new('RGB', (宽度, 高度), color='#f8f9fa')
            绘制 = ImageDraw.Draw(图片)

            # 添加标题
            try:
                标题字体 = ImageFont.truetype("arial.ttf", 32)
                内容字体 = ImageFont.truetype("arial.ttf", 20)
            except:
                标题字体 = ImageFont.load_default()
                内容字体 = ImageFont.load_default()

            # 绘制标题
            标题文本 = "🤖 豆包 AI 配图"
            标题框 = 绘制.textbbox((0, 0), 标题文本, font=标题字体)
            标题宽度 = 标题框[2] - 标题框[0]
            标题x = (宽度 - 标题宽度) // 2
            绘制.text((标题x, 50), 标题文本, fill='#ff6b35', font=标题字体)

            # 绘制分割线
            绘制.line([(100, 120), (宽度-100, 120)], fill='#e5e7eb', width=2)

            # 绘制豆包描述（截取前200字符）
            描述文本 = 豆包描述[:200] + "..." if len(豆包描述) > 200 else 豆包描述

            # 文本换行处理
            行列表 = []
            当前行 = ""
            for 字符 in 描述文本:
                if 字符 == '\n' or len(当前行) > 40:
                    行列表.append(当前行)
                    当前行 = 字符 if 字符 != '\n' else ""
                else:
                    当前行 += 字符
            if 当前行:
                行列表.append(当前行)

            # 绘制描述文本
            y_位置 = 160
            for 行 in 行列表[:15]:  # 最多显示15行
                if y_位置 > 高度 - 100:
                    break
                绘制.text((80, y_位置), 行, fill='#374151', font=内容字体)
                y_位置 += 30

            # 添加底部信息
            底部文本 = f"Generated by 豆包 AI • {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            底部框 = 绘制.textbbox((0, 0), 底部文本, font=内容字体)
            底部宽度 = 底部框[2] - 底部框[0]
            底部x = (宽度 - 底部宽度) // 2
            绘制.text((底部x, 高度-50), 底部文本, fill='#9ca3af', font=内容字体)

            # 保存图片
            时间戳 = datetime.now().strftime('%Y%m%d_%H%M%S')
            文件名 = f"doubao_ai_{时间戳}.png"

            图片目录 = os.path.join(os.path.dirname(__file__), 'generated_images')
            os.makedirs(图片目录, exist_ok=True)

            文件路径 = os.path.join(图片目录, 文件名)
            图片.save(文件路径)

            return 文件路径

        except Exception as e:
            self.logger.error(f"❌ 豆包占位符图片创建失败: {str(e)}")
            # 回退到普通占位符
            return self._创建占位符图片(原始提示词)

    def 批量搜索配图(self, 文章内容: str, 标题: str = "", 服务: str = 'baidu_search') -> Dict:
        """为文章批量搜索配图"""
        try:
            self.logger.info(f"🔍 开始为文章搜索配图...")

            # 1. 分析文章内容，提取关键词
            分析结果 = self.分析文章内容(文章内容, 标题)

            # 2. 搜索图片
            搜索的图片 = []
            关键词列表 = 分析结果['关键词']

            # 为封面图选择最重要的关键词
            封面关键词 = f"{标题} {分析结果['主题分类']}"

            self.logger.info(f"🖼️  搜索封面图: {封面关键词}")
            封面图片路径 = self.搜索图片(封面关键词, 服务)
            if 封面图片路径:
                搜索的图片.append({
                    '序号': 1,
                    '类型': '封面图',
                    '关键词': 封面关键词,
                    '文件路径': 封面图片路径,
                    '搜索时间': datetime.now().isoformat()
                })

            # 为内容搜索配图
            内容关键词数量 = min(3, len(关键词列表))  # 最多3张内容图
            for i in range(内容关键词数量):
                if i < len(关键词列表):
                    关键词 = 关键词列表[i]
                    self.logger.info(f"🖼️  搜索内容图 {i+1}: {关键词}")

                    图片路径 = self.搜索图片(关键词, 服务)
                    if 图片路径:
                        搜索的图片.append({
                            '序号': i + 2,
                            '类型': f'内容图{i+1}',
                            '关键词': 关键词,
                            '文件路径': 图片路径,
                            '搜索时间': datetime.now().isoformat()
                        })
                    else:
                        self.logger.warning(f"⚠️  内容图 {i+1} 搜索失败")

            # 3. 构建结果
            配图结果 = {
                '文章分析': 分析结果,
                '搜索的图片': 搜索的图片,
                '成功数量': len(搜索的图片),
                '计划数量': 1 + 内容关键词数量,  # 1张封面图 + N张内容图
                '使用服务': 服务,
                '搜索时间': datetime.now().isoformat()
            }

            self.logger.info(f"✅ 配图搜索完成，成功获取 {len(搜索的图片)} 张图片")
            return 配图结果

        except Exception as e:
            self.logger.error(f"❌ 批量配图搜索失败: {str(e)}")
            raise


def 测试AI配图系统():
    """测试AI配图系统"""
    print("🧪 测试AI配图系统")
    print("=" * 50)
    
    # 读取测试文章
    try:
        with open('AI配图测试文章.md', 'r', encoding='utf-8') as f:
            测试文章 = f.read()
    except FileNotFoundError:
        print("❌ 找不到测试文章文件")
        return
    
    # 创建AI配图生成器
    配图生成器 = AI配图生成器()
    
    # 提取标题
    标题匹配 = re.search(r'^# (.+)', 测试文章, re.MULTILINE)
    标题 = 标题匹配.group(1) if 标题匹配 else "测试文章"
    
    print(f"📝 文章标题: {标题}")
    print(f"📊 文章长度: {len(测试文章)} 字符")
    
    # 分析文章内容
    try:
        分析结果 = 配图生成器.分析文章内容(测试文章, 标题)
        
        print(f"\n🔍 文章分析结果:")
        print(f"   主题分类: {分析结果['主题分类']}")
        print(f"   配图风格: {分析结果['配图风格']}")
        print(f"   关键词: {', '.join(分析结果['关键词'][:5])}...")
        print(f"   建议图片数量: {分析结果['建议图片数量']}")
        
        print(f"\n🎨 生成的提示词:")
        for i, 提示词 in enumerate(分析结果['图片提示词'], 1):
            print(f"   {i}. {提示词[:80]}...")
        
        # 生成配图（使用豆包模式）
        print(f"\n🖼️  开始生成配图...")
        配图结果 = 配图生成器.批量生成配图(测试文章, 标题, 'doubao')
        
        print(f"\n✅ 配图生成完成:")
        print(f"   成功生成: {配图结果['成功数量']} 张")
        print(f"   总计划: {配图结果['总数量']} 张")
        
        for 图片信息 in 配图结果['生成的图片']:
            print(f"   📷 {图片信息['类型']}: {图片信息['文件路径']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    测试AI配图系统()
