# 微信公众号自动发布器

## 📖 项目简介

微信公众号自动发布器是一个功能完整的自动化发布系统，支持文章内容处理、多种排版样式、图片处理、草稿管理和自动发布等功能。系统设计安全可靠，支持测试模式和生产模式切换。

## ✨ 核心功能

### 🚀 自动发布功能
- ✅ 文章上传到微信公众号草稿箱
- ✅ 支持自动发布（可配置开关）
- ✅ 发布前确认机制
- ✅ 测试模式和生产模式切换
- ✅ 发布频率限制和安全控制

### 🎨 排版样式系统
- **商务风格**: 简洁专业，适合商业内容
- **科技风格**: 现代感强，适合技术文章
- **生活风格**: 温馨亲和，适合生活分享
- **学术风格**: 严谨规范，适合知识分享

### 📷 图片处理
- 自动下载和上传图片素材
- 图片压缩和尺寸调整
- 支持多种图片格式
- 自动设置封面图片

### 🔒 安全特性
- 敏感信息配置文件管理
- 发布前确认机制
- 完善的错误处理和重试机制
- 详细的日志记录和监控

## 📁 项目结构

```
源代码/自动发布器/
├── 微信自动发布器.py      # 主程序入口
├── 微信API.py             # 微信公众号API封装
├── 文章处理器.py          # 文章内容处理模块
├── 排版模板.py            # 排版样式模板系统
├── 日志系统.py            # 日志和错误处理系统
├── templates/             # 排版模板文件
│   ├── business_template.html
│   ├── tech_template.html
│   └── ...
├── temp/                  # 临时文件目录
└── README.md             # 使用说明文档
```

## ⚙️ 配置要求

### 1. 微信公众号配置

在 `配置文件/微信发布配置.py` 中配置：

```python
# 环境变量方式（推荐）
export WECHAT_APP_ID="your_app_id"
export WECHAT_APP_SECRET="your_app_secret"
```

或直接在配置文件中设置：

```python
微信API配置 = {
    'app_id': 'your_app_id',
    'app_secret': 'your_app_secret',
    # ...
}
```

### 2. 发布控制配置

```python
发布控制配置 = {
    '启用自动发布': False,  # 默认关闭，仅上传到草稿箱
    '测试模式': True,       # 测试模式
    '发布前确认': True,     # 需要人工确认
    # ...
}
```

### 3. 依赖安装

```bash
pip install requests pillow
```

## 🚀 快速开始

### 1. 基本使用

```python
from 微信自动发布器 import 微信自动发布器

# 创建发布器实例
发布器 = 微信自动发布器()

# 准备文章数据
文章数据 = {
    '标题': '测试文章标题',
    '内容': '文章内容...',
    '元数据': {
        '作者': '作者名称',
        '来源': '来源信息',
        '标签': ['标签1', '标签2']
    }
}

# 发布文章
结果 = 发布器.发布文章(文章数据)
print(f"发布结果: {结果}")
```

### 2. 命令行使用

```bash
# 查看配置信息
python 微信自动发布器.py --config

# 运行测试
python 微信自动发布器.py --test

# 查看草稿列表
python 微信自动发布器.py --draft-list

# 查看统计信息
python 微信自动发布器.py --stats
```

### 3. 批量发布

```python
# 批量发布文章
文章列表 = [文章数据1, 文章数据2, 文章数据3]
发布选项 = {
    '仅草稿': True,      # 仅创建草稿，不发布
    '发布间隔': 60,      # 发布间隔60秒
    '排版样式': 'tech'   # 使用科技风格
}

结果列表 = 发布器.批量发布文章(文章列表, 发布选项)
```

## 🎨 排版样式使用

### 1. 选择排版样式

```python
# 在发布时指定样式
发布选项 = {
    '排版样式': 'business'  # business, tech, life, academic
}

结果 = 发布器.发布文章(文章数据, 发布选项)
```

### 2. 自定义样式

可以在 `排版模板.py` 中添加新的样式配置：

```python
'custom_style': {
    '名称': '自定义风格',
    '描述': '自定义排版样式',
    '主色调': '#FF6B6B',
    '辅助色': '#4ECDC4',
    # ...
}
```

## 🔧 高级配置

### 1. 图片处理配置

```python
内容处理配置 = {
    '图片处理': {
        '启用图片上传': True,
        '图片压缩': True,
        '最大图片大小': 2048000,  # 2MB
        '图片质量': 85,
        '最大宽度': 900
    }
}
```

### 2. 安全设置

```python
安全审核配置 = {
    '内容审核': {
        '启用敏感词检测': True,
        '敏感词库文件': '配置文件/敏感词库.txt'
    },
    '错误处理': {
        '最大重试次数': 3,
        '重试间隔秒数': [60, 120, 300]
    }
}
```

### 3. 通知配置

```python
通知配置 = {
    '发布成功通知': True,
    '发布失败通知': True,
    '邮件通知': {
        '启用': False,
        'smtp_server': 'smtp.qq.com',
        # ...
    }
}
```

## 🧪 测试方法

### 1. 系统配置测试

```bash
python 微信自动发布器.py --config
```

检查输出是否显示：
- ✅ API配置状态: 已配置
- ✅ 配置验证通过

### 2. 功能测试

```bash
python 微信自动发布器.py --test
```

测试流程：
1. 验证系统配置
2. 创建测试文章
3. 处理文章内容
4. 上传到草稿箱
5. 显示测试结果

### 3. API连接测试

```python
from 微信API import 测试微信API
测试微信API()
```

### 4. 文章处理测试

```python
from 文章处理器 import 测试文章处理器
测试文章处理器()
```

### 5. 排版模板测试

```python
from 排版模板 import 排版模板管理器

模板管理器 = 排版模板管理器()
print("可用样式:", 模板管理器.获取可用样式列表())
```

## 📊 监控和日志

### 1. 日志文件位置

- 主日志: `日志文件/微信发布.log`
- 错误日志: `日志文件/微信发布_errors.log`
- API日志: `日志文件/微信API.log`

### 2. 统计信息

```python
统计信息 = 发布器.获取发布统计()
print(统计信息)
```

包含信息：
- 处理文章总数
- 成功上传数量
- 成功发布数量
- 失败统计
- API状态
- 错误统计

### 3. 草稿管理

```python
# 获取草稿列表
草稿列表 = 发布器.获取草稿列表()

# 删除草稿
发布器.删除草稿(media_id)
```

## ⚠️ 注意事项

### 1. 安全提醒

- **敏感信息**: 请使用环境变量或配置文件管理API密钥
- **测试模式**: 首次使用请确保开启测试模式
- **发布确认**: 建议开启发布前确认机制
- **频率限制**: 遵守微信公众号API调用频率限制

### 2. 使用限制

- 每日最大发布数: 3篇（可配置）
- 最小发布间隔: 4小时（可配置）
- 图片大小限制: 2MB
- 文章标题长度: 64字符

### 3. 错误处理

- 系统具有自动重试机制
- 详细的错误日志记录
- 支持邮件和Webhook通知
- 临时文件自动清理

## 🔄 更新和维护

### 1. 配置更新

修改配置文件后重启程序即可生效。

### 2. 日志清理

```python
发布器.日志管理器.清理旧日志(保留天数=30)
```

### 3. 统计重置

```python
发布器.重置统计信息()
```

## 📞 技术支持

如遇到问题，请检查：

1. **配置文件**: 确保微信API配置正确
2. **网络连接**: 确保能访问微信API服务器
3. **日志文件**: 查看详细错误信息
4. **测试模式**: 先在测试模式下验证功能

## 📄 许可证

本项目仅供学习和研究使用，请遵守微信公众平台相关规定。
