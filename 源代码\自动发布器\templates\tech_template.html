<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        .article-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
            font-family: PingFang SC, Microsoft YaHei, sans-serif;
            font-size: 15px;
            line-height: 1.7;
            color: #333;
            background-color: #fff;
        }
        
        .article-title {
            font-family: PingFang SC, Microsoft YaHei, sans-serif;
            font-size: 19px;
            font-weight: bold;
            color: #1A1A1A;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #00D4FF;
            position: relative;
        }
        
        .article-title::before {
            content: "🚀";
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: -30px;
            font-size: 24px;
        }
        
        .article-meta {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border: 1px solid #00D4FF33;
        }
        
        .article-content {
            text-align: justify;
            word-wrap: break-word;
        }
        
        .article-content h1, .article-content h2, .article-content h3 {
            color: #1A1A1A;
            margin-top: 25px;
            margin-bottom: 15px;
            font-weight: bold;
            position: relative;
        }
        
        .article-content h1 {
            font-size: 20px;
            border-left: 4px solid #00D4FF;
            padding-left: 15px;
            background: linear-gradient(90deg, #00D4FF11, transparent);
        }
        
        .article-content h2 {
            font-size: 18px;
            border-left: 3px solid #00D4FF;
            padding-left: 12px;
            background: linear-gradient(90deg, #00D4FF08, transparent);
        }
        
        .article-content h3 {
            font-size: 16px;
            border-left: 2px solid #00D4FF;
            padding-left: 10px;
        }
        
        .article-content p {
            margin-bottom: 15px;
            text-indent: 2em;
        }
        
        .article-content blockquote {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #00D4FF11, #1A1A1A11);
            border-left: 4px solid #00D4FF;
            border-radius: 8px;
            font-style: italic;
            color: #555;
            position: relative;
        }
        
        .article-content blockquote::before {
            content: "💡";
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 18px;
        }
        
        .article-content ul, .article-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        .article-content li {
            margin-bottom: 8px;
            position: relative;
        }
        
        .article-content ul li::before {
            content: "▶";
            color: #00D4FF;
            position: absolute;
            left: -20px;
        }
        
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,212,255,0.2);
            border: 1px solid #00D4FF33;
        }
        
        .article-content code {
            background-color: #1A1A1A;
            color: #00D4FF;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .article-content pre {
            background-color: #1A1A1A;
            color: #00D4FF;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid #00D4FF33;
        }
        
        .article-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #999;
            font-size: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #00D4FF22, #1A1A1A22);
            border: 1px solid #00D4FF;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }
        
        .highlight-box::before {
            content: "⚡";
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
        }
        
        .emphasis {
            color: #00D4FF;
            font-weight: bold;
            text-shadow: 0 0 5px #00D4FF33;
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            color: #00D4FF;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="article-container">
        <h1 class="article-title">{{title}}</h1>
        <div class="article-meta">
            <span>📅 {{publish_date}}</span>
            {{#author}}<span> | 👨‍💻 {{author}}</span>{{/author}}
            {{#source}}<span> | 🔗 来源：{{source}}</span>{{/source}}
        </div>
        <div class="article-content">
            {{content}}
        </div>
        <div class="article-footer">
            <div class="divider">⚡ ◇ ⚡</div>
            <p>🚀 感谢您的阅读，让我们一起探索科技前沿！</p>
            {{#original_link}}<p><a href="{{original_link}}" style="color: #00D4FF;">🔗 原文链接</a></p>{{/original_link}}
        </div>
    </div>
</body>
</html>
