# -*- coding: utf-8 -*-
"""
微信公众号自动发布器启动脚本

作者: AI助手
日期: 2025-07-28
功能: 提供简单的启动界面和快捷操作
"""

import os
import sys
import json
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))

# 导入主要模块
from 微信自动发布器 import 微信自动发布器, 创建示例文章
from 微信发布配置 import 显示配置摘要, 更新发布状态

def 显示欢迎信息():
    """显示欢迎信息"""
    print("🚀 微信公众号自动发布器")
    print("=" * 50)
    print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("📁 工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version.split()[0])
    print("=" * 50)

def 显示主菜单():
    """显示主菜单"""
    print("\n📋 主菜单")
    print("-" * 30)
    print("1. 📝 发布单篇文章")
    print("2. 📚 批量发布文章")
    print("3. 🧪 运行测试")
    print("4. ⚙️  查看配置")
    print("5. 📄 管理草稿")
    print("6. 📊 查看统计")
    print("7. 🔧 系统设置")
    print("8. ❓ 帮助信息")
    print("0. 🚪 退出程序")
    print("-" * 30)

def 发布单篇文章():
    """发布单篇文章"""
    print("\n📝 发布单篇文章")
    print("-" * 30)
    
    try:
        发布器 = 微信自动发布器()
        
        # 验证配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        if not 配置有效:
            print("❌ 系统配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            print("请先配置系统后再使用发布功能。")
            return
        
        print("请选择文章来源:")
        print("1. 使用示例文章")
        print("2. 手动输入文章")
        print("3. 从文件读取")
        
        选择 = input("请输入选择 (1-3): ").strip()
        
        if 选择 == "1":
            # 使用示例文章
            文章数据 = 创建示例文章()
            print(f"✅ 已加载示例文章: {文章数据['标题']}")
            
        elif 选择 == "2":
            # 手动输入文章
            标题 = input("请输入文章标题: ").strip()
            if not 标题:
                print("❌ 标题不能为空")
                return
            
            print("请输入文章内容 (输入 'END' 结束):")
            内容行 = []
            while True:
                行 = input()
                if 行.strip() == 'END':
                    break
                内容行.append(行)
            
            内容 = '\n'.join(内容行)
            if not 内容.strip():
                print("❌ 内容不能为空")
                return
            
            文章数据 = {
                '标题': 标题,
                '内容': 内容,
                '元数据': {
                    '作者': input("作者 (可选): ").strip() or '',
                    '来源': input("来源 (可选): ").strip() or '',
                    '原文链接': input("原文链接 (可选): ").strip() or ''
                }
            }
            
        elif 选择 == "3":
            # 从文件读取
            文件路径 = input("请输入文件路径: ").strip()
            if not os.path.exists(文件路径):
                print(f"❌ 文件不存在: {文件路径}")
                return
            
            try:
                with open(文件路径, 'r', encoding='utf-8') as f:
                    if 文件路径.endswith('.json'):
                        文章数据 = json.load(f)
                    else:
                        内容 = f.read()
                        文件名 = os.path.basename(文件路径)
                        标题 = os.path.splitext(文件名)[0]
                        文章数据 = {
                            '标题': 标题,
                            '内容': 内容,
                            '元数据': {}
                        }
                print(f"✅ 已从文件加载文章: {文章数据.get('标题', '未知标题')}")
            except Exception as e:
                print(f"❌ 读取文件失败: {str(e)}")
                return
        else:
            print("❌ 无效选择")
            return
        
        # 发布选项
        print("\n发布选项:")
        仅草稿 = input("是否仅创建草稿? (y/n, 默认y): ").strip().lower()
        仅草稿 = 仅草稿 != 'n'
        
        排版样式 = input("排版样式 (business/tech/life/academic, 默认business): ").strip()
        if 排版样式 not in ['business', 'tech', 'life', 'academic']:
            排版样式 = 'business'
        
        发布选项 = {
            '仅草稿': 仅草稿,
            '排版样式': 排版样式,
            '跳过确认': False
        }
        
        # 执行发布
        print(f"\n🚀 开始发布文章: {文章数据['标题']}")
        结果 = 发布器.发布文章(文章数据, 发布选项)
        
        if 结果['success']:
            print("✅ 文章发布成功!")
            print(f"📄 草稿ID: {结果['media_id']}")
            if 结果['draft_only']:
                print("📝 文章已保存为草稿")
            else:
                print(f"🚀 文章已发布: {结果['publish_id']}")
            print(f"⏱️  处理耗时: {结果['process_time']:.2f}秒")
        else:
            print(f"❌ 文章发布失败: {结果['error_message']}")
            
    except Exception as e:
        print(f"❌ 发布过程异常: {str(e)}")

def 批量发布文章():
    """批量发布文章"""
    print("\n📚 批量发布文章")
    print("-" * 30)
    print("⚠️  批量发布功能需要准备文章数据文件")
    print("文件格式: JSON数组，每个元素包含标题、内容、元数据等字段")
    
    文件路径 = input("请输入文章数据文件路径: ").strip()
    if not os.path.exists(文件路径):
        print(f"❌ 文件不存在: {文件路径}")
        return
    
    try:
        with open(文件路径, 'r', encoding='utf-8') as f:
            文章列表 = json.load(f)
        
        if not isinstance(文章列表, list):
            print("❌ 文件格式错误，应为JSON数组")
            return
        
        print(f"✅ 已加载 {len(文章列表)} 篇文章")
        
        # 批量发布选项
        发布间隔 = input("发布间隔秒数 (默认60): ").strip()
        try:
            发布间隔 = int(发布间隔) if 发布间隔 else 60
        except:
            发布间隔 = 60
        
        发布选项 = {
            '仅草稿': True,  # 批量发布默认仅创建草稿
            '发布间隔': 发布间隔,
            '跳过确认': True
        }
        
        确认 = input(f"确认批量发布 {len(文章列表)} 篇文章? (y/n): ").strip().lower()
        if 确认 != 'y':
            print("❌ 用户取消操作")
            return
        
        # 执行批量发布
        发布器 = 微信自动发布器()
        结果列表 = 发布器.批量发布文章(文章列表, 发布选项)
        
        # 统计结果
        成功数量 = sum(1 for r in 结果列表 if r['success'])
        失败数量 = len(结果列表) - 成功数量
        
        print(f"\n📊 批量发布完成:")
        print(f"   总数: {len(结果列表)}")
        print(f"   成功: {成功数量}")
        print(f"   失败: {失败数量}")
        
    except Exception as e:
        print(f"❌ 批量发布失败: {str(e)}")

def 运行测试():
    """运行测试"""
    print("\n🧪 运行测试")
    print("-" * 30)
    
    print("请选择测试类型:")
    print("1. 快速测试")
    print("2. 完整测试")
    print("3. 性能测试")
    
    选择 = input("请输入选择 (1-3): ").strip()
    
    if 选择 == "1":
        # 快速测试
        try:
            from 测试脚本 import 微信发布器测试
            import unittest
            
            # 只运行关键测试
            suite = unittest.TestSuite()
            suite.addTest(微信发布器测试('test_01_系统配置验证'))
            suite.addTest(微信发布器测试('test_03_文章处理器测试'))
            
            runner = unittest.TextTestRunner(verbosity=2)
            runner.run(suite)
            
        except Exception as e:
            print(f"❌ 快速测试失败: {str(e)}")
    
    elif 选择 == "2":
        # 完整测试
        try:
            os.system(f"python {os.path.join(os.path.dirname(__file__), '测试脚本.py')}")
        except Exception as e:
            print(f"❌ 完整测试失败: {str(e)}")
    
    elif 选择 == "3":
        # 性能测试
        try:
            from 测试脚本 import 运行性能测试
            运行性能测试()
        except Exception as e:
            print(f"❌ 性能测试失败: {str(e)}")
    
    else:
        print("❌ 无效选择")

def 查看配置():
    """查看配置"""
    print("\n⚙️  系统配置")
    print("-" * 30)
    
    try:
        显示配置摘要()
        
        发布器 = 微信自动发布器()
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if 配置有效:
            print("\n✅ 配置验证通过")
        else:
            print("\n❌ 配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
        
    except Exception as e:
        print(f"❌ 查看配置失败: {str(e)}")

def 管理草稿():
    """管理草稿"""
    print("\n📄 草稿管理")
    print("-" * 30)
    
    try:
        发布器 = 微信自动发布器()
        草稿列表 = 发布器.获取草稿列表(数量=10)
        
        if not 草稿列表:
            print("📝 暂无草稿")
            return
        
        print(f"📝 草稿列表 (共 {len(草稿列表)} 篇):")
        for i, 草稿 in enumerate(草稿列表, 1):
            标题 = 草稿.get('title', '无标题')
            media_id = 草稿.get('media_id', '')
            print(f"{i:2d}. {标题[:30]}... - {media_id}")
        
        操作 = input("\n请选择操作 (d=删除草稿, 其他=返回): ").strip().lower()
        if 操作 == 'd':
            try:
                索引 = int(input("请输入要删除的草稿编号: ")) - 1
                if 0 <= 索引 < len(草稿列表):
                    media_id = 草稿列表[索引]['media_id']
                    确认 = input(f"确认删除草稿 {media_id}? (y/n): ").strip().lower()
                    if 确认 == 'y':
                        if 发布器.删除草稿(media_id):
                            print("✅ 草稿删除成功")
                        else:
                            print("❌ 草稿删除失败")
                    else:
                        print("❌ 用户取消操作")
                else:
                    print("❌ 无效的草稿编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        
    except Exception as e:
        print(f"❌ 草稿管理失败: {str(e)}")

def 查看统计():
    """查看统计"""
    print("\n📊 系统统计")
    print("-" * 30)
    
    try:
        发布器 = 微信自动发布器()
        统计信息 = 发布器.获取发布统计()
        
        print(f"📈 发布统计:")
        print(f"   处理总数: {统计信息['total_processed']}")
        print(f"   成功上传: {统计信息['successful_uploads']}")
        print(f"   成功发布: {统计信息['successful_publishes']}")
        print(f"   失败上传: {统计信息['failed_uploads']}")
        print(f"   失败发布: {统计信息['failed_publishes']}")
        
        if 统计信息['start_time']:
            print(f"   开始时间: {统计信息['start_time']}")
        if 统计信息['last_publish_time']:
            print(f"   最后发布: {统计信息['last_publish_time']}")
        
        print(f"\n🔗 API状态:")
        api_status = 统计信息['api_status']
        for key, value in api_status.items():
            print(f"   {key}: {value}")
        
        print(f"\n❌ 错误统计:")
        error_stats = 统计信息['error_stats']
        for key, value in error_stats.items():
            print(f"   {key}: {value}")
        
    except Exception as e:
        print(f"❌ 查看统计失败: {str(e)}")

def 系统设置():
    """系统设置"""
    print("\n🔧 系统设置")
    print("-" * 30)
    
    print("1. 启用/禁用自动发布")
    print("2. 切换测试模式")
    print("3. 重置统计信息")
    print("4. 清理临时文件")
    
    选择 = input("请输入选择 (1-4): ").strip()
    
    if 选择 == "1":
        当前状态 = input("启用自动发布? (y/n): ").strip().lower() == 'y'
        更新发布状态(启用自动发布=当前状态)
        print(f"✅ 自动发布已{'启用' if 当前状态 else '禁用'}")
        
    elif 选择 == "2":
        当前状态 = input("启用测试模式? (y/n): ").strip().lower() == 'y'
        更新发布状态(测试模式=当前状态)
        print(f"✅ 测试模式已{'启用' if 当前状态 else '禁用'}")
        
    elif 选择 == "3":
        确认 = input("确认重置统计信息? (y/n): ").strip().lower()
        if 确认 == 'y':
            发布器 = 微信自动发布器()
            发布器.重置统计信息()
            print("✅ 统计信息已重置")
        
    elif 选择 == "4":
        确认 = input("确认清理临时文件? (y/n): ").strip().lower()
        if 确认 == 'y':
            发布器 = 微信自动发布器()
            发布器.文章处理器.清理临时文件()
            print("✅ 临时文件已清理")
    
    else:
        print("❌ 无效选择")

def 显示帮助():
    """显示帮助信息"""
    print("\n❓ 帮助信息")
    print("-" * 30)
    print("📖 使用说明:")
    print("   1. 首次使用请先配置微信公众号API信息")
    print("   2. 建议先运行测试确认系统正常")
    print("   3. 默认仅创建草稿，不会直接发布")
    print("   4. 支持多种排版样式和图片处理")
    print("")
    print("📁 相关文件:")
    print("   - README.md: 详细使用说明")
    print("   - 配置指南.md: 配置说明文档")
    print("   - 测试脚本.py: 完整测试套件")
    print("")
    print("⚠️  注意事项:")
    print("   - 请遵守微信公众号API使用规范")
    print("   - 建议在测试模式下验证功能")
    print("   - 敏感信息请使用环境变量管理")

def main():
    """主函数"""
    显示欢迎信息()
    
    while True:
        try:
            显示主菜单()
            选择 = input("请输入选择 (0-8): ").strip()
            
            if 选择 == "1":
                发布单篇文章()
            elif 选择 == "2":
                批量发布文章()
            elif 选择 == "3":
                运行测试()
            elif 选择 == "4":
                查看配置()
            elif 选择 == "5":
                管理草稿()
            elif 选择 == "6":
                查看统计()
            elif 选择 == "7":
                系统设置()
            elif 选择 == "8":
                显示帮助()
            elif 选择 == "0":
                print("👋 感谢使用微信公众号自动发布器！")
                break
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，程序退出")
            break
        except Exception as e:
            print(f"\n❌ 程序异常: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
