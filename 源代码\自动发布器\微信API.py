# -*- coding: utf-8 -*-
"""
微信公众号API集成模块

作者: AI助手
日期: 2025-07-28
功能: 封装微信公众号API接口，实现素材上传、草稿箱管理、文章发布等功能
"""

import os
import sys
import json
import time
import requests
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))
from 微信发布配置 import 获取微信API配置, 获取发布控制配置, 获取安全审核配置

class 微信公众号API:
    """微信公众号API封装类"""
    
    def __init__(self):
        self.api_配置 = 获取微信API配置()
        self.发布配置 = 获取发布控制配置()
        self.安全配置 = 获取安全审核配置()
        self.access_token = None
        self.token_expires_at = 0
        
        # 初始化日志
        self._初始化日志()
    
    def _初始化日志(self):
        """初始化日志系统"""
        import logging
        
        # 创建日志目录
        日志目录 = os.path.dirname(os.path.join(os.path.dirname(__file__), '..', '..', '日志文件'))
        os.makedirs(日志目录, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(日志目录, '微信API.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('微信API')
    
    def 获取访问令牌(self, 强制刷新=False) -> str:
        """获取访问令牌"""
        try:
            # 检查是否需要刷新token
            if not 强制刷新 and self.access_token and time.time() < self.token_expires_at:
                return self.access_token
            
            # 获取新的access_token
            url = self.api_配置['api_endpoints']['token']
            params = {
                'grant_type': 'client_credential',
                'appid': self.api_配置['app_id'],
                'secret': self.api_配置['app_secret']
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if 'access_token' in data:
                self.access_token = data['access_token']
                # 提前5分钟过期，确保安全
                self.token_expires_at = time.time() + data.get('expires_in', 7200) - 300
                
                self.logger.info("✅ 成功获取访问令牌")
                return self.access_token
            else:
                error_msg = f"获取访问令牌失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            self.logger.error(f"❌ 获取访问令牌异常: {str(e)}")
            raise
    
    def 上传图片素材(self, 图片路径: str, 素材类型: str = "permanent") -> str:
        """上传图片素材到微信服务器

        Args:
            图片路径: 图片文件路径
            素材类型: "permanent" 永久素材（用于草稿）, "temporary" 临时素材
        """
        try:
            if not os.path.exists(图片路径):
                raise FileNotFoundError(f"图片文件不存在: {图片路径}")

            # 检查文件大小
            文件大小 = os.path.getsize(图片路径)
            if 文件大小 > 2 * 1024 * 1024:  # 2MB
                raise ValueError(f"图片文件过大: {文件大小} bytes，最大支持2MB")

            # 根据素材类型选择上传方法
            if 素材类型 == "permanent":
                return self._上传永久图片素材(图片路径)
            else:
                return self._上传临时图片素材(图片路径)

        except Exception as e:
            self.logger.error(f"❌ 图片上传异常: {str(e)}")
            raise

    def _上传永久图片素材(self, 图片路径: str) -> str:
        """上传永久图片素材（用于草稿封面）"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()

            # 上传永久素材
            url = f"{self.api_配置['api_endpoints']['material_add']}?access_token={access_token}&type=image"

            with open(图片路径, 'rb') as f:
                files = {'media': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()

            data = response.json()
            if 'media_id' in data:
                media_id = data['media_id']
                self.logger.info(f"✅ 永久图片素材上传成功: {media_id}")
                return media_id
            else:
                error_msg = f"永久图片素材上传失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"❌ 永久图片素材上传异常: {str(e)}")
            raise

    def _上传临时图片素材(self, 图片路径: str) -> str:
        """上传临时图片素材"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()

            # 上传临时素材
            url = f"{self.api_配置['api_endpoints']['media_upload']}?access_token={access_token}&type=image"

            with open(图片路径, 'rb') as f:
                files = {'media': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()

            data = response.json()
            if 'media_id' in data:
                media_id = data['media_id']
                self.logger.info(f"✅ 临时图片素材上传成功: {media_id}")

                # 验证media_id是否有效
                if self._验证media_id有效性(media_id):
                    return media_id
                else:
                    self.logger.warning(f"⚠️  上传的media_id无效，重试上传...")
                    # 重试一次
                    return self._重试上传图片(图片路径, access_token)
            else:
                error_msg = f"临时图片素材上传失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"❌ 临时图片素材上传异常: {str(e)}")
            raise

    def _验证media_id有效性(self, media_id: str) -> bool:
        """验证media_id是否有效"""
        try:
            # 简单验证：检查media_id格式
            if not media_id or len(media_id) < 10:
                return False

            # 可以添加更多验证逻辑，比如尝试获取素材信息
            return True

        except Exception as e:
            self.logger.warning(f"⚠️  media_id验证异常: {str(e)}")
            return False

    def _重试上传图片(self, 图片路径: str, access_token: str) -> str:
        """重试上传图片"""
        try:
            import time
            time.sleep(2)  # 等待2秒后重试

            url = f"{self.api_配置['api_endpoints']['media_upload']}?access_token={access_token}&type=image"

            with open(图片路径, 'rb') as f:
                files = {'media': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()

            data = response.json()
            if 'media_id' in data:
                media_id = data['media_id']
                self.logger.info(f"✅ 重试上传成功: {media_id}")
                return media_id
            else:
                error_msg = f"重试上传失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"❌ 重试上传异常: {str(e)}")
            raise
    
    def 创建草稿(self, 文章数据: Dict) -> str:
        """创建草稿"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()

            # 构建草稿数据
            文章信息 = {
                "title": 文章数据.get('标题', ''),
                "author": 文章数据.get('作者', ''),
                "digest": 文章数据.get('摘要', ''),
                "content": 文章数据.get('内容', ''),
                "content_source_url": 文章数据.get('原文链接', ''),
                "show_cover_pic": 1 if 文章数据.get('显示封面', True) else 0,
                "need_open_comment": 1 if 文章数据.get('开启评论', False) else 0,
                "only_fans_can_comment": 1 if 文章数据.get('仅粉丝评论', False) else 0
            }

            # 处理封面图片ID
            封面图片ID = 文章数据.get('封面图片ID', '')
            if 封面图片ID:
                # 验证封面图片ID是否有效
                if self._验证media_id有效性(封面图片ID):
                    文章信息["thumb_media_id"] = 封面图片ID
                    self.logger.info(f"📷 使用封面图片: {封面图片ID}")
                else:
                    self.logger.warning(f"⚠️  封面图片ID无效，跳过封面图片: {封面图片ID}")
                    # 不添加thumb_media_id字段，让微信使用默认封面

            草稿数据 = {"articles": [文章信息]}

            # 记录调试信息
            self.logger.debug(f"📋 草稿数据: {草稿数据}")

            # 发送请求
            url = f"{self.api_配置['api_endpoints']['draft_add']}?access_token={access_token}"

            # 使用json参数而不是data参数，确保正确的UTF-8编码
            # 根据微信社区反馈，这可以解决中文内容导致的invalid media_id错误
            import json
            headers = {'Content-Type': 'application/json; charset=UTF-8'}
            response = requests.post(
                url,
                data=json.dumps(草稿数据, ensure_ascii=False).encode('utf-8'),
                headers=headers,
                timeout=60
            )
            response.raise_for_status()

            data = response.json()
            self.logger.debug(f"📡 API响应: {data}")

            if data.get('errcode', 0) == 0:
                media_id = data.get('media_id', '')
                self.logger.info(f"✅ 草稿创建成功: {media_id}")
                return media_id
            else:
                error_code = data.get('errcode', 0)
                error_msg = data.get('errmsg', '未知错误')

                # 特殊处理invalid media_id错误
                if 'invalid media_id' in error_msg:
                    self.logger.warning(f"⚠️  可能是中文编码问题，尝试不使用封面图片重新创建草稿")
                    return self._创建无封面草稿(文章数据, access_token)

                full_error_msg = f"草稿创建失败: {error_msg} (错误码: {error_code})"
                self.logger.error(full_error_msg)
                raise Exception(full_error_msg)

        except Exception as e:
            self.logger.error(f"❌ 草稿创建异常: {str(e)}")
            raise

    def _创建无封面草稿(self, 文章数据: Dict, access_token: str) -> str:
        """创建无封面图片的草稿"""
        try:
            self.logger.info("📝 创建无封面图片的草稿...")

            # 构建不包含封面图片的草稿数据
            文章信息 = {
                "title": 文章数据.get('标题', ''),
                "author": 文章数据.get('作者', ''),
                "digest": 文章数据.get('摘要', ''),
                "content": 文章数据.get('内容', ''),
                "content_source_url": 文章数据.get('原文链接', ''),
                "show_cover_pic": 0,  # 不显示封面图片
                "need_open_comment": 1 if 文章数据.get('开启评论', False) else 0,
                "only_fans_can_comment": 1 if 文章数据.get('仅粉丝评论', False) else 0
            }

            草稿数据 = {"articles": [文章信息]}

            # 发送请求，使用正确的UTF-8编码
            url = f"{self.api_配置['api_endpoints']['draft_add']}?access_token={access_token}"

            import json
            headers = {'Content-Type': 'application/json; charset=UTF-8'}
            response = requests.post(
                url,
                data=json.dumps(草稿数据, ensure_ascii=False).encode('utf-8'),
                headers=headers,
                timeout=60
            )
            response.raise_for_status()

            data = response.json()
            if data.get('errcode', 0) == 0:
                media_id = data.get('media_id', '')
                self.logger.info(f"✅ 无封面草稿创建成功: {media_id}")
                return media_id
            else:
                error_msg = f"无封面草稿创建失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            self.logger.error(f"❌ 无封面草稿创建异常: {str(e)}")
            raise
    
    def 获取草稿列表(self, 偏移量=0, 数量=20) -> List[Dict]:
        """获取草稿列表"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()
            
            # 构建请求数据
            请求数据 = {
                "offset": 偏移量,
                "count": 数量,
                "no_content": 0  # 返回内容
            }
            
            # 发送请求
            url = f"{self.api_配置['api_endpoints']['draft_get']}?access_token={access_token}"
            response = requests.post(url, json=请求数据, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            if data.get('errcode', 0) == 0:
                草稿列表 = data.get('item', [])
                self.logger.info(f"✅ 获取草稿列表成功，共 {len(草稿列表)} 篇")
                return 草稿列表
            else:
                error_msg = f"获取草稿列表失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            self.logger.error(f"❌ 获取草稿列表异常: {str(e)}")
            raise
    
    def 删除草稿(self, media_id: str) -> bool:
        """删除草稿"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()
            
            # 构建请求数据
            请求数据 = {"media_id": media_id}
            
            # 发送请求
            url = f"{self.api_配置['api_endpoints']['draft_delete']}?access_token={access_token}"
            response = requests.post(url, json=请求数据, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            if data.get('errcode', 0) == 0:
                self.logger.info(f"✅ 草稿删除成功: {media_id}")
                return True
            else:
                error_msg = f"草稿删除失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 草稿删除异常: {str(e)}")
            return False
    
    def 发布文章(self, media_id: str) -> str:
        """发布文章"""
        try:
            # 检查是否允许发布
            if not self.发布配置['启用自动发布']:
                self.logger.warning("⚠️  自动发布已禁用，仅创建草稿")
                return "draft_only"
            
            if self.发布配置['测试模式']:
                self.logger.info("🧪 测试模式：模拟发布成功")
                return "test_mode"
            
            # 获取访问令牌
            access_token = self.获取访问令牌()
            
            # 构建发布数据
            发布数据 = {"media_id": media_id}
            
            # 发送发布请求
            url = f"{self.api_配置['api_endpoints']['publish_submit']}?access_token={access_token}"
            response = requests.post(url, json=发布数据, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            if data.get('errcode', 0) == 0:
                publish_id = data.get('publish_id', '')
                self.logger.info(f"✅ 文章发布成功: {publish_id}")
                return publish_id
            else:
                error_msg = f"文章发布失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            self.logger.error(f"❌ 文章发布异常: {str(e)}")
            raise
    
    def 检查发布状态(self, publish_id: str) -> Dict:
        """检查发布状态"""
        try:
            # 获取访问令牌
            access_token = self.获取访问令牌()
            
            # 构建请求数据
            请求数据 = {"publish_id": publish_id}
            
            # 发送请求
            url = f"{self.api_配置['api_endpoints']['publish_get']}?access_token={access_token}"
            response = requests.post(url, json=请求数据, timeout=60)
            response.raise_for_status()
            
            data = response.json()
            if data.get('errcode', 0) == 0:
                状态信息 = {
                    'publish_id': publish_id,
                    'publish_status': data.get('publish_status', 0),
                    'article_id': data.get('article_id', ''),
                    'article_url': data.get('article_url', ''),
                    'fail_idx': data.get('fail_idx', [])
                }
                self.logger.info(f"✅ 发布状态查询成功: {状态信息}")
                return 状态信息
            else:
                error_msg = f"发布状态查询失败: {data.get('errmsg', '未知错误')}"
                self.logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            self.logger.error(f"❌ 发布状态查询异常: {str(e)}")
            raise
    
    def 验证配置(self) -> Tuple[bool, List[str]]:
        """验证API配置"""
        errors = []
        
        if not self.api_配置['app_id']:
            errors.append("缺少微信APP_ID")
        
        if not self.api_配置['app_secret']:
            errors.append("缺少微信APP_SECRET")
        
        # 测试API连接
        try:
            self.获取访问令牌()
        except Exception as e:
            errors.append(f"API连接测试失败: {str(e)}")
        
        return len(errors) == 0, errors
    
    def 获取API状态(self) -> Dict:
        """获取API状态信息"""
        return {
            'access_token_valid': bool(self.access_token and time.time() < self.token_expires_at),
            'token_expires_at': datetime.fromtimestamp(self.token_expires_at).strftime('%Y-%m-%d %H:%M:%S') if self.token_expires_at else None,
            'auto_publish_enabled': self.发布配置['启用自动发布'],
            'test_mode': self.发布配置['测试模式'],
            'draft_mode': self.发布配置['草稿箱模式']
        }


def 测试微信API():
    """测试微信API功能"""
    print("🧪 开始测试微信API...")
    
    api = 微信公众号API()
    
    # 验证配置
    配置有效, 错误列表 = api.验证配置()
    if not 配置有效:
        print("❌ 配置验证失败:")
        for error in 错误列表:
            print(f"   - {error}")
        return False
    
    print("✅ 配置验证通过")
    
    # 获取API状态
    状态信息 = api.获取API状态()
    print(f"📊 API状态: {状态信息}")
    
    # 测试获取草稿列表
    try:
        草稿列表 = api.获取草稿列表(数量=5)
        print(f"📝 当前草稿数量: {len(草稿列表)}")
    except Exception as e:
        print(f"⚠️  获取草稿列表失败: {str(e)}")
    
    print("✅ 微信API测试完成")
    return True


if __name__ == "__main__":
    测试微信API()
