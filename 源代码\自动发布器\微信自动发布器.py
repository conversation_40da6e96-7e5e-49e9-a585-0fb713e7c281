# -*- coding: utf-8 -*-
"""
微信公众号自动发布器主程序

作者: AI助手
日期: 2025-07-28
功能: 整合所有模块，实现完整的微信公众号自动发布流程
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))
from 微信发布配置 import (
    获取微信API配置, 获取发布控制配置, 获取内容处理配置,
    获取安全审核配置, 更新发布状态, 验证配置, 显示配置摘要
)

# 导入模块
from 微信API import 微信公众号API
from 文章处理器 import 文章处理器
from 排版模板 import 排版模板管理器
from 日志系统 import 日志管理器, 错误处理装饰器, 性能监控装饰器
from AI配图系统 import AI配图生成器

class 微信自动发布器:
    """微信公众号自动发布器主类"""
    
    def __init__(self):
        # 初始化组件
        self.微信API = 微信公众号API()
        self.文章处理器 = 文章处理器()
        self.模板管理器 = 排版模板管理器()
        self.AI配图生成器 = AI配图生成器()
        self.日志管理器 = 日志管理器("自动发布器")
        
        # 加载配置
        self.发布配置 = 获取发布控制配置()
        self.安全配置 = 获取安全审核配置()
        
        # 发布统计
        self.发布统计 = {
            'total_processed': 0,
            'successful_uploads': 0,
            'successful_publishes': 0,
            'failed_uploads': 0,
            'failed_publishes': 0,
            'start_time': None,
            'last_publish_time': None
        }
        
        self.日志管理器.记录信息("🚀 微信自动发布器初始化完成")
    
    @错误处理装饰器(错误类型="validation")
    def 验证系统配置(self) -> Tuple[bool, List[str]]:
        """验证系统配置"""
        self.日志管理器.记录信息("🔍 开始验证系统配置...")
        
        错误列表 = []
        
        # 验证微信API配置
        api有效, api错误 = self.微信API.验证配置()
        if not api有效:
            错误列表.extend([f"微信API: {error}" for error in api错误])
        
        # 验证发布配置
        if not self.发布配置:
            错误列表.append("缺少发布控制配置")
        
        # 验证安全配置
        if not self.安全配置:
            错误列表.append("缺少安全审核配置")
        
        # 验证必要目录
        必要目录 = [
            os.path.join(os.path.dirname(__file__), 'temp'),
            os.path.join(os.path.dirname(__file__), '..', '..', '日志文件'),
            os.path.join(os.path.dirname(__file__), '..', '..', '数据存储')
        ]
        
        for 目录 in 必要目录:
            if not os.path.exists(目录):
                try:
                    os.makedirs(目录, exist_ok=True)
                    self.日志管理器.记录信息(f"✅ 创建目录: {目录}")
                except Exception as e:
                    错误列表.append(f"无法创建目录 {目录}: {str(e)}")
        
        配置有效 = len(错误列表) == 0
        if 配置有效:
            self.日志管理器.记录信息("✅ 系统配置验证通过")
        else:
            self.日志管理器.记录错误("❌ 系统配置验证失败", None, "validation", {"errors": 错误列表})
        
        return 配置有效, 错误列表
    
    @性能监控装饰器("文章发布")
    @错误处理装饰器(错误类型="api", 重试次数=2, 重试延迟=60)
    def 发布文章(self, 文章数据: Dict, 发布选项: Dict = None) -> Dict:
        """发布单篇文章"""
        发布选项 = 发布选项 or {}
        文章标题 = 文章数据.get('标题', '未知标题')
        
        self.日志管理器.记录信息(f"📝 开始发布文章: {文章标题}")
        
        发布结果 = {
            'success': False,
            'article_title': 文章标题,
            'media_id': None,
            'publish_id': None,
            'draft_only': False,
            'error_message': None,
            'process_time': None,
            'upload_time': None,
            'publish_time': None
        }
        
        开始时间 = datetime.now()
        
        try:
            # 1. 处理文章内容
            self.日志管理器.记录信息("🔄 处理文章内容...")

            # 准备传递给文章处理器的参数
            处理参数 = {
                '排版样式': 发布选项.get('排版样式', 'business'),
                '发布日期': datetime.now().strftime('%Y年%m月%d日'),
                **文章数据.get('元数据', {})
            }

            处理结果 = self.文章处理器.处理文章内容(
                原始内容=文章数据.get('内容', ''),
                标题=文章标题,
                **处理参数
            )
            
            # 2. 验证文章内容
            验证通过, 验证错误 = self.文章处理器.验证文章内容(处理结果)
            if not 验证通过:
                raise ValueError(f"文章验证失败: {', '.join(验证错误)}")
            
            # 3. 免费配图（如果启用）
            封面图片ID = None
            配图结果 = None

            if 发布选项.get('启用AI配图', False):
                try:
                    self.日志管理器.记录信息("🎨 开始免费配图...")
                    配图服务 = 发布选项.get('AI配图服务', 'picsum')

                    # 使用批量搜索配图
                    配图结果 = self.AI配图生成器.批量搜索配图(
                        文章内容=文章数据.get('内容', ''),
                        标题=文章标题,
                        服务=配图服务
                    )

                    # 上传搜索到的图片
                    if 配图结果['搜索的图片']:
                        self.日志管理器.记录信息(f"📤 上传 {len(配图结果['搜索的图片'])} 张配图...")

                        # 分别处理封面图和内容图
                        封面图片列表 = []
                        内容图片列表 = []

                        for 图片信息 in 配图结果['搜索的图片']:
                            try:
                                # 延迟上传，避免频率限制
                                import time
                                time.sleep(0.5)

                                # 封面图使用永久素材，内容图使用临时素材
                                if 图片信息['类型'] == '封面图':
                                    图片ID = self.微信API.上传图片素材(图片信息['文件路径'], "permanent")
                                    封面图片列表.append(图片ID)
                                    self.日志管理器.记录信息(f"✅ 封面图（永久素材）上传成功: {图片ID}")
                                else:
                                    图片ID = self.微信API.上传图片素材(图片信息['文件路径'], "temporary")
                                    内容图片列表.append(图片ID)
                                    self.日志管理器.记录信息(f"✅ 内容图（临时素材）上传成功: {图片ID}")

                            except Exception as e:
                                self.日志管理器.记录警告(f"⚠️  配图上传失败: {str(e)}")

                        # 选择最新的封面图
                        if 封面图片列表:
                            封面图片ID = 封面图片列表[-1]  # 使用最后一个成功上传的封面图
                            self.日志管理器.记录信息(f"📷 选择封面图: {封面图片ID}")

                except Exception as e:
                    self.日志管理器.记录警告(f"⚠️  免费配图失败: {str(e)}")
                    # 降级到本地配图
                    try:
                        self.日志管理器.记录信息("🔄 降级到本地配图...")
                        本地图片路径 = self.AI配图生成器._创建本地配图(文章标题)
                        if 本地图片路径:
                            import time
                            time.sleep(1)  # 等待1秒
                            图片ID = self.微信API.上传图片素材(本地图片路径, "permanent")
                            封面图片ID = 图片ID
                            self.日志管理器.记录信息(f"✅ 本地配图（永久素材）上传成功: {图片ID}")
                    except Exception as e2:
                        self.日志管理器.记录警告(f"⚠️  本地配图也失败: {str(e2)}")

            # 4. 处理原有图片
            if 处理结果['图片列表']:
                self.日志管理器.记录信息(f"📷 上传 {len(处理结果['图片列表'])} 张原有图片...")
                for 图片信息 in 处理结果['图片列表']:
                    try:
                        # 如果没有封面图，第一张原有图片作为封面图（永久素材）
                        if not 封面图片ID:
                            图片ID = self.微信API.上传图片素材(图片信息['本地路径'], "permanent")
                            封面图片ID = 图片ID
                            self.日志管理器.记录信息(f"✅ 原有图片作为封面图（永久素材）上传成功: {图片ID}")
                        else:
                            图片ID = self.微信API.上传图片素材(图片信息['本地路径'], "temporary")
                            self.日志管理器.记录信息(f"✅ 原有图片（临时素材）上传成功: {图片ID}")
                    except Exception as e:
                        self.日志管理器.记录警告(f"⚠️  图片上传失败: {str(e)}")

            # 5. 确保有封面图片（微信草稿API要求必须有封面图片）
            if not 封面图片ID:
                self.日志管理器.记录信息("📷 没有封面图片，创建默认封面...")
                try:
                    # 创建默认封面图片
                    默认封面路径 = self._创建默认封面图片(文章标题)
                    if 默认封面路径:
                        封面图片ID = self.微信API.上传图片素材(默认封面路径, "permanent")
                        self.日志管理器.记录信息(f"✅ 默认封面图片上传成功: {封面图片ID}")
                    else:
                        raise Exception("无法创建默认封面图片")
                except Exception as e:
                    self.日志管理器.记录警告(f"⚠️  默认封面图片创建失败: {str(e)}")
                    # 最后的备选方案：使用纯色图片
                    try:
                        纯色封面路径 = self._创建纯色封面图片()
                        封面图片ID = self.微信API.上传图片素材(纯色封面路径, "permanent")
                        self.日志管理器.记录信息(f"✅ 纯色封面图片上传成功: {封面图片ID}")
                    except Exception as e2:
                        self.日志管理器.记录错误(f"❌ 纯色封面图片也失败: {str(e2)}")
                        # 如果连纯色图片都无法创建，抛出异常
                        raise Exception("无法创建任何封面图片，草稿创建将失败")
            
            # 6. 构建微信文章数据
            微信文章数据 = {
                '标题': 处理结果['标题'],
                '内容': 处理结果['格式化内容'],
                '摘要': 处理结果['摘要'],
                '作者': 处理结果['元数据'].get('作者', ''),
                '原文链接': 处理结果['元数据'].get('原文链接', ''),
                '显示封面': 发布选项.get('显示封面', True),
                '开启评论': 发布选项.get('开启评论', False),
                '仅粉丝评论': 发布选项.get('仅粉丝评论', False),
                '封面图片ID': 封面图片ID  # 现在总是有封面图片ID
            }
            
            # 5. 创建草稿
            上传开始时间 = datetime.now()
            self.日志管理器.记录信息("📤 创建草稿...")

            # 记录草稿数据用于调试
            self.日志管理器.记录信息(f"📋 草稿标题: {微信文章数据.get('标题', '')}")
            self.日志管理器.记录信息(f"📋 内容长度: {len(微信文章数据.get('内容', ''))} 字符")
            if 微信文章数据.get('封面图片ID'):
                self.日志管理器.记录信息(f"📋 封面图片ID: {微信文章数据['封面图片ID']}")

            try:
                media_id = self.微信API.创建草稿(微信文章数据)
                发布结果['media_id'] = media_id
                发布结果['upload_time'] = (datetime.now() - 上传开始时间).total_seconds()

                self.日志管理器.记录信息(f"✅ 草稿创建成功: {media_id}")

            except Exception as e:
                error_msg = str(e)
                self.日志管理器.记录错误(f"❌ 草稿创建失败: {error_msg}")

                # 如果是media_id相关错误，尝试不使用封面图重新创建
                if 'invalid media_id' in error_msg and 微信文章数据.get('封面图片ID'):
                    self.日志管理器.记录信息("🔄 尝试不使用封面图重新创建草稿...")
                    try:
                        # 移除封面图片ID
                        微信文章数据_无封面 = 微信文章数据.copy()
                        微信文章数据_无封面.pop('封面图片ID', None)
                        微信文章数据_无封面['显示封面'] = False

                        media_id = self.微信API.创建草稿(微信文章数据_无封面)
                        发布结果['media_id'] = media_id
                        发布结果['upload_time'] = (datetime.now() - 上传开始时间).total_seconds()

                        self.日志管理器.记录信息(f"✅ 无封面草稿创建成功: {media_id}")

                    except Exception as e2:
                        self.日志管理器.记录错误(f"❌ 无封面草稿创建也失败: {str(e2)}")
                        raise e  # 抛出原始错误
                else:
                    raise e  # 抛出原始错误
            
            # 6. 发布文章（如果启用）
            if self.发布配置['启用自动发布'] and not 发布选项.get('仅草稿', False):
                if self.发布配置['发布前确认'] and not 发布选项.get('跳过确认', False):
                    确认结果 = self._请求发布确认(文章标题, media_id)
                    if not 确认结果:
                        self.日志管理器.记录信息("⏸️  用户取消发布，文章保存为草稿")
                        发布结果['draft_only'] = True
                        发布结果['success'] = True
                        return 发布结果
                
                发布开始时间 = datetime.now()
                self.日志管理器.记录信息("🚀 发布文章...")
                publish_id = self.微信API.发布文章(media_id)
                发布结果['publish_id'] = publish_id
                发布结果['publish_time'] = (datetime.now() - 发布开始时间).total_seconds()
                
                if publish_id == "test_mode":
                    self.日志管理器.记录信息("🧪 测试模式发布完成")
                elif publish_id == "draft_only":
                    self.日志管理器.记录信息("📝 仅创建草稿模式")
                    发布结果['draft_only'] = True
                else:
                    self.日志管理器.记录信息(f"✅ 文章发布成功: {publish_id}")
                    self.发布统计['successful_publishes'] += 1
                    self.发布统计['last_publish_time'] = datetime.now().isoformat()
            else:
                self.日志管理器.记录信息("📝 仅创建草稿（自动发布已禁用）")
                发布结果['draft_only'] = True
            
            # 7. 更新统计
            self.发布统计['total_processed'] += 1
            self.发布统计['successful_uploads'] += 1
            
            发布结果['success'] = True
            发布结果['process_time'] = (datetime.now() - 开始时间).total_seconds()
            
            self.日志管理器.记录信息(f"🎉 文章处理完成: {文章标题}")
            
            # 8. 清理临时文件
            self.文章处理器.清理临时文件()
            
            return 发布结果
            
        except Exception as e:
            self.发布统计['failed_uploads'] += 1
            发布结果['error_message'] = str(e)
            发布结果['process_time'] = (datetime.now() - 开始时间).total_seconds()
            
            self.日志管理器.记录错误(f"❌ 文章发布失败: {文章标题}", e, "api")
            raise
    
    def _请求发布确认(self, 文章标题: str, media_id: str) -> bool:
        """请求发布确认"""
        if self.发布配置['测试模式']:
            return True
        
        print(f"\n📝 准备发布文章: {文章标题}")
        print(f"📄 草稿ID: {media_id}")
        print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        while True:
            确认 = input("\n是否确认发布？(y/n/s=跳过): ").lower().strip()
            if 确认 in ['y', 'yes', '是', '确认']:
                return True
            elif 确认 in ['n', 'no', '否', '取消']:
                return False
            elif 确认 in ['s', 'skip', '跳过']:
                return False
            else:
                print("请输入 y(确认) 或 n(取消) 或 s(跳过)")
    
    def 批量发布文章(self, 文章列表: List[Dict], 发布选项: Dict = None) -> List[Dict]:
        """批量发布文章"""
        发布选项 = 发布选项 or {}
        
        self.日志管理器.记录信息(f"📚 开始批量发布 {len(文章列表)} 篇文章")
        self.发布统计['start_time'] = datetime.now().isoformat()
        
        发布结果列表 = []
        
        for i, 文章数据 in enumerate(文章列表, 1):
            try:
                self.日志管理器.记录信息(f"📝 处理第 {i}/{len(文章列表)} 篇文章")
                
                # 检查发布频率限制
                if not self._检查发布频率():
                    self.日志管理器.记录警告("⚠️  达到发布频率限制，跳过后续文章")
                    break
                
                # 发布文章
                结果 = self.发布文章(文章数据, 发布选项)
                发布结果列表.append(结果)
                
                # 发布间隔
                if i < len(文章列表) and 结果['success']:
                    间隔时间 = 发布选项.get('发布间隔', 60)  # 默认60秒间隔
                    if 间隔时间 > 0:
                        self.日志管理器.记录信息(f"⏱️  等待 {间隔时间} 秒...")
                        time.sleep(间隔时间)
                
            except Exception as e:
                错误结果 = {
                    'success': False,
                    'article_title': 文章数据.get('标题', f'第{i}篇文章'),
                    'error_message': str(e)
                }
                发布结果列表.append(错误结果)
                self.日志管理器.记录错误(f"❌ 第 {i} 篇文章处理失败", e)
        
        # 输出批量发布统计
        成功数量 = sum(1 for r in 发布结果列表 if r['success'])
        失败数量 = len(发布结果列表) - 成功数量
        
        self.日志管理器.记录信息(f"📊 批量发布完成: 成功 {成功数量} 篇，失败 {失败数量} 篇")
        
        return 发布结果列表
    
    def _检查发布频率(self) -> bool:
        """检查发布频率限制"""
        频率限制 = self.发布配置['频率限制']
        
        # 检查每日发布数量
        if self.发布统计['successful_publishes'] >= 频率限制['每日最大发布数']:
            return False
        
        # 检查最小间隔
        if self.发布统计['last_publish_time']:
            上次发布时间 = datetime.fromisoformat(self.发布统计['last_publish_time'])
            最小间隔 = timedelta(hours=频率限制['最小间隔小时'])
            if datetime.now() - 上次发布时间 < 最小间隔:
                return False
        
        return True
    
    def 获取草稿列表(self, 数量: int = 20) -> List[Dict]:
        """获取草稿列表"""
        try:
            return self.微信API.获取草稿列表(数量=数量)
        except Exception as e:
            self.日志管理器.记录错误("获取草稿列表失败", e, "api")
            return []
    
    def 删除草稿(self, media_id: str) -> bool:
        """删除草稿"""
        try:
            return self.微信API.删除草稿(media_id)
        except Exception as e:
            self.日志管理器.记录错误(f"删除草稿失败: {media_id}", e, "api")
            return False
    
    def 获取发布统计(self) -> Dict:
        """获取发布统计信息"""
        统计信息 = self.发布统计.copy()
        统计信息.update({
            'api_status': self.微信API.获取API状态(),
            'error_stats': self.日志管理器.获取错误统计(),
            'processor_stats': self.文章处理器.获取处理统计()
        })
        return 统计信息
    
    def 重置统计信息(self):
        """重置统计信息"""
        self.发布统计 = {
            'total_processed': 0,
            'successful_uploads': 0,
            'successful_publishes': 0,
            'failed_uploads': 0,
            'failed_publishes': 0,
            'start_time': None,
            'last_publish_time': None
        }
        self.日志管理器.重置错误统计()
        self.日志管理器.记录信息("🔄 统计信息已重置")

    def _创建默认封面图片(self, 标题: str) -> str:
        """创建默认封面图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import os
            import random
            from datetime import datetime

            # 创建图片目录
            图片目录 = os.path.join(os.path.dirname(os.path.dirname(__file__)), "生成的图片")
            if not os.path.exists(图片目录):
                os.makedirs(图片目录)

            # 创建一个简单的封面图片
            宽度, 高度 = 900, 500

            # 随机选择背景颜色
            背景颜色列表 = [
                (173, 216, 230),  # 浅蓝色
                (144, 238, 144),  # 浅绿色
                (255, 182, 193),  # 浅粉色
                (255, 222, 173),  # 浅橙色
                (230, 230, 250)   # 浅紫色
            ]
            背景颜色 = random.choice(背景颜色列表)

            # 创建图片
            图片 = Image.new('RGB', (宽度, 高度), color=背景颜色)
            绘制 = ImageDraw.Draw(图片)

            # 尝试加载字体
            字体大小 = 40
            字体 = None
            try:
                # 尝试使用系统字体
                字体路径列表 = [
                    "C:/Windows/Fonts/simhei.ttf",  # Windows 黑体
                    "C:/Windows/Fonts/simsun.ttc",  # Windows 宋体
                    "C:/Windows/Fonts/msyh.ttc",    # Windows 微软雅黑
                    "/System/Library/Fonts/PingFang.ttc"  # macOS 苹方
                ]

                for 路径 in 字体路径列表:
                    if os.path.exists(路径):
                        字体 = ImageFont.truetype(路径, 字体大小)
                        break
            except:
                pass

            if not 字体:
                # 如果没有找到字体，使用默认字体
                字体 = ImageFont.load_default()

            # 绘制标题
            标题行数 = len(标题) // 15 + 1  # 每行约15个字
            行高 = 字体大小 + 10
            起始Y = (高度 - 标题行数 * 行高) // 2

            for i in range(标题行数):
                if i < 标题行数 - 1:
                    行文本 = 标题[i*15:(i+1)*15]
                else:
                    行文本 = 标题[i*15:]

                文本宽度 = 字体.getlength(行文本) if hasattr(字体, 'getlength') else len(行文本) * 字体大小 // 2
                绘制.text(((宽度 - 文本宽度) // 2, 起始Y + i * 行高), 行文本, fill=(0, 0, 0), font=字体)

            # 绘制日期
            日期文本 = datetime.now().strftime("%Y-%m-%d")
            日期字体大小 = 字体大小 // 2
            日期字体 = 字体
            日期Y = 高度 - 日期字体大小 - 20
            日期宽度 = 日期字体.getlength(日期文本) if hasattr(日期字体, 'getlength') else len(日期文本) * 日期字体大小 // 2
            绘制.text(((宽度 - 日期宽度) // 2, 日期Y), 日期文本, fill=(100, 100, 100), font=日期字体)

            # 保存图片
            图片路径 = os.path.join(图片目录, f"默认封面_{datetime.now().strftime('%Y%m%d%H%M%S')}.jpg")
            图片.save(图片路径, "JPEG", quality=95)

            self.日志管理器.记录信息(f"✅ 默认封面图片创建成功: {图片路径}")
            return 图片路径

        except Exception as e:
            self.日志管理器.记录错误(f"❌ 默认封面图片创建失败: {str(e)}")
            return None

    def _创建纯色封面图片(self) -> str:
        """创建纯色封面图片（最简单的备选方案）"""
        try:
            from PIL import Image
            import os
            from datetime import datetime

            # 创建图片目录
            图片目录 = os.path.join(os.path.dirname(os.path.dirname(__file__)), "生成的图片")
            if not os.path.exists(图片目录):
                os.makedirs(图片目录)

            # 创建一个纯色图片
            宽度, 高度 = 800, 600
            图片 = Image.new('RGB', (宽度, 高度), color=(240, 240, 240))  # 浅灰色

            # 保存图片
            图片路径 = os.path.join(图片目录, f"纯色封面_{datetime.now().strftime('%Y%m%d%H%M%S')}.jpg")
            图片.save(图片路径, "JPEG", quality=90)

            self.日志管理器.记录信息(f"✅ 纯色封面图片创建成功: {图片路径}")
            return 图片路径

        except Exception as e:
            self.日志管理器.记录错误(f"❌ 纯色封面图片创建失败: {str(e)}")
            return None


def 创建示例文章() -> Dict:
    """创建示例文章用于测试"""
    return {
        '标题': '微信公众号自动发布测试文章',
        '内容': '''
# 微信公众号自动发布系统测试

这是一篇用于测试微信公众号自动发布系统的示例文章。

## 系统特性

本系统具有以下特性：

- **自动化发布**: 支持文章的自动化处理和发布
- **多种排版**: 提供商务、科技、生活、学术等多种排版风格
- **安全可靠**: 包含完善的错误处理和日志记录机制
- **灵活配置**: 支持测试模式和生产模式切换

## 使用方法

1. 配置微信公众号API信息
2. 准备文章内容
3. 选择排版样式
4. 执行发布流程

> 注意：默认情况下系统运行在测试模式，不会实际发布文章。

## 总结

微信公众号自动发布系统为内容创作者提供了便捷的发布工具，提高了工作效率。

感谢使用本系统！
        ''',
        '元数据': {
            '作者': 'AI助手',
            '来源': '自动发布系统',
            '标签': ['测试', '自动化', '微信公众号'],
            '分类': '技术文档'
        }
    }


if __name__ == "__main__":
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='微信公众号自动发布器')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--config', action='store_true', help='显示配置信息')
    parser.add_argument('--draft-list', action='store_true', help='显示草稿列表')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    
    args = parser.parse_args()
    
    # 创建发布器实例
    发布器 = 微信自动发布器()
    
    if args.config:
        # 显示配置信息
        显示配置摘要()
        配置有效, 错误列表 = 发布器.验证系统配置()
        if not 配置有效:
            print("\n❌ 配置问题:")
            for error in 错误列表:
                print(f"   - {error}")
    
    elif args.draft_list:
        # 显示草稿列表
        草稿列表 = 发布器.获取草稿列表()
        print(f"\n📝 草稿列表 (共 {len(草稿列表)} 篇):")
        for i, 草稿 in enumerate(草稿列表, 1):
            print(f"{i}. {草稿.get('title', '无标题')} - {草稿.get('media_id', '')}")
    
    elif args.stats:
        # 显示统计信息
        统计信息 = 发布器.获取发布统计()
        print(f"\n📊 发布统计信息:")
        print(json.dumps(统计信息, ensure_ascii=False, indent=2))
    
    elif args.test:
        # 运行测试
        print("🧪 开始测试微信自动发布器...")
        
        # 验证配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        if not 配置有效:
            print("❌ 配置验证失败，无法继续测试")
            sys.exit(1)
        
        # 创建测试文章
        测试文章 = 创建示例文章()
        
        # 发布测试文章
        try:
            结果 = 发布器.发布文章(测试文章, {'仅草稿': True})
            if 结果['success']:
                print("✅ 测试发布成功")
                print(f"📄 草稿ID: {结果['media_id']}")
            else:
                print(f"❌ 测试发布失败: {结果['error_message']}")
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
    
    else:
        # 显示帮助信息
        print("🚀 微信公众号自动发布器")
        print("使用 --help 查看可用选项")
        print("使用 --test 运行测试")
        print("使用 --config 查看配置")
        print("使用 --draft-list 查看草稿列表")
        print("使用 --stats 查看统计信息")
