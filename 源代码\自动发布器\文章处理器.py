# -*- coding: utf-8 -*-
"""
文章处理和格式化模块

作者: AI助手
日期: 2025-07-28
功能: 实现文章内容处理、排版应用、图片处理等功能
"""

import os
import sys
import re
import json
import hashlib
import requests
from PIL import Image
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))
from 微信发布配置 import 获取内容处理配置, 获取排版样式配置

# 导入排版模板
from 排版模板 import 排版模板管理器

class 文章处理器:
    """文章处理器类"""
    
    def __init__(self):
        self.内容配置 = 获取内容处理配置()
        self.排版配置 = 获取排版样式配置()
        self.模板管理器 = 排版模板管理器()
        
        # 创建临时目录
        self.临时目录 = os.path.join(os.path.dirname(__file__), 'temp')
        os.makedirs(self.临时目录, exist_ok=True)
        
        # 初始化日志
        self._初始化日志()
    
    def _初始化日志(self):
        """初始化日志系统"""
        import logging
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger('文章处理器')
    
    def 处理文章内容(self, 原始内容: str, 标题: str = "", **kwargs) -> Dict:
        """处理文章内容"""
        try:
            self.logger.info(f"📝 开始处理文章: {标题[:30]}...")
            
            # 1. 清理和格式化文本
            清理后内容 = self._清理文本内容(原始内容)
            
            # 2. 提取和处理图片
            内容, 图片列表 = self._处理图片(清理后内容)
            
            # 3. 生成摘要
            摘要 = self._生成摘要(内容, 标题)
            
            # 4. 应用排版样式
            样式名称 = kwargs.get('排版样式', self.排版配置['默认样式'])
            格式化内容 = self.模板管理器.应用排版样式(
                内容=内容,
                标题=标题,
                样式名称=样式名称,
                **kwargs
            )
            
            # 5. 构建结果
            处理结果 = {
                '原始内容': 原始内容,
                '清理后内容': 清理后内容,
                '格式化内容': 格式化内容,
                '标题': 标题,
                '摘要': 摘要,
                '图片列表': 图片列表,
                '排版样式': 样式名称,
                '字数统计': len(清理后内容),
                '处理时间': datetime.now().isoformat(),
                '元数据': {
                    '作者': kwargs.get('作者', ''),
                    '来源': kwargs.get('来源', ''),
                    '原文链接': kwargs.get('原文链接', ''),
                    '标签': kwargs.get('标签', []),
                    '分类': kwargs.get('分类', '')
                }
            }
            
            self.logger.info(f"✅ 文章处理完成，字数: {处理结果['字数统计']}")
            return 处理结果
            
        except Exception as e:
            self.logger.error(f"❌ 文章处理失败: {str(e)}")
            raise
    
    def _清理文本内容(self, 内容: str) -> str:
        """清理文本内容"""
        # 移除多余的空行
        if self.内容配置['文本处理']['移除多余空行']:
            内容 = re.sub(r'\n\s*\n\s*\n', '\n\n', 内容)
        
        # 统一标点符号
        if self.内容配置['文本处理']['统一标点符号']:
            内容 = self._统一标点符号(内容)
        
        # 处理段落间距
        段落间距 = self.内容配置['文本处理'].get('段落间距', 2)
        if 段落间距 > 1:
            内容 = re.sub(r'\n\n', '\n' * 段落间距, 内容)
        
        return 内容.strip()
    
    def _统一标点符号(self, 内容: str) -> str:
        """统一标点符号"""
        # 中文标点符号替换
        替换规则 = {
            ',': '，',
            '.': '。',
            '?': '？',
            '!': '！',
            ':': '：',
            ';': '；',
            '"': '"',
            '"': '"',
            "'": ''',
            "'": '''
        }
        
        for 英文, 中文 in 替换规则.items():
            内容 = 内容.replace(英文, 中文)
        
        return 内容
    
    def _处理图片(self, 内容: str) -> Tuple[str, List[Dict]]:
        """处理图片"""
        图片列表 = []
        
        if not self.内容配置['图片处理']['启用图片上传']:
            return 内容, 图片列表
        
        # 查找图片链接
        图片模式 = r'!\[([^\]]*)\]\(([^)]+)\)'
        图片匹配 = re.findall(图片模式, 内容)
        
        for 图片描述, 图片链接 in 图片匹配:
            try:
                # 下载和处理图片
                处理结果 = self._下载并处理图片(图片链接, 图片描述)
                if 处理结果:
                    图片列表.append(处理结果)
                    
                    # 替换内容中的图片链接
                    原始标记 = f'![{图片描述}]({图片链接})'
                    新标记 = f'![{图片描述}]({处理结果["本地路径"]})'
                    内容 = 内容.replace(原始标记, 新标记)
                    
            except Exception as e:
                self.logger.warning(f"⚠️  图片处理失败 {图片链接}: {str(e)}")
        
        return 内容, 图片列表
    
    def _下载并处理图片(self, 图片链接: str, 描述: str = "") -> Optional[Dict]:
        """下载并处理图片"""
        try:
            # 检查是否为有效URL
            parsed_url = urlparse(图片链接)
            if not parsed_url.scheme:
                return None
            
            # 下载图片
            response = requests.get(图片链接, timeout=30, stream=True)
            response.raise_for_status()
            
            # 生成文件名
            文件扩展名 = os.path.splitext(parsed_url.path)[1] or '.jpg'
            文件名 = hashlib.md5(图片链接.encode()).hexdigest() + 文件扩展名
            本地路径 = os.path.join(self.临时目录, 文件名)
            
            # 保存图片
            with open(本地路径, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 处理图片
            处理后路径 = self._压缩图片(本地路径)
            
            return {
                '原始链接': 图片链接,
                '本地路径': 处理后路径,
                '描述': 描述,
                '文件大小': os.path.getsize(处理后路径),
                '文件名': os.path.basename(处理后路径)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 图片下载失败 {图片链接}: {str(e)}")
            return None
    
    def _压缩图片(self, 图片路径: str) -> str:
        """压缩图片"""
        try:
            if not self.内容配置['图片处理']['图片压缩']:
                return 图片路径
            
            with Image.open(图片路径) as img:
                # 转换为RGB模式
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 调整尺寸
                最大宽度 = self.内容配置['图片处理'].get('最大宽度', 900)
                if img.width > 最大宽度:
                    比例 = 最大宽度 / img.width
                    新高度 = int(img.height * 比例)
                    img = img.resize((最大宽度, 新高度), Image.Resampling.LANCZOS)
                
                # 保存压缩后的图片
                压缩路径 = 图片路径.replace('.', '_compressed.')
                质量 = self.内容配置['图片处理']['图片质量']
                img.save(压缩路径, 'JPEG', quality=质量, optimize=True)
                
                # 检查文件大小
                if os.path.getsize(压缩路径) < os.path.getsize(图片路径):
                    os.remove(图片路径)
                    return 压缩路径
                else:
                    os.remove(压缩路径)
                    return 图片路径
                    
        except Exception as e:
            self.logger.warning(f"⚠️  图片压缩失败: {str(e)}")
            return 图片路径
    
    def _生成摘要(self, 内容: str, 标题: str = "") -> str:
        """生成文章摘要"""
        if not self.内容配置['文本处理']['自动生成摘要']:
            return ""
        
        # 移除Markdown标记
        纯文本 = re.sub(r'[#*`\[\]()!]', '', 内容)
        纯文本 = re.sub(r'\n+', ' ', 纯文本)
        
        # 提取前几句话作为摘要
        句子列表 = re.split(r'[。！？]', 纯文本)
        摘要句子 = []
        摘要长度 = 0
        最大长度 = self.内容配置['文本处理']['最大摘要长度']
        
        for 句子 in 句子列表:
            句子 = 句子.strip()
            if not 句子:
                continue
                
            if 摘要长度 + len(句子) <= 最大长度:
                摘要句子.append(句子)
                摘要长度 += len(句子)
            else:
                break
        
        摘要 = '。'.join(摘要句子)
        if 摘要 and not 摘要.endswith('。'):
            摘要 += '。'
        
        return 摘要
    
    def 验证文章内容(self, 文章数据: Dict) -> Tuple[bool, List[str]]:
        """验证文章内容"""
        错误列表 = []
        
        # 检查标题
        标题 = 文章数据.get('标题', '')
        if not 标题:
            错误列表.append("缺少文章标题")
        elif len(标题) > self.内容配置['文本处理']['最大标题长度']:
            错误列表.append(f"标题过长，最大长度: {self.内容配置['文本处理']['最大标题长度']}")
        
        # 检查内容
        内容 = 文章数据.get('清理后内容', '')
        if not 内容:
            错误列表.append("缺少文章内容")
        elif len(内容) < 100:
            错误列表.append("文章内容过短")
        
        # 检查摘要
        摘要 = 文章数据.get('摘要', '')
        if 摘要 and len(摘要) > self.内容配置['文本处理']['最大摘要长度']:
            错误列表.append(f"摘要过长，最大长度: {self.内容配置['文本处理']['最大摘要长度']}")
        
        return len(错误列表) == 0, 错误列表
    
    def 清理临时文件(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.临时目录):
                shutil.rmtree(self.临时目录)
                os.makedirs(self.临时目录, exist_ok=True)
            self.logger.info("🧹 临时文件清理完成")
        except Exception as e:
            self.logger.warning(f"⚠️  临时文件清理失败: {str(e)}")
    
    def 获取处理统计(self) -> Dict:
        """获取处理统计信息"""
        return {
            '临时目录': self.临时目录,
            '临时文件数': len(os.listdir(self.临时目录)) if os.path.exists(self.临时目录) else 0,
            '支持的图片格式': self.内容配置['图片处理']['支持格式'],
            '最大图片大小': self.内容配置['图片处理']['最大图片大小'],
            '默认排版样式': self.排版配置['默认样式']
        }


def 测试文章处理器():
    """测试文章处理器功能"""
    print("🧪 开始测试文章处理器...")
    
    处理器 = 文章处理器()
    
    # 测试文章内容
    测试内容 = """
# 测试文章标题

这是一篇测试文章的内容。

## 第一部分

这里是第一部分的内容，包含一些**重要信息**。

- 列表项目1
- 列表项目2
- 列表项目3

## 第二部分

> 这是一个引用块，用来展示重要观点。

这里是第二部分的内容，包含更多详细信息。

![测试图片](https://example.com/test.jpg)

## 总结

这是文章的总结部分。
    """
    
    # 处理文章
    try:
        结果 = 处理器.处理文章内容(
            原始内容=测试内容,
            标题="测试文章标题",
            作者="测试作者",
            来源="测试来源",
            排版样式="business"
        )
        
        print(f"✅ 文章处理成功")
        print(f"📊 字数统计: {结果['字数统计']}")
        print(f"📝 摘要: {结果['摘要']}")
        print(f"🖼️  图片数量: {len(结果['图片列表'])}")
        
        # 验证文章
        验证通过, 错误列表 = 处理器.验证文章内容(结果)
        if 验证通过:
            print("✅ 文章验证通过")
        else:
            print("❌ 文章验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
        
        # 获取统计信息
        统计信息 = 处理器.获取处理统计()
        print(f"📊 处理统计: {统计信息}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    
    print("✅ 文章处理器测试完成")
    return True


if __name__ == "__main__":
    测试文章处理器()
