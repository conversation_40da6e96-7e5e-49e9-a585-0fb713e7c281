# -*- coding: utf-8 -*-
"""
日志和错误处理系统

作者: AI助手
日期: 2025-07-28
功能: 实现完善的日志记录和错误处理机制
"""

import os
import sys
import json
import logging
import traceback
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
from typing import Dict, List, Optional, Any
from functools import wraps
import time
try:
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
except ImportError:
    smtplib = None

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))
from 微信发布配置 import 获取日志配置, 获取通知配置

class 日志管理器:
    """日志管理器类"""
    
    def __init__(self, 模块名称: str = "微信发布"):
        self.模块名称 = 模块名称
        self.日志配置 = 获取日志配置()
        self.通知配置 = 获取通知配置()
        
        # 创建日志目录
        self.日志目录 = os.path.dirname(os.path.join(os.path.dirname(__file__), '..', '..', '日志文件'))
        os.makedirs(self.日志目录, exist_ok=True)
        
        # 初始化日志器
        self.logger = self._创建日志器()
        
        # 错误统计
        self.错误统计 = {
            'total_errors': 0,
            'api_errors': 0,
            'network_errors': 0,
            'validation_errors': 0,
            'system_errors': 0,
            'last_error_time': None
        }
    
    def _创建日志器(self) -> logging.Logger:
        """创建日志器"""
        logger = logging.getLogger(self.模块名称)
        logger.setLevel(getattr(logging, self.日志配置['日志级别']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(self.日志配置['日志格式'])
        
        # 文件处理器（轮转日志）
        if self.日志配置['记录内容']['发布操作']:
            文件处理器 = RotatingFileHandler(
                filename=os.path.join(self.日志目录, f'{self.模块名称}.log'),
                maxBytes=self._解析文件大小(self.日志配置['最大文件大小']),
                backupCount=self.日志配置['备份文件数'],
                encoding='utf-8'
            )
            文件处理器.setFormatter(formatter)
            logger.addHandler(文件处理器)
        
        # 控制台处理器
        控制台处理器 = logging.StreamHandler()
        控制台处理器.setFormatter(formatter)
        logger.addHandler(控制台处理器)
        
        # 错误文件处理器
        错误处理器 = RotatingFileHandler(
            filename=os.path.join(self.日志目录, f'{self.模块名称}_errors.log'),
            maxBytes=self._解析文件大小(self.日志配置['最大文件大小']),
            backupCount=self.日志配置['备份文件数'],
            encoding='utf-8'
        )
        错误处理器.setLevel(logging.ERROR)
        错误处理器.setFormatter(formatter)
        logger.addHandler(错误处理器)
        
        return logger
    
    def _解析文件大小(self, 大小字符串: str) -> int:
        """解析文件大小字符串"""
        大小字符串 = 大小字符串.upper()
        if 'MB' in 大小字符串:
            return int(大小字符串.replace('MB', '')) * 1024 * 1024
        elif 'KB' in 大小字符串:
            return int(大小字符串.replace('KB', '')) * 1024
        else:
            return int(大小字符串)
    
    def 记录信息(self, 消息: str, 额外数据: Dict = None):
        """记录信息日志"""
        self.logger.info(消息)
        if 额外数据:
            self.logger.debug(f"额外数据: {json.dumps(额外数据, ensure_ascii=False, indent=2)}")
    
    def 记录警告(self, 消息: str, 额外数据: Dict = None):
        """记录警告日志"""
        self.logger.warning(消息)
        if 额外数据:
            self.logger.debug(f"额外数据: {json.dumps(额外数据, ensure_ascii=False, indent=2)}")
    
    def 记录错误(self, 消息: str, 异常: Exception = None, 错误类型: str = "system", 额外数据: Dict = None):
        """记录错误日志"""
        # 更新错误统计
        self.错误统计['total_errors'] += 1
        self.错误统计[f'{错误类型}_errors'] = self.错误统计.get(f'{错误类型}_errors', 0) + 1
        self.错误统计['last_error_time'] = datetime.now().isoformat()
        
        # 记录错误
        错误信息 = f"[{错误类型.upper()}] {消息}"
        if 异常:
            错误信息 += f"\n异常详情: {str(异常)}"
            错误信息 += f"\n堆栈跟踪:\n{traceback.format_exc()}"
        
        self.logger.error(错误信息)
        
        if 额外数据:
            self.logger.error(f"额外数据: {json.dumps(额外数据, ensure_ascii=False, indent=2)}")
        
        # 发送错误通知
        if self.通知配置['发布失败通知']:
            self._发送错误通知(消息, 异常, 错误类型, 额外数据)
    
    def 记录API调用(self, 接口名称: str, 请求数据: Dict = None, 响应数据: Dict = None, 耗时: float = None):
        """记录API调用"""
        if not self.日志配置['记录内容']['API调用']:
            return
        
        调用信息 = f"API调用: {接口名称}"
        if 耗时:
            调用信息 += f" (耗时: {耗时:.2f}s)"
        
        self.logger.info(调用信息)
        
        if 请求数据:
            self.logger.debug(f"请求数据: {json.dumps(请求数据, ensure_ascii=False, indent=2)}")
        
        if 响应数据:
            self.logger.debug(f"响应数据: {json.dumps(响应数据, ensure_ascii=False, indent=2)}")
    
    def 记录性能指标(self, 操作名称: str, 耗时: float, 额外指标: Dict = None):
        """记录性能指标"""
        if not self.日志配置['记录内容']['性能指标']:
            return
        
        性能信息 = f"性能指标: {操作名称} 耗时 {耗时:.2f}s"
        self.logger.info(性能信息)
        
        if 额外指标:
            self.logger.debug(f"额外指标: {json.dumps(额外指标, ensure_ascii=False, indent=2)}")
    
    def _发送错误通知(self, 消息: str, 异常: Exception = None, 错误类型: str = "system", 额外数据: Dict = None):
        """发送错误通知"""
        try:
            # 邮件通知
            if self.通知配置['邮件通知']['启用']:
                self._发送邮件通知(消息, 异常, 错误类型, 额外数据)
            
            # Webhook通知
            if self.通知配置['webhook通知']['启用']:
                self._发送webhook通知(消息, 异常, 错误类型, 额外数据)
                
        except Exception as e:
            self.logger.error(f"发送错误通知失败: {str(e)}")
    
    def _发送邮件通知(self, 消息: str, 异常: Exception = None, 错误类型: str = "system", 额外数据: Dict = None):
        """发送邮件通知"""
        try:
            邮件配置 = self.通知配置['邮件通知']
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = 邮件配置['发送邮箱']
            msg['To'] = ', '.join(邮件配置['接收邮箱'])
            msg['Subject'] = f"微信发布系统错误通知 - {错误类型.upper()}"
            
            # 邮件内容
            邮件内容 = f"""
微信公众号发布系统错误通知

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
错误类型: {错误类型.upper()}
错误消息: {消息}

"""
            
            if 异常:
                邮件内容 += f"""
异常详情: {str(异常)}

堆栈跟踪:
{traceback.format_exc()}
"""
            
            if 额外数据:
                邮件内容 += f"""
额外数据:
{json.dumps(额外数据, ensure_ascii=False, indent=2)}
"""
            
            邮件内容 += f"""
错误统计:
{json.dumps(self.错误统计, ensure_ascii=False, indent=2)}
"""
            
            msg.attach(MIMEText(邮件内容, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(邮件配置['smtp_server'], 邮件配置['smtp_port']) as server:
                server.starttls()
                server.login(邮件配置['发送邮箱'], 邮件配置['邮箱密码'])
                server.send_message(msg)
            
            self.logger.info("✅ 错误通知邮件发送成功")
            
        except Exception as e:
            self.logger.error(f"❌ 邮件通知发送失败: {str(e)}")
    
    def _发送webhook通知(self, 消息: str, 异常: Exception = None, 错误类型: str = "system", 额外数据: Dict = None):
        """发送Webhook通知"""
        try:
            import requests
            
            webhook配置 = self.通知配置['webhook通知']
            
            # 构建通知数据
            通知数据 = {
                'timestamp': datetime.now().isoformat(),
                'error_type': 错误类型,
                'message': 消息,
                'module': self.模块名称,
                'error_stats': self.错误统计
            }
            
            if 异常:
                通知数据['exception'] = str(异常)
                通知数据['traceback'] = traceback.format_exc()
            
            if 额外数据:
                通知数据['extra_data'] = 额外数据
            
            # 发送请求
            response = requests.post(
                webhook配置['通知地址'],
                json=通知数据,
                headers=webhook配置['请求头'],
                timeout=30
            )
            response.raise_for_status()
            
            self.logger.info("✅ Webhook通知发送成功")
            
        except Exception as e:
            self.logger.error(f"❌ Webhook通知发送失败: {str(e)}")
    
    def 获取错误统计(self) -> Dict:
        """获取错误统计信息"""
        return self.错误统计.copy()
    
    def 重置错误统计(self):
        """重置错误统计"""
        self.错误统计 = {
            'total_errors': 0,
            'api_errors': 0,
            'network_errors': 0,
            'validation_errors': 0,
            'system_errors': 0,
            'last_error_time': None
        }
        self.logger.info("🔄 错误统计已重置")
    
    def 清理旧日志(self, 保留天数: int = 30):
        """清理旧日志文件"""
        try:
            截止时间 = datetime.now() - timedelta(days=保留天数)
            清理数量 = 0
            
            for 文件名 in os.listdir(self.日志目录):
                文件路径 = os.path.join(self.日志目录, 文件名)
                if os.path.isfile(文件路径):
                    文件时间 = datetime.fromtimestamp(os.path.getmtime(文件路径))
                    if 文件时间 < 截止时间:
                        os.remove(文件路径)
                        清理数量 += 1
            
            self.logger.info(f"🧹 清理了 {清理数量} 个旧日志文件")
            
        except Exception as e:
            self.logger.error(f"❌ 清理旧日志失败: {str(e)}")


def 错误处理装饰器(错误类型: str = "system", 重试次数: int = 0, 重试延迟: float = 1.0):
    """错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            日志器 = 日志管理器(func.__module__)
            
            for 尝试次数 in range(重试次数 + 1):
                try:
                    开始时间 = datetime.now()
                    结果 = func(*args, **kwargs)
                    耗时 = (datetime.now() - 开始时间).total_seconds()
                    
                    if 尝试次数 > 0:
                        日志器.记录信息(f"✅ {func.__name__} 重试成功 (第{尝试次数}次)")
                    
                    日志器.记录性能指标(func.__name__, 耗时)
                    return 结果
                    
                except Exception as e:
                    if 尝试次数 < 重试次数:
                        日志器.记录警告(f"⚠️  {func.__name__} 执行失败，准备重试 (第{尝试次数 + 1}次): {str(e)}")
                        time.sleep(重试延迟 * (尝试次数 + 1))  # 递增延迟
                    else:
                        日志器.记录错误(f"❌ {func.__name__} 执行失败", e, 错误类型)
                        raise
            
        return wrapper
    return decorator


def 性能监控装饰器(操作名称: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            日志器 = 日志管理器(func.__module__)
            名称 = 操作名称 or func.__name__
            
            开始时间 = datetime.now()
            try:
                结果 = func(*args, **kwargs)
                耗时 = (datetime.now() - 开始时间).total_seconds()
                日志器.记录性能指标(名称, 耗时)
                return 结果
            except Exception as e:
                耗时 = (datetime.now() - 开始时间).total_seconds()
                日志器.记录错误(f"{名称} 执行失败 (耗时: {耗时:.2f}s)", e)
                raise
                
        return wrapper
    return decorator


# 全局日志管理器实例
全局日志管理器 = 日志管理器("微信发布系统")


def 测试日志系统():
    """测试日志系统"""
    print("🧪 开始测试日志系统...")
    
    日志器 = 日志管理器("测试模块")
    
    # 测试各种日志级别
    日志器.记录信息("这是一条信息日志", {"test_data": "info"})
    日志器.记录警告("这是一条警告日志", {"test_data": "warning"})
    
    # 测试错误日志
    try:
        raise ValueError("这是一个测试异常")
    except Exception as e:
        日志器.记录错误("测试错误处理", e, "validation", {"test_data": "error"})
    
    # 测试API调用日志
    日志器.记录API调用("test_api", {"param": "value"}, {"result": "success"}, 1.23)
    
    # 测试性能指标
    日志器.记录性能指标("test_operation", 2.45, {"memory_usage": "100MB"})
    
    # 获取错误统计
    统计信息 = 日志器.获取错误统计()
    print(f"📊 错误统计: {统计信息}")
    
    print("✅ 日志系统测试完成")


if __name__ == "__main__":
    测试日志系统()
