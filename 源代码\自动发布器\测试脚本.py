# -*- coding: utf-8 -*-
"""
微信公众号自动发布器测试脚本

作者: AI助手
日期: 2025-07-28
功能: 提供完整的测试方法和步骤
"""

import os
import sys
import json
import time
import unittest
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))

# 导入测试模块
from 微信自动发布器 import 微信自动发布器, 创建示例文章
from 微信API import 微信公众号API
from 文章处理器 import 文章处理器
from 排版模板 import 排版模板管理器
from 日志系统 import 日志管理器

class 微信发布器测试(unittest.TestCase):
    """微信发布器单元测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("🧪 开始微信公众号自动发布器测试")
        print("=" * 60)
        cls.发布器 = 微信自动发布器()
        cls.测试结果 = []
    
    def setUp(self):
        """每个测试方法前的初始化"""
        self.开始时间 = datetime.now()
    
    def tearDown(self):
        """每个测试方法后的清理"""
        耗时 = (datetime.now() - self.开始时间).total_seconds()
        测试名称 = self._testMethodName
        print(f"⏱️  {测试名称} 耗时: {耗时:.2f}秒")
    
    def test_01_系统配置验证(self):
        """测试系统配置验证"""
        print("\n🔍 测试1: 系统配置验证")
        
        配置有效, 错误列表 = self.发布器.验证系统配置()
        
        if 配置有效:
            print("✅ 系统配置验证通过")
            self.assertTrue(配置有效)
        else:
            print("❌ 系统配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            # 配置问题不应该导致测试失败，只是警告
            print("⚠️  请检查配置后重新测试")
    
    def test_02_微信API测试(self):
        """测试微信API功能"""
        print("\n🔗 测试2: 微信API连接")
        
        try:
            api = 微信公众号API()
            
            # 测试配置验证
            配置有效, 错误列表 = api.验证配置()
            if not 配置有效:
                print("⚠️  微信API配置问题:")
                for error in 错误列表:
                    print(f"   - {error}")
                self.skipTest("微信API配置不完整，跳过API测试")
            
            # 测试获取访问令牌
            try:
                token = api.获取访问令牌()
                print(f"✅ 成功获取访问令牌: {token[:10]}...")
                self.assertIsNotNone(token)
            except Exception as e:
                print(f"❌ 获取访问令牌失败: {str(e)}")
                self.skipTest("无法获取访问令牌，跳过后续API测试")
            
            # 测试获取草稿列表
            try:
                草稿列表 = api.获取草稿列表(数量=5)
                print(f"✅ 成功获取草稿列表，共 {len(草稿列表)} 篇")
                self.assertIsInstance(草稿列表, list)
            except Exception as e:
                print(f"⚠️  获取草稿列表失败: {str(e)}")
            
            # 测试API状态
            状态信息 = api.获取API状态()
            print(f"📊 API状态: {状态信息}")
            self.assertIsInstance(状态信息, dict)
            
        except Exception as e:
            print(f"❌ 微信API测试异常: {str(e)}")
            self.fail(f"微信API测试失败: {str(e)}")
    
    def test_03_文章处理器测试(self):
        """测试文章处理器功能"""
        print("\n📝 测试3: 文章处理器")
        
        try:
            处理器 = 文章处理器()
            
            # 测试文章内容
            测试内容 = """
# 测试文章标题

这是一篇测试文章的内容。

## 第一部分

这里是第一部分的内容，包含一些**重要信息**。

- 列表项目1
- 列表项目2
- 列表项目3

## 第二部分

> 这是一个引用块，用来展示重要观点。

这里是第二部分的内容，包含更多详细信息。

## 总结

这是文章的总结部分。
            """
            
            # 处理文章
            结果 = 处理器.处理文章内容(
                原始内容=测试内容,
                标题="测试文章标题",
                作者="测试作者",
                来源="测试来源",
                排版样式="business"
            )
            
            print(f"✅ 文章处理成功")
            print(f"📊 字数统计: {结果['字数统计']}")
            print(f"📝 摘要: {结果['摘要'][:50]}...")
            print(f"🖼️  图片数量: {len(结果['图片列表'])}")
            
            # 验证结果结构
            self.assertIn('标题', 结果)
            self.assertIn('格式化内容', 结果)
            self.assertIn('摘要', 结果)
            self.assertIsInstance(结果['字数统计'], int)
            
            # 验证文章内容
            验证通过, 错误列表 = 处理器.验证文章内容(结果)
            if 验证通过:
                print("✅ 文章验证通过")
                self.assertTrue(验证通过)
            else:
                print("❌ 文章验证失败:")
                for error in 错误列表:
                    print(f"   - {error}")
                self.fail("文章验证失败")
            
            # 获取处理统计
            统计信息 = 处理器.获取处理统计()
            print(f"📊 处理统计: {统计信息}")
            self.assertIsInstance(统计信息, dict)
            
        except Exception as e:
            print(f"❌ 文章处理器测试失败: {str(e)}")
            self.fail(f"文章处理器测试异常: {str(e)}")
    
    def test_04_排版模板测试(self):
        """测试排版模板功能"""
        print("\n🎨 测试4: 排版模板系统")
        
        try:
            模板管理器 = 排版模板管理器()
            
            # 测试获取可用样式
            样式列表 = 模板管理器.获取可用样式列表()
            print(f"✅ 可用排版样式: {样式列表}")
            self.assertIsInstance(样式列表, list)
            self.assertGreater(len(样式列表), 0)
            
            # 测试每种样式
            for 样式名称 in 样式列表:
                样式信息 = 模板管理器.获取样式信息(样式名称)
                print(f"   - {样式名称}: {样式信息['名称']} - {样式信息['描述']}")
                self.assertIsInstance(样式信息, dict)
                self.assertIn('名称', 样式信息)
                self.assertIn('描述', 样式信息)
            
            # 测试应用排版样式
            测试内容 = "# 测试标题\n\n这是测试内容。\n\n## 子标题\n\n更多内容。"
            
            for 样式名称 in 样式列表[:2]:  # 测试前两种样式
                try:
                    格式化结果 = 模板管理器.应用排版样式(
                        内容=测试内容,
                        标题="测试标题",
                        样式名称=样式名称,
                        发布日期="2025-07-28",
                        作者="测试作者"
                    )
                    print(f"✅ {样式名称} 样式应用成功")
                    self.assertIsInstance(格式化结果, str)
                    self.assertIn("测试标题", 格式化结果)
                except Exception as e:
                    print(f"❌ {样式名称} 样式应用失败: {str(e)}")
                    self.fail(f"排版样式应用失败: {str(e)}")
            
        except Exception as e:
            print(f"❌ 排版模板测试失败: {str(e)}")
            self.fail(f"排版模板测试异常: {str(e)}")
    
    def test_05_日志系统测试(self):
        """测试日志系统功能"""
        print("\n📋 测试5: 日志系统")
        
        try:
            日志器 = 日志管理器("测试模块")
            
            # 测试各种日志级别
            日志器.记录信息("这是一条测试信息日志", {"test_data": "info"})
            日志器.记录警告("这是一条测试警告日志", {"test_data": "warning"})
            
            # 测试错误日志
            try:
                raise ValueError("这是一个测试异常")
            except Exception as e:
                日志器.记录错误("测试错误处理", e, "validation", {"test_data": "error"})
            
            # 测试API调用日志
            日志器.记录API调用("test_api", {"param": "value"}, {"result": "success"}, 1.23)
            
            # 测试性能指标
            日志器.记录性能指标("test_operation", 2.45, {"memory_usage": "100MB"})
            
            # 获取错误统计
            统计信息 = 日志器.获取错误统计()
            print(f"✅ 日志系统测试完成")
            print(f"📊 错误统计: {统计信息}")
            
            self.assertIsInstance(统计信息, dict)
            self.assertIn('total_errors', 统计信息)
            
        except Exception as e:
            print(f"❌ 日志系统测试失败: {str(e)}")
            self.fail(f"日志系统测试异常: {str(e)}")
    
    def test_06_完整发布流程测试(self):
        """测试完整发布流程"""
        print("\n🚀 测试6: 完整发布流程（仅草稿）")
        
        try:
            # 创建测试文章
            测试文章 = 创建示例文章()
            print(f"📝 创建测试文章: {测试文章['标题']}")
            
            # 发布文章（仅创建草稿）
            发布选项 = {
                '仅草稿': True,
                '排版样式': 'business',
                '跳过确认': True
            }
            
            结果 = self.发布器.发布文章(测试文章, 发布选项)
            
            if 结果['success']:
                print("✅ 测试发布成功")
                print(f"📄 草稿ID: {结果['media_id']}")
                print(f"⏱️  处理耗时: {结果['process_time']:.2f}秒")
                
                self.assertTrue(结果['success'])
                self.assertIsNotNone(结果['media_id'])
                self.assertTrue(结果['draft_only'])
                
                # 验证草稿是否创建成功
                if 结果['media_id'] and 结果['media_id'] != 'test_mode':
                    草稿列表 = self.发布器.获取草稿列表(数量=5)
                    草稿ID列表 = [草稿.get('media_id') for 草稿 in 草稿列表]
                    if 结果['media_id'] in 草稿ID列表:
                        print("✅ 草稿创建验证成功")
                    else:
                        print("⚠️  草稿创建验证失败（可能是测试模式）")
                
            else:
                print(f"❌ 测试发布失败: {结果['error_message']}")
                self.fail(f"发布流程测试失败: {结果['error_message']}")
            
        except Exception as e:
            print(f"❌ 完整发布流程测试失败: {str(e)}")
            # 不让这个测试失败影响整体测试
            print("⚠️  这可能是由于API配置问题导致的，请检查配置")
    
    def test_07_统计信息测试(self):
        """测试统计信息功能"""
        print("\n📊 测试7: 统计信息")
        
        try:
            # 获取发布统计
            统计信息 = self.发布器.获取发布统计()
            print("✅ 获取发布统计成功")
            
            # 验证统计信息结构
            必要字段 = ['total_processed', 'successful_uploads', 'api_status', 'error_stats']
            for 字段 in 必要字段:
                self.assertIn(字段, 统计信息, f"缺少统计字段: {字段}")
            
            print(f"📈 处理总数: {统计信息['total_processed']}")
            print(f"📤 成功上传: {统计信息['successful_uploads']}")
            print(f"📋 API状态: {统计信息['api_status']}")
            
            # 重置统计信息
            self.发布器.重置统计信息()
            print("✅ 统计信息重置成功")
            
        except Exception as e:
            print(f"❌ 统计信息测试失败: {str(e)}")
            self.fail(f"统计信息测试异常: {str(e)}")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        print("\n" + "=" * 60)
        print("🎉 微信公众号自动发布器测试完成")
        
        # 清理临时文件
        try:
            cls.发布器.文章处理器.清理临时文件()
            print("🧹 临时文件清理完成")
        except:
            pass


def 运行性能测试():
    """运行性能测试"""
    print("\n⚡ 性能测试")
    print("-" * 40)
    
    发布器 = 微信自动发布器()
    
    # 测试文章处理性能
    测试文章 = 创建示例文章()
    
    开始时间 = time.time()
    for i in range(5):
        try:
            结果 = 发布器.文章处理器.处理文章内容(
                原始内容=测试文章['内容'],
                标题=f"性能测试文章 {i+1}",
                排版样式="business"
            )
            print(f"✅ 第{i+1}次处理完成，字数: {结果['字数统计']}")
        except Exception as e:
            print(f"❌ 第{i+1}次处理失败: {str(e)}")
    
    总耗时 = time.time() - 开始时间
    平均耗时 = 总耗时 / 5
    
    print(f"📊 性能测试结果:")
    print(f"   总耗时: {总耗时:.2f}秒")
    print(f"   平均耗时: {平均耗时:.2f}秒/篇")
    print(f"   处理速度: {1/平均耗时:.2f}篇/秒")


def 运行压力测试():
    """运行压力测试"""
    print("\n💪 压力测试")
    print("-" * 40)
    
    发布器 = 微信自动发布器()
    
    # 创建多篇测试文章
    文章列表 = []
    for i in range(3):
        文章 = 创建示例文章()
        文章['标题'] = f"压力测试文章 {i+1}"
        文章列表.append(文章)
    
    # 批量处理测试
    发布选项 = {
        '仅草稿': True,
        '发布间隔': 5,  # 5秒间隔
        '跳过确认': True
    }
    
    开始时间 = time.time()
    try:
        结果列表 = 发布器.批量发布文章(文章列表, 发布选项)
        总耗时 = time.time() - 开始时间
        
        成功数量 = sum(1 for r in 结果列表 if r['success'])
        失败数量 = len(结果列表) - 成功数量
        
        print(f"📊 压力测试结果:")
        print(f"   处理文章数: {len(文章列表)}")
        print(f"   成功数量: {成功数量}")
        print(f"   失败数量: {失败数量}")
        print(f"   总耗时: {总耗时:.2f}秒")
        print(f"   平均耗时: {总耗时/len(文章列表):.2f}秒/篇")
        
    except Exception as e:
        print(f"❌ 压力测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("🧪 微信公众号自动发布器测试套件")
    print("=" * 60)
    
    # 运行单元测试
    print("1️⃣  运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=0)
    
    # 运行性能测试
    print("\n2️⃣  运行性能测试...")
    try:
        运行性能测试()
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
    
    # 运行压力测试
    print("\n3️⃣  运行压力测试...")
    try:
        运行压力测试()
    except Exception as e:
        print(f"❌ 压力测试失败: {str(e)}")
    
    print("\n🎉 所有测试完成！")
    print("请查看上述输出，确认各项功能正常工作。")


if __name__ == "__main__":
    main()
