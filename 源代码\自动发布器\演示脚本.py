# -*- coding: utf-8 -*-
"""
微信公众号自动发布器演示脚本

作者: AI助手
日期: 2025-07-28
功能: 演示系统主要功能，用于快速体验
"""

import os
import sys
import json
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '配置文件'))

# 导入主要模块
from 微信自动发布器 import 微信自动发布器, 创建示例文章
from 微信发布配置 import 显示配置摘要
from 排版模板 import 排版模板管理器

def 演示_配置展示():
    """演示配置展示功能"""
    print("🔧 配置展示演示")
    print("=" * 50)
    
    try:
        显示配置摘要()
        print("✅ 配置展示完成")
    except Exception as e:
        print(f"❌ 配置展示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def 演示_排版样式():
    """演示排版样式功能"""
    print("🎨 排版样式演示")
    print("=" * 50)
    
    try:
        模板管理器 = 排版模板管理器()
        
        # 显示可用样式
        样式列表 = 模板管理器.获取可用样式列表()
        print(f"📋 可用排版样式 ({len(样式列表)} 种):")
        
        for 样式名称 in 样式列表:
            样式信息 = 模板管理器.获取样式信息(样式名称)
            print(f"   🎯 {样式名称}: {样式信息['名称']} - {样式信息['描述']}")
            print(f"      主色调: {样式信息['主色调']}, 辅助色: {样式信息['辅助色']}")
        
        # 演示样式应用
        print(f"\n📝 样式应用演示:")
        测试内容 = """
# 演示标题

这是一段演示内容，用来展示不同排版样式的效果。

## 子标题

- 列表项目1
- 列表项目2
- 列表项目3

> 这是一个引用块，展示重要信息。

这是正文内容，包含**重要文字**和普通文字。
        """
        
        # 应用商务风格
        商务样式结果 = 模板管理器.应用排版样式(
            内容=测试内容,
            标题="商务风格演示",
            样式名称="business",
            发布日期="2025-07-28",
            作者="演示作者"
        )
        
        print("✅ 商务风格应用成功")
        print(f"   生成HTML长度: {len(商务样式结果)} 字符")
        
        # 应用科技风格
        科技样式结果 = 模板管理器.应用排版样式(
            内容=测试内容,
            标题="科技风格演示",
            样式名称="tech",
            发布日期="2025-07-28",
            作者="演示作者"
        )
        
        print("✅ 科技风格应用成功")
        print(f"   生成HTML长度: {len(科技样式结果)} 字符")
        
        print("✅ 排版样式演示完成")
        
    except Exception as e:
        print(f"❌ 排版样式演示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def 演示_文章处理():
    """演示文章处理功能"""
    print("📝 文章处理演示")
    print("=" * 50)
    
    try:
        from 文章处理器 import 文章处理器
        
        处理器 = 文章处理器()
        
        # 创建测试文章
        测试文章 = 创建示例文章()
        print(f"📄 测试文章: {测试文章['标题']}")
        print(f"📊 原始内容长度: {len(测试文章['内容'])} 字符")
        
        # 处理文章
        处理结果 = 处理器.处理文章内容(
            原始内容=测试文章['内容'],
            标题=测试文章['标题'],
            **测试文章['元数据']
        )
        
        print(f"✅ 文章处理完成:")
        print(f"   📊 处理后字数: {处理结果['字数统计']}")
        print(f"   📝 生成摘要: {处理结果['摘要'][:50]}...")
        print(f"   🖼️  图片数量: {len(处理结果['图片列表'])}")
        print(f"   🎨 排版样式: {处理结果['排版样式']}")
        print(f"   ⏱️  处理时间: {处理结果['处理时间']}")
        
        # 验证文章
        验证通过, 错误列表 = 处理器.验证文章内容(处理结果)
        if 验证通过:
            print("✅ 文章验证通过")
        else:
            print("❌ 文章验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
        
        # 获取处理统计
        统计信息 = 处理器.获取处理统计()
        print(f"📊 处理器统计: {统计信息}")
        
        print("✅ 文章处理演示完成")
        
    except Exception as e:
        print(f"❌ 文章处理演示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def 演示_API功能():
    """演示API功能"""
    print("🔗 API功能演示")
    print("=" * 50)
    
    try:
        from 微信API import 微信公众号API
        
        api = 微信公众号API()
        
        # 验证配置
        配置有效, 错误列表 = api.验证配置()
        print(f"🔍 API配置验证: {'✅ 通过' if 配置有效 else '❌ 失败'}")
        
        if not 配置有效:
            print("⚠️  配置问题:")
            for error in 错误列表:
                print(f"   - {error}")
            print("💡 请配置微信API信息后重试")
        else:
            # 获取API状态
            状态信息 = api.获取API状态()
            print(f"📊 API状态:")
            for key, value in 状态信息.items():
                print(f"   {key}: {value}")
            
            # 尝试获取草稿列表
            try:
                草稿列表 = api.获取草稿列表(数量=3)
                print(f"📄 草稿列表: 共 {len(草稿列表)} 篇")
                for i, 草稿 in enumerate(草稿列表, 1):
                    标题 = 草稿.get('title', '无标题')
                    print(f"   {i}. {标题[:30]}...")
            except Exception as e:
                print(f"⚠️  获取草稿列表失败: {str(e)}")
        
        print("✅ API功能演示完成")
        
    except Exception as e:
        print(f"❌ API功能演示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def 演示_完整流程():
    """演示完整发布流程"""
    print("🚀 完整流程演示")
    print("=" * 50)
    
    try:
        发布器 = 微信自动发布器()
        
        # 验证系统配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        print(f"🔍 系统配置验证: {'✅ 通过' if 配置有效 else '❌ 失败'}")
        
        if not 配置有效:
            print("⚠️  配置问题:")
            for error in 错误列表:
                print(f"   - {error}")
            print("💡 请完善配置后重试完整流程演示")
            return
        
        # 创建演示文章
        演示文章 = {
            '标题': f'自动发布器演示文章 - {datetime.now().strftime("%H:%M:%S")}',
            '内容': '''
# 微信公众号自动发布器演示

欢迎使用微信公众号自动发布器！这是一篇演示文章。

## 系统特性

本系统具有以下特性：

- **自动化发布**: 支持文章的自动化处理和发布
- **多种排版**: 提供商务、科技、生活、学术等多种排版风格
- **安全可靠**: 包含完善的错误处理和日志记录机制
- **灵活配置**: 支持测试模式和生产模式切换

## 演示内容

> 这是一个引用块，用来展示重要信息。

这篇文章展示了系统的基本功能，包括：

1. 文章内容处理
2. 排版样式应用
3. 图片处理（如果有）
4. 草稿创建

## 总结

感谢您体验微信公众号自动发布器！

**注意**: 这是演示模式，文章将保存为草稿，不会实际发布。
            ''',
            '元数据': {
                '作者': '自动发布器',
                '来源': '演示系统',
                '标签': ['演示', '测试', '自动化'],
                '分类': '系统演示'
            }
        }
        
        print(f"📝 演示文章: {演示文章['标题']}")
        
        # 发布选项（仅创建草稿）
        发布选项 = {
            '仅草稿': True,
            '排版样式': 'business',
            '跳过确认': True,
            '显示封面': True,
            '开启评论': False
        }
        
        print("🔄 开始发布流程...")
        
        # 执行发布
        结果 = 发布器.发布文章(演示文章, 发布选项)
        
        if 结果['success']:
            print("🎉 演示发布成功!")
            print(f"📄 草稿ID: {结果['media_id']}")
            print(f"📝 仅草稿模式: {结果['draft_only']}")
            print(f"⏱️  处理耗时: {结果['process_time']:.2f}秒")
            
            if 结果['upload_time']:
                print(f"📤 上传耗时: {结果['upload_time']:.2f}秒")
        else:
            print(f"❌ 演示发布失败: {结果['error_message']}")
        
        # 获取发布统计
        统计信息 = 发布器.获取发布统计()
        print(f"\n📊 发布统计:")
        print(f"   处理总数: {统计信息['total_processed']}")
        print(f"   成功上传: {统计信息['successful_uploads']}")
        print(f"   成功发布: {统计信息['successful_publishes']}")
        
        print("✅ 完整流程演示完成")
        
    except Exception as e:
        print(f"❌ 完整流程演示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def 演示_日志系统():
    """演示日志系统功能"""
    print("📋 日志系统演示")
    print("=" * 50)
    
    try:
        from 日志系统 import 日志管理器
        
        日志器 = 日志管理器("演示模块")
        
        # 演示各种日志级别
        日志器.记录信息("这是一条演示信息日志", {"demo": "info"})
        日志器.记录警告("这是一条演示警告日志", {"demo": "warning"})
        
        # 演示错误日志
        try:
            raise ValueError("这是一个演示异常")
        except Exception as e:
            日志器.记录错误("演示错误处理", e, "demo", {"demo": "error"})
        
        # 演示API调用日志
        日志器.记录API调用("demo_api", {"param": "value"}, {"result": "success"}, 0.5)
        
        # 演示性能指标
        日志器.记录性能指标("demo_operation", 1.23, {"memory": "50MB"})
        
        # 获取错误统计
        统计信息 = 日志器.获取错误统计()
        print(f"📊 错误统计: {统计信息}")
        
        print("✅ 日志系统演示完成")
        
    except Exception as e:
        print(f"❌ 日志系统演示失败: {str(e)}")
    
    print("\n" + "=" * 50)

def main():
    """主演示函数"""
    print("🎭 微信公众号自动发布器功能演示")
    print("=" * 60)
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    演示列表 = [
        ("配置展示", 演示_配置展示),
        ("排版样式", 演示_排版样式),
        ("文章处理", 演示_文章处理),
        ("API功能", 演示_API功能),
        ("日志系统", 演示_日志系统),
        ("完整流程", 演示_完整流程)
    ]
    
    for i, (名称, 演示函数) in enumerate(演示列表, 1):
        print(f"\n{i}️⃣  {名称}演示")
        try:
            演示函数()
        except Exception as e:
            print(f"❌ {名称}演示异常: {str(e)}")
        
        if i < len(演示列表):
            input("按回车键继续下一个演示...")
    
    print("\n🎉 所有演示完成！")
    print("=" * 60)
    print("💡 提示:")
    print("   - 如需实际使用，请先配置微信API信息")
    print("   - 建议先在测试模式下验证功能")
    print("   - 查看README.md获取详细使用说明")
    print("   - 运行测试脚本.py进行完整测试")
    print("=" * 60)

if __name__ == "__main__":
    main()
