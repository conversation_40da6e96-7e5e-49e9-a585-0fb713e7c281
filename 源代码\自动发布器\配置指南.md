# 微信公众号自动发布器配置指南

## 📋 配置概览

本系统的所有配置都集中在 `配置文件` 文件夹中，遵循项目的统一配置管理规则。主要配置文件包括：

- `配置文件/微信发布配置.py` - 微信发布专用配置
- `配置文件/公众号配置.py` - 公众号基础配置（已扩展）
- `配置文件/系统配置.yaml` - 系统全局配置

## 🔧 详细配置说明

### 1. 微信API配置

#### 1.1 获取微信公众号API凭据

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入"开发" → "基本配置"
3. 获取 `AppID` 和 `AppSecret`

#### 1.2 配置方式

**方式一：环境变量（推荐）**

```bash
# Windows
set WECHAT_APP_ID=your_app_id_here
set WECHAT_APP_SECRET=your_app_secret_here

# Linux/Mac
export WECHAT_APP_ID="your_app_id_here"
export WECHAT_APP_SECRET="your_app_secret_here"
```

**方式二：直接修改配置文件**

编辑 `配置文件/微信发布配置.py`：

```python
微信API配置 = {
    'app_id': 'your_app_id_here',
    'app_secret': 'your_app_secret_here',
    # 其他配置保持不变
}
```

### 2. 发布控制配置

#### 2.1 基本发布设置

```python
发布控制配置 = {
    '启用自动发布': False,  # 🔴 重要：默认关闭自动发布
    '测试模式': True,       # 🔴 重要：默认开启测试模式
    '发布前确认': True,     # 建议开启人工确认
    '草稿箱模式': True,     # 优先使用草稿箱
}
```

#### 2.2 发布策略配置

```python
'发布策略': {
    '立即发布': False,           # 是否立即发布
    '定时发布': True,            # 是否支持定时发布
    '默认发布时间': ['09:00', '18:00'],  # 默认发布时间点
    '时区': 'Asia/Shanghai',     # 时区设置
    '工作日发布': True,          # 是否在工作日发布
    '周末发布': False            # 是否在周末发布
}
```

#### 2.3 频率限制配置

```python
'频率限制': {
    '每日最大发布数': 3,      # 每天最多发布3篇
    '最小间隔小时': 4,        # 最小间隔4小时
    '每周最大发布数': 15      # 每周最多发布15篇
}
```

### 3. 内容处理配置

#### 3.1 文本处理设置

```python
'文本处理': {
    '最大标题长度': 64,        # 微信限制64字符
    '最大摘要长度': 120,       # 摘要最大长度
    '自动生成摘要': True,      # 是否自动生成摘要
    '移除多余空行': True,      # 清理格式
    '统一标点符号': True,      # 统一中文标点
    '自动换行': True,          # 自动换行处理
    '段落间距': 2              # 段落间空行数
}
```

#### 3.2 图片处理设置

```python
'图片处理': {
    '启用图片上传': True,      # 是否上传图片
    '图片压缩': True,          # 是否压缩图片
    '最大图片大小': 2048000,   # 2MB限制
    '图片质量': 85,            # 压缩质量(1-100)
    '支持格式': ['jpg', 'jpeg', 'png', 'gif'],
    '图片水印': False,         # 是否添加水印
    '自动调整尺寸': True,      # 自动调整尺寸
    '最大宽度': 900            # 最大宽度像素
}
```

### 4. 排版样式配置

#### 4.1 默认样式设置

```python
排版样式配置 = {
    '默认样式': 'business',  # 默认使用商务风格
}
```

#### 4.2 自定义样式

可以在样式库中添加新样式：

```python
'样式库': {
    'custom': {
        '名称': '自定义风格',
        '描述': '适合特定场景的自定义样式',
        '主色调': '#FF6B6B',
        '辅助色': '#4ECDC4',
        '字体配置': {
            '标题字体': 'Microsoft YaHei, sans-serif',
            '正文字体': 'Microsoft YaHei, sans-serif',
            '标题大小': '18px',
            '正文大小': '14px',
            '行高': '1.6'
        }
    }
}
```

### 5. 安全和审核配置

#### 5.1 内容审核设置

```python
'内容审核': {
    '启用敏感词检测': True,           # 开启敏感词检测
    '敏感词库文件': '配置文件/敏感词库.txt',  # 敏感词库路径
    '自动替换敏感词': False,          # 是否自动替换
    '审核失败处理': 'skip'            # skip, manual, auto_fix
}
```

#### 5.2 发布安全设置

```python
'发布安全': {
    'IP白名单': [],              # IP访问限制
    '访问令牌加密': True,        # token加密存储
    '日志记录': True,            # 记录操作日志
    '操作审计': True             # 操作审计
}
```

#### 5.3 错误处理配置

```python
'错误处理': {
    '最大重试次数': 3,
    '重试间隔秒数': [60, 120, 300],  # 递增重试间隔
    '超时设置': {
        '连接超时': 10,
        '读取超时': 30,
        '上传超时': 120
    }
}
```

### 6. 日志配置

#### 6.1 日志基本设置

```python
日志配置 = {
    '日志级别': 'INFO',                    # DEBUG, INFO, WARNING, ERROR
    '日志文件': '日志文件/微信发布.log',    # 日志文件路径
    '最大文件大小': '10MB',                # 单个日志文件最大大小
    '备份文件数': 5,                       # 保留的备份文件数量
    '日志格式': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}
```

#### 6.2 日志记录内容

```python
'记录内容': {
    '发布操作': True,     # 记录发布操作
    'API调用': True,      # 记录API调用
    '错误信息': True,     # 记录错误信息
    '性能指标': False     # 记录性能指标（可选）
}
```

### 7. 通知配置

#### 7.1 基本通知设置

```python
通知配置 = {
    '发布成功通知': True,                    # 发布成功时通知
    '发布失败通知': True,                    # 发布失败时通知
    '通知方式': ['log', 'console'],          # 通知方式
}
```

#### 7.2 邮件通知配置

```python
'邮件通知': {
    '启用': False,                    # 是否启用邮件通知
    'smtp_server': 'smtp.qq.com',    # SMTP服务器
    'smtp_port': 587,                # SMTP端口
    '发送邮箱': '<EMAIL>', # 发送邮箱
    '邮箱密码': 'your_password',     # 邮箱密码或授权码
    '接收邮箱': ['<EMAIL>'] # 接收邮箱列表
}
```

#### 7.3 Webhook通知配置

```python
'webhook通知': {
    '启用': False,                           # 是否启用Webhook
    '通知地址': 'https://your-webhook.com',  # Webhook地址
    '请求方法': 'POST',                      # 请求方法
    '请求头': {'Content-Type': 'application/json'}
}
```

## 🔄 配置验证和测试

### 1. 验证配置完整性

```python
from 微信发布配置 import 验证配置

errors = 验证配置()
if errors:
    print("配置错误:", errors)
else:
    print("配置验证通过")
```

### 2. 显示配置摘要

```python
from 微信发布配置 import 显示配置摘要

显示配置摘要()
```

### 3. 测试API连接

```bash
python 微信自动发布器.py --config
```

## 📝 配置模板

### 1. 开发环境配置

```python
# 开发环境 - 安全的测试配置
发布控制配置 = {
    '启用自动发布': False,
    '测试模式': True,
    '发布前确认': True,
    '草稿箱模式': True,
}

日志配置 = {
    '日志级别': 'DEBUG',
    '记录内容': {
        '发布操作': True,
        'API调用': True,
        '错误信息': True,
        '性能指标': True
    }
}
```

### 2. 生产环境配置

```python
# 生产环境 - 谨慎的发布配置
发布控制配置 = {
    '启用自动发布': True,      # 可以开启自动发布
    '测试模式': False,         # 关闭测试模式
    '发布前确认': True,        # 保持人工确认
    '草稿箱模式': True,        # 优先草稿箱
}

日志配置 = {
    '日志级别': 'INFO',
    '记录内容': {
        '发布操作': True,
        'API调用': True,
        '错误信息': True,
        '性能指标': False
    }
}
```

## ⚠️ 重要提醒

### 1. 安全注意事项

- **🔴 敏感信息**: 绝不要将API密钥提交到版本控制系统
- **🔴 测试模式**: 首次使用必须开启测试模式
- **🔴 发布确认**: 生产环境建议保持发布前确认
- **🔴 频率限制**: 严格遵守微信API调用频率限制

### 2. 配置优先级

1. 环境变量（最高优先级）
2. 配置文件中的直接设置
3. 默认配置值

### 3. 配置生效

- 修改配置文件后需要重启程序
- 环境变量修改后需要重新启动终端
- 建议在测试环境验证配置后再应用到生产环境

## 🔧 故障排除

### 1. 常见配置问题

**问题**: API调用失败
**解决**: 检查AppID和AppSecret是否正确

**问题**: 图片上传失败
**解决**: 检查图片大小和格式是否符合要求

**问题**: 发布频率限制
**解决**: 调整频率限制配置或等待冷却时间

### 2. 配置验证命令

```bash
# 验证完整配置
python 微信自动发布器.py --config

# 测试API连接
python -c "from 微信API import 测试微信API; 测试微信API()"

# 测试文章处理
python -c "from 文章处理器 import 测试文章处理器; 测试文章处理器()"
```

### 3. 日志查看

```bash
# 查看主日志
tail -f 日志文件/微信发布.log

# 查看错误日志
tail -f 日志文件/微信发布_errors.log
```

通过以上配置指南，您可以根据实际需求灵活配置微信公众号自动发布器，确保系统安全稳定运行。
