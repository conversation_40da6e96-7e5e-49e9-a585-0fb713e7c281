# 微信公众号自动化系统环境变量配置示例
# 复制此文件为 .env 并填入真实的配置值

# ===========================================
# 微信公众号配置
# ===========================================
# 在微信公众平台获取：https://mp.weixin.qq.com/
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here

# ===========================================
# AI服务配置
# ===========================================
# Google Gemini API配置（推荐）
# 在 Google AI Studio 获取：https://makersuite.google.com/
GEMINI_API_KEY=AIzaSyBsxU4m7X7WWwLehjg-6gv-TfjOx4orNqg

# OpenAI API配置（可选）
# 在 OpenAI Platform 获取：https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# ===========================================
# 数据库配置
# ===========================================
# 如果使用PostgreSQL数据库（可选，默认使用SQLite）
DATABASE_URL=postgresql://username:password@localhost:5432/公众号自动化

# ===========================================
# 安全配置
# ===========================================
# 32位加密密钥，用于敏感数据加密
# 可以使用以下命令生成：python -c "import secrets; print(secrets.token_hex(16))"
ENCRYPTION_KEY=your_32_character_encryption_key_here

# ===========================================
# 通知配置
# ===========================================
# 邮件通知配置（可选）
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password_or_app_password

# Webhook通知配置（可选）
WEBHOOK_URL=https://your-webhook-url.com/notify

# ===========================================
# 系统配置
# ===========================================
# 调试模式（true/false）
DEBUG=false

# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO

# API服务配置
API_HOST=0.0.0.0
API_PORT=5000
