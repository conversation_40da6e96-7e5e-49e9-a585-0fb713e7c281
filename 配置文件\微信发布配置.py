# -*- coding: utf-8 -*-
"""
微信公众号发布配置管理

作者: AI助手
日期: 2025-07-28
功能: 专门管理微信公众号自动发布相关配置
"""

from datetime import datetime, timedelta

# 微信公众号API配置
微信API配置 = {
    'app_id': 'wx21343c9e737b9c08',
    'app_secret': '7a88bb8cdec59b61425dbec8dc2c5797',
    'access_token': '',
    'token_expires_at': 0,
    'api_endpoints': {
        'token': 'https://api.weixin.qq.com/cgi-bin/token',
        'media_upload': 'https://api.weixin.qq.com/cgi-bin/media/upload',  # 临时素材上传
        'material_add': 'https://api.weixin.qq.com/cgi-bin/material/add_material',  # 永久素材上传
        'draft_add': 'https://api.weixin.qq.com/cgi-bin/draft/add',
        'draft_get': 'https://api.weixin.qq.com/cgi-bin/draft/get',
        'draft_delete': 'https://api.weixin.qq.com/cgi-bin/draft/delete',
        'draft_count': 'https://api.weixin.qq.com/cgi-bin/draft/count',
        'publish_submit': 'https://api.weixin.qq.com/cgi-bin/freepublish/submit',
        'publish_get': 'https://api.weixin.qq.com/cgi-bin/freepublish/get',
        'publish_delete': 'https://api.weixin.qq.com/cgi-bin/freepublish/delete'
    }
}

# 发布控制配置
发布控制配置 = {
    '启用自动发布': False,  # 默认关闭，仅上传到草稿箱
    '测试模式': True,  # 测试模式
    '发布前确认': True,  # 需要人工确认
    '草稿箱模式': True,  # 优先使用草稿箱
    '发布策略': {
        '立即发布': False,
        '定时发布': True,
        '默认发布时间': ['09:00', '18:00'],
        '时区': 'Asia/Shanghai',
        '工作日发布': True,
        '周末发布': False
    },
    '频率限制': {
        '每日最大发布数': 3,
        '最小间隔小时': 4,
        '每周最大发布数': 15
    }
}

# 内容处理配置
内容处理配置 = {
    '文本处理': {
        '最大标题长度': 64,
        '最大摘要长度': 120,
        '自动生成摘要': True,
        '移除多余空行': True,
        '统一标点符号': True,
        '自动换行': True,
        '段落间距': 2
    },
    '图片处理': {
        '启用图片上传': True,
        '图片压缩': True,
        '最大图片大小': 2048000,  # 2MB
        '图片质量': 85,
        '支持格式': ['jpg', 'jpeg', 'png', 'gif'],
        '图片水印': False,
        '自动调整尺寸': True,
        '最大宽度': 900
    },
    '链接处理': {
        '保留原文链接': True,
        '转换短链接': False,
        '添加来源说明': True
    }
}

# 排版样式配置
排版样式配置 = {
    '默认样式': 'business',  # business, tech, life, academic
    '样式库': {
        'business': {
            '名称': '商务风格',
            '描述': '简洁专业，适合商业内容',
            '主色调': '#2C3E50',
            '辅助色': '#3498DB',
            '字体配置': {
                '标题字体': 'Microsoft YaHei, sans-serif',
                '正文字体': 'Microsoft YaHei, sans-serif',
                '标题大小': '18px',
                '正文大小': '14px',
                '行高': '1.6'
            }
        },
        'tech': {
            '名称': '科技风格',
            '描述': '现代感强，适合技术文章',
            '主色调': '#1A1A1A',
            '辅助色': '#00D4FF',
            '字体配置': {
                '标题字体': 'PingFang SC, Microsoft YaHei, sans-serif',
                '正文字体': 'PingFang SC, Microsoft YaHei, sans-serif',
                '标题大小': '19px',
                '正文大小': '15px',
                '行高': '1.7'
            }
        },
        'life': {
            '名称': '生活风格',
            '描述': '温馨亲和，适合生活分享',
            '主色调': '#E74C3C',
            '辅助色': '#F39C12',
            '字体配置': {
                '标题字体': 'Microsoft YaHei, sans-serif',
                '正文字体': 'Microsoft YaHei, sans-serif',
                '标题大小': '17px',
                '正文大小': '14px',
                '行高': '1.8'
            }
        },
        'academic': {
            '名称': '学术风格',
            '描述': '严谨规范，适合知识分享',
            '主色调': '#34495E',
            '辅助色': '#9B59B6',
            '字体配置': {
                '标题字体': 'SimSun, serif',
                '正文字体': 'SimSun, serif',
                '标题大小': '16px',
                '正文大小': '14px',
                '行高': '1.9'
            }
        }
    }
}

# 安全和审核配置
安全审核配置 = {
    '内容审核': {
        '启用敏感词检测': True,
        '敏感词库文件': '配置文件/敏感词库.txt',
        '自动替换敏感词': False,
        '审核失败处理': 'skip'  # skip, manual, auto_fix
    },
    '发布安全': {
        'IP白名单': [],
        '访问令牌加密': True,
        '日志记录': True,
        '操作审计': True
    },
    '错误处理': {
        '最大重试次数': 3,
        '重试间隔秒数': [60, 120, 300],  # 递增重试间隔
        '超时设置': {
            '连接超时': 10,
            '读取超时': 30,
            '上传超时': 120
        }
    }
}

# 日志配置
日志配置 = {
    '日志级别': 'INFO',
    '日志文件': '日志文件/微信发布.log',
    '最大文件大小': '10MB',
    '备份文件数': 5,
    '日志格式': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    '记录内容': {
        '发布操作': True,
        'API调用': True,
        '错误信息': True,
        '性能指标': False
    }
}

# 通知配置
通知配置 = {
    '发布成功通知': True,
    '发布失败通知': True,
    '通知方式': ['log', 'console'],  # log, console, email, webhook
    '邮件通知': {
        '启用': False,
        'smtp_server': 'smtp.qq.com',
        'smtp_port': 587,
        '发送邮箱': '',
        '邮箱密码': '',
        '接收邮箱': []
    },
    'webhook通知': {
        '启用': False,
        '通知地址': '',
        '请求方法': 'POST',
        '请求头': {'Content-Type': 'application/json'}
    }
}


def 获取微信API配置():
    """获取微信API配置"""
    return 微信API配置


def 获取发布控制配置():
    """获取发布控制配置"""
    return 发布控制配置


def 获取内容处理配置():
    """获取内容处理配置"""
    return 内容处理配置


def 获取排版样式配置(样式名称=None):
    """获取排版样式配置"""
    if 样式名称:
        return 排版样式配置['样式库'].get(样式名称)
    return 排版样式配置


def 获取安全审核配置():
    """获取安全审核配置"""
    return 安全审核配置


def 获取日志配置():
    """获取日志配置"""
    return 日志配置


def 获取通知配置():
    """获取通知配置"""
    return 通知配置


def 更新发布状态(启用自动发布=None, 测试模式=None):
    """更新发布状态"""
    if 启用自动发布 is not None:
        发布控制配置['启用自动发布'] = 启用自动发布
    if 测试模式 is not None:
        发布控制配置['测试模式'] = 测试模式


def 验证配置():
    """验证配置完整性"""
    errors = []
    
    # 检查必要的API配置
    if not 微信API配置['app_id']:
        errors.append("缺少微信APP_ID配置")
    if not 微信API配置['app_secret']:
        errors.append("缺少微信APP_SECRET配置")
    
    return errors


# 免费配图配置
免费配图配置 = {
    '启用': True,  # 启用免费配图功能
    '默认服务': 'picsum',  # 推荐：picsum（稳定可靠）

    # 免费服务优先级（中国可用）
    '服务优先级': ['picsum', 'local_ai', 'unsplash'],

    # 图片参数
    '图片尺寸': {
        '宽度': 1200,
        '高度': 800
    },
    '最大图片数量': 4,
    '下载超时': 30,

    # 支持的免费服务
    '支持的服务': {
        'picsum': {
            '名称': 'Lorem Picsum',
            '描述': '🎲 随机高质量图片，完全免费',
            '网址': 'https://picsum.photos/',
            '中国可用': True,
            '需要密钥': False,
            '质量': '高'
        },
        'unsplash': {
            '名称': 'Unsplash',
            '描述': '📷 高质量免费图库，支持关键词',
            '网址': 'https://unsplash.com/',
            '中国可用': False,  # 可能需要翻墙
            '需要密钥': False,
            '质量': '最高'
        },
        'local_ai': {
            '名称': '本地配图',
            '描述': '💻 本地生成，离线可用',
            '网址': '',
            '中国可用': True,
            '需要密钥': False,
            '质量': '中等'
        }
    },

    # 关键词映射（中文到英文）
    '关键词映射': {
        '人工智能': 'artificial intelligence',
        '科技': 'technology',
        '商业': 'business',
        '医疗': 'medical',
        '教育': 'education',
        '金融': 'finance',
        '环境': 'environment',
        '旅游': 'travel',
        '美食': 'food',
        '运动': 'sports',
        '音乐': 'music',
        '艺术': 'art',
        '自然': 'nature',
        '城市': 'city',
        '建筑': 'architecture'
    },

    # 主题分类
    '主题分类': {
        '科技': ['technology', 'computer', 'digital', 'innovation'],
        '商业': ['business', 'office', 'meeting', 'corporate'],
        '医疗': ['medical', 'health', 'hospital', 'doctor'],
        '教育': ['education', 'learning', 'school', 'study'],
        '生活': ['lifestyle', 'people', 'family', 'home'],
        '自然': ['nature', 'landscape', 'forest', 'ocean']
    }
}


def 显示配置摘要():
    """显示配置摘要信息"""
    print("🔧 微信公众号发布配置摘要")
    print("=" * 50)
    print(f"📱 API配置状态: {'✅ 已配置' if 微信API配置['app_id'] else '❌ 未配置'}")
    print(f"🚀 自动发布: {'✅ 启用' if 发布控制配置['启用自动发布'] else '❌ 禁用'}")
    print(f"🧪 测试模式: {'✅ 启用' if 发布控制配置['测试模式'] else '❌ 禁用'}")
    print(f"📝 草稿箱模式: {'✅ 启用' if 发布控制配置['草稿箱模式'] else '❌ 禁用'}")
    print(f"🎨 默认排版样式: {排版样式配置['默认样式']}")
    print(f"🔒 内容审核: {'✅ 启用' if 安全审核配置['内容审核']['启用敏感词检测'] else '❌ 禁用'}")
    print(f"📊 日志级别: {日志配置['日志级别']}")


if __name__ == "__main__":
    显示配置摘要()
    
    # 验证配置
    config_errors = 验证配置()
    if config_errors:
        print(f"\n⚠️  配置问题:")
        for error in config_errors:
            print(f"   - {error}")
    else:
        print(f"\n✅ 配置验证通过")
