# -*- coding: utf-8 -*-
"""
智能学习系统配置文件

作者: AI助手
日期: 2025-07-27
功能: 智能学习系统的统一配置管理
"""

import os
from datetime import datetime

# 获取智能学习系统根目录
智能学习系统根目录 = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 目录配置
目录配置 = {
    '根目录': 智能学习系统根目录,
    '核心程序目录': os.path.join(智能学习系统根目录, '核心程序'),
    '知识库目录': os.path.join(智能学习系统根目录, '知识库'),
    '学习日志目录': os.path.join(智能学习系统根目录, '学习日志'),
    '配置文件目录': os.path.join(智能学习系统根目录, '配置文件'),
    '数据存储目录': os.path.join(os.path.dirname(智能学习系统根目录), '数据存储', '原文章')
}

# 学习周期配置
学习周期配置 = {
    '套利发现': 24,    # 24小时运行一次
    '信息获取': 4,     # 4小时运行一次
    '文章采集': 2,     # 2小时运行一次
    '知识整理': 12,    # 12小时运行一次
    '基础学习': 6,     # 6小时运行一次（简化版）
    '信息收集': 3,     # 3小时运行一次（简化版）
    # 测试模式 - 更频繁的学习周期
    '测试基础学习': 0.1,  # 6分钟运行一次（测试用）
    '测试信息收集': 0.05, # 3分钟运行一次（测试用）
    '测试知识整理': 0.2   # 12分钟运行一次（测试用）
}

# 基础关键词池
基础关键词池 = [
    # 核心套利类型
    "套利", "价差套利", "跨市场套利", "可转债套利", "分红套利",
    "期现套利", "统计套利", "配对交易", "ETF套利", "REIT套利",
    
    # 市场相关
    "债券套利", "汇率套利", "利率套利", "商品套利", "股指套利",
    "A股港股套利", "沪深套利", "跨境套利", "货币套利",
    
    # 策略相关
    "无风险套利", "低风险套利", "高频套利", "量化套利",
    "算法套利", "程序化套利", "自动化套利", "智能套利",
    
    # 工具相关
    "套利工具", "套利软件", "套利平台", "套利系统",
    "套利策略", "套利技术", "套利方法", "套利模型"
]

# 数据源配置
数据源配置 = {
    '雪球': {
        '基础URL': 'https://xueqiu.com',
        '搜索URL': 'https://xueqiu.com/k?q=',
        '请求头': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://xueqiu.com'
        },
        '启用': True,
        '优先级': 1
    },
    '微信公众号': {
        '搜索URL': 'https://weixin.sogou.com/weixin?type=2&query=',
        '请求头': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        '启用': True,
        '优先级': 2
    },
    '知乎': {
        '搜索URL': 'https://www.zhihu.com/search?type=content&q=',
        '请求头': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        '启用': False,
        '优先级': 3
    }
}

# AI模型配置
AI模型配置 = {
    'gemini': {
        '模型名称': 'gemini-2.5-flash',
        '环境变量': 'GEMINI_API_KEY',
        '用途': ['套利发现', '信息分析', '知识提取'],
        '启用': True
    }
}

# 文件命名配置
文件命名配置 = {
    '套利知识库': '套利知识库.json',
    '系统状态': '系统状态.json',
    '简化系统状态': '简化系统状态.json',
    '学习报告': '学习报告_{日期}.json',
    '信息收集结果': '信息收集_{关键词}_{时间}.json',
    '自动获取结果': '自动获取_{关键词}_{时间}.json',
    '系统日志': '智能学习系统.log',
    '简化系统日志': '简化全自动系统.log'
}

# 学习参数配置
学习参数配置 = {
    '每次处理关键词数量': 5,
    '每次信息获取数量': 8,
    '关键词池最大大小': 100,
    '学习历史保留天数': 30,
    '请求延迟秒数': 2,
    '超时时间秒数': 30,
    '重试次数': 3,
    '最小文章字数': 500,
    '最大文章字数': 50000
}

# 公众号配置（从原配置文件导入）
公众号配置 = {
    'taotiehai_investment': {
        '名称': '饕餮海投资',
        '描述': '专注跨市场套利、价差套利、可转债套利等投资策略',
        '关键词': ['跨市场套利', '价差套利', '可转债套利', '分红套利', '投资策略'],
        '优先级': 1,
        '是否启用': True,
        '采集频率': '每日',
        '示例链接': [
            'https://mp.weixin.qq.com/s/N2bjVpOHQjbO2cNA0inhAQ'
        ]
    },
    'xinye_lowrisk': {
        '名称': '鑫爷低风险投资',
        '描述': '专注低风险套利、稳健投资策略',
        '关键词': ['低风险套利', '稳健投资', '国债逆回购', '银行理财', '货币基金'],
        '优先级': 1,
        '是否启用': True,
        '采集频率': '每日',
        '示例链接': []
    }
}


def 获取文件路径(文件类型: str, **kwargs) -> str:
    """
    获取指定类型文件的完整路径
    
    参数:
        文件类型: 文件类型名称
        **kwargs: 文件名中的变量参数
        
    返回:
        完整文件路径
    """
    if 文件类型 in 文件命名配置:
        文件名模板 = 文件命名配置[文件类型]
        
        # 替换模板中的变量
        if '{日期}' in 文件名模板:
            文件名模板 = 文件名模板.replace('{日期}', datetime.now().strftime('%Y%m%d'))
        if '{时间}' in 文件名模板:
            文件名模板 = 文件名模板.replace('{时间}', datetime.now().strftime('%Y%m%d_%H%M'))
        if '{关键词}' in 文件名模板 and 'keyword' in kwargs:
            文件名模板 = 文件名模板.replace('{关键词}', kwargs['keyword'])
        
        # 根据文件类型确定目录
        if 文件类型 in ['套利知识库', '系统状态', '简化系统状态']:
            目录 = 目录配置['知识库目录']
        elif 文件类型 in ['学习报告', '信息收集结果', '自动获取结果']:
            目录 = 目录配置['知识库目录']
        elif 文件类型 in ['系统日志', '简化系统日志']:
            目录 = 目录配置['学习日志目录']
        else:
            目录 = 目录配置['知识库目录']
        
        return os.path.join(目录, 文件名模板)
    
    return ""


def 获取配置(配置类型: str):
    """
    获取指定类型的配置
    
    参数:
        配置类型: 配置类型名称
        
    返回:
        配置字典
    """
    配置映射 = {
        '目录': 目录配置,
        '学习周期': 学习周期配置,
        '基础关键词': 基础关键词池,
        '数据源': 数据源配置,
        'AI模型': AI模型配置,
        '文件命名': 文件命名配置,
        '学习参数': 学习参数配置,
        '公众号': 公众号配置
    }
    
    return 配置映射.get(配置类型, {})


def 显示配置信息():
    """显示所有配置信息"""
    print("📋 智能学习系统配置信息")
    print("=" * 60)
    
    print(f"\n📁 目录配置:")
    for 名称, 路径 in 目录配置.items():
        print(f"  {名称}: {路径}")
    
    print(f"\n⏰ 学习周期配置:")
    for 类型, 小时 in 学习周期配置.items():
        print(f"  {类型}: 每 {小时} 小时")
    
    print(f"\n🔑 基础关键词池: {len(基础关键词池)} 个")
    for i, 关键词 in enumerate(基础关键词池[:10], 1):
        print(f"  {i}. {关键词}")
    if len(基础关键词池) > 10:
        print(f"  ... 还有 {len(基础关键词池) - 10} 个")
    
    print(f"\n🌐 数据源配置:")
    for 名称, 配置 in 数据源配置.items():
        状态 = "✅ 启用" if 配置['启用'] else "❌ 禁用"
        print(f"  {名称}: {状态} (优先级: {配置['优先级']})")
    
    print(f"\n🤖 AI模型配置:")
    for 名称, 配置 in AI模型配置.items():
        状态 = "✅ 启用" if 配置['启用'] else "❌ 禁用"
        print(f"  {名称}: {状态} ({配置['模型名称']})")
    
    print(f"\n📊 学习参数:")
    for 参数, 值 in 学习参数配置.items():
        print(f"  {参数}: {值}")


def 创建必要目录():
    """创建所有必要的目录"""
    for 名称, 路径 in 目录配置.items():
        try:
            os.makedirs(路径, exist_ok=True)
            print(f"✅ 目录已准备: {名称}")
        except Exception as e:
            print(f"❌ 创建目录失败 {名称}: {e}")


if __name__ == "__main__":
    # 显示配置信息
    显示配置信息()
    
    print(f"\n📁 创建必要目录:")
    创建必要目录()
    
    print(f"\n📄 示例文件路径:")
    print(f"  套利知识库: {获取文件路径('套利知识库')}")
    print(f"  学习报告: {获取文件路径('学习报告')}")
    print(f"  系统日志: {获取文件路径('系统日志')}")
