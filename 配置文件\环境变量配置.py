# -*- coding: utf-8 -*-
"""
环境变量配置文件

作者: AI助手
日期: 2025-07-27
功能: 统一管理所有环境变量配置
"""

import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# API密钥配置
API_配置 = {
    'GEMINI_API_KEY': {
        '环境变量名': 'GEMINI_API_KEY',
        '描述': 'Google Gemini AI API密钥',
        '必需': True,
        '示例': 'AIzaSyD...',
        '获取方式': 'https://makersuite.google.com/app/apikey'
    }
}

# 代理配置（如果需要）
代理配置 = {
    'HTTP_PROXY': {
        '环境变量名': 'HTTP_PROXY',
        '描述': 'HTTP代理服务器',
        '必需': False,
        '示例': 'http://proxy.example.com:8080'
    },
    'HTTPS_PROXY': {
        '环境变量名': 'HTTPS_PROXY',
        '描述': 'HTTPS代理服务器',
        '必需': False,
        '示例': 'https://proxy.example.com:8080'
    }
}

# 其他配置
其他配置 = {
    'LOG_LEVEL': {
        '环境变量名': 'LOG_LEVEL',
        '描述': '日志级别',
        '必需': False,
        '默认值': 'INFO',
        '可选值': ['DEBUG', 'INFO', 'WARNING', 'ERROR']
    }
}


def 获取环境变量(变量名: str, 默认值: str = None) -> str:
    """
    获取环境变量值
    
    参数:
        变量名: 环境变量名称
        默认值: 如果环境变量不存在时的默认值
        
    返回:
        环境变量值
    """
    return os.getenv(变量名, 默认值)


def 检查必需环境变量() -> dict:
    """
    检查所有必需的环境变量
    
    返回:
        检查结果字典
    """
    检查结果 = {
        '成功': True,
        '缺失变量': [],
        '已设置变量': [],
        '详细信息': {}
    }
    
    # 检查API配置
    for 变量名, 配置 in API_配置.items():
        环境变量名 = 配置['环境变量名']
        值 = 获取环境变量(环境变量名)
        
        if 配置['必需'] and not 值:
            检查结果['成功'] = False
            检查结果['缺失变量'].append(环境变量名)
            检查结果['详细信息'][环境变量名] = {
                '状态': '缺失',
                '描述': 配置['描述'],
                '获取方式': 配置.get('获取方式', ''),
                '示例': 配置.get('示例', '')
            }
        elif 值:
            检查结果['已设置变量'].append(环境变量名)
            # 隐藏敏感信息
            显示值 = f"{值[:10]}...{值[-4:]}" if len(值) > 14 else "已设置"
            检查结果['详细信息'][环境变量名] = {
                '状态': '已设置',
                '值': 显示值,
                '描述': 配置['描述']
            }
    
    return 检查结果


def 显示环境变量状态():
    """显示所有环境变量的状态"""
    print("🔑 环境变量配置状态")
    print("=" * 60)
    
    检查结果 = 检查必需环境变量()
    
    if 检查结果['成功']:
        print("✅ 所有必需环境变量已正确设置")
    else:
        print("❌ 发现缺失的必需环境变量")
    
    print(f"\n📊 环境变量详情:")
    for 变量名, 信息 in 检查结果['详细信息'].items():
        状态图标 = "✅" if 信息['状态'] == '已设置' else "❌"
        print(f"  {状态图标} {变量名}: {信息['状态']}")
        print(f"     描述: {信息['描述']}")
        
        if 信息['状态'] == '已设置':
            print(f"     值: {信息['值']}")
        else:
            print(f"     示例: {信息.get('示例', 'N/A')}")
            if '获取方式' in 信息:
                print(f"     获取方式: {信息['获取方式']}")
        print()
    
    if not 检查结果['成功']:
        print("💡 设置环境变量的方法:")
        print("  Windows: set GEMINI_API_KEY=your_api_key_here")
        print("  Linux/Mac: export GEMINI_API_KEY=your_api_key_here")
        print("  或创建 .env 文件在项目根目录")


def 创建环境变量模板():
    """创建.env文件模板"""
    模板内容 = """# 智能学习系统环境变量配置
# 请复制此文件为 .env 并填入真实的API密钥

# Google Gemini AI API密钥 (必需)
# 获取地址: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# 代理配置 (可选)
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# 日志级别 (可选)
# LOG_LEVEL=INFO
"""
    
    模板文件路径 = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
        '.env.template'
    )
    
    try:
        with open(模板文件路径, 'w', encoding='utf-8') as f:
            f.write(模板内容)
        print(f"✅ 环境变量模板已创建: {模板文件路径}")
        print("💡 请复制为 .env 文件并填入真实的API密钥")
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")


def 获取API密钥(api_名称: str = 'GEMINI') -> str:
    """
    获取指定API的密钥
    
    参数:
        api_名称: API名称
        
    返回:
        API密钥
    """
    if api_名称 == 'GEMINI':
        return 获取环境变量('GEMINI_API_KEY')
    
    return None


def 设置环境变量提示():
    """显示设置环境变量的提示"""
    print("🔧 环境变量设置指南")
    print("=" * 60)
    print("1. 获取Gemini API密钥:")
    print("   访问: https://makersuite.google.com/app/apikey")
    print("   创建新的API密钥")
    print()
    print("2. 设置环境变量:")
    print("   方法1 - 命令行设置:")
    print("   Windows: set GEMINI_API_KEY=your_api_key_here")
    print("   Linux/Mac: export GEMINI_API_KEY=your_api_key_here")
    print()
    print("   方法2 - 创建.env文件:")
    print("   在项目根目录创建 .env 文件")
    print("   内容: GEMINI_API_KEY=your_api_key_here")
    print()
    print("3. 重新启动系统验证")


if __name__ == "__main__":
    # 显示环境变量状态
    显示环境变量状态()
    
    # 如果有缺失的环境变量，显示设置指南
    检查结果 = 检查必需环境变量()
    if not 检查结果['成功']:
        print()
        设置环境变量提示()
        
        # 询问是否创建模板
        创建模板 = input("\n是否创建.env模板文件? (y/n): ").strip().lower()
        if 创建模板 in ['y', 'yes', '是']:
            创建环境变量模板()
