# 微信公众号自动化系统配置文件

# 数据库配置
数据库:
  类型: "sqlite"  # sqlite, postgresql, mysql
  sqlite配置:
    文件路径: "数据存储/公众号自动化.db"
  postgresql配置:
    主机: "localhost"
    端口: 5432
    数据库名: "公众号自动化"
    用户名: "your_username"
    密码: "your_password"

# 微信公众号配置
微信配置:
  # 目标发布公众号（你的公众号）
  目标账号:
    应用ID: "your_app_id"
    应用密钥: "your_app_secret"
    访问令牌: ""
    
  # 源公众号列表（要监控的公众号）
  源账号列表:
    - 名称: "饕餮海投资"
      账号ID: "taotiehai_investment"
      关键词: ["投资", "理财", "金融", "股票", "基金"]
      优先级: 1
    - 名称: "鑫爷低风险投资"
      账号ID: "xinye_lowrisk"
      关键词: ["低风险", "投资", "理财", "稳健", "收益"]
      优先级: 2

# 内容收集配置
内容收集:
  # 抓取频率（分钟）
  抓取间隔: 60
  # 每次抓取的文章数量（设置为1，只抓取最新一篇）
  每次抓取数量: 1
  # 内容过滤规则
  过滤规则:
    最小字数: 100  # 降低最小字数要求，适应模拟数据
    最大字数: 10000
    排除关键词: ["广告", "推广", "营销"]
    
# 内容分析配置
内容分析:
  # AI模型配置
  AI模型:
    服务商: "gemini"  # gemini, openai, local
    模型名称: "gemini-2.5-flash"  # 使用Gemini 2.5 Flash
    API密钥: "your_gemini_api_key"
    最大令牌数: 8000  # Gemini 2.5 Pro支持更大的上下文
    温度参数: 0.7
    
  # 内容质量评分阈值
  质量阈值: 0.7
  
  # 关键词提取配置
  关键词提取:
    最大关键词数: 10
    最小关键词频率: 2

# 内容整合配置
内容整合:
  # 原创性改写配置
  内容改写:
    相似度阈值: 0.3  # 与原文相似度阈值
    风格模板:
      - "深度分析"
      - "行业观察"
      - "趋势解读"
      
  # 文章生成配置
  文章生成:
    最小长度: 800
    最大长度: 2000
    包含图片: true
    
# 自动发布配置
自动发布:
  # 发布时间配置
  发布计划:
    启用: true
    发布时间:
      - "09:00"
      - "18:00"
    时区: "Asia/Shanghai"
    
  # 发布前审核
  内容审核:
    启用: true
    自动通过阈值: 0.8
    
  # 发布频率限制
  频率限制:
    每日最大发布数: 3
    最小间隔小时: 4

# 系统管理配置
系统管理:
  # 日志配置
  日志设置:
    日志级别: "INFO"  # DEBUG, INFO, WARNING, ERROR
    日志文件: "日志文件/系统日志.log"
    最大文件大小: "10MB"
    备份文件数: 5
    
  # 错误处理
  错误处理:
    最大重试次数: 3
    重试延迟秒数: 60
    
  # 数据备份
  数据备份:
    启用: true
    备份间隔小时: 24
    备份路径: "数据存储/备份文件"
    
# API服务配置
API服务:
  主机: "0.0.0.0"
  端口: 5000
  调试模式: false
  
# 安全配置
安全设置:
  加密密钥: "your_encryption_key"
  API访问限制: 100  # 每分钟请求数
  
# 通知配置
通知设置:
  邮件通知:
    启用: false
    SMTP服务器: "smtp.gmail.com"
    SMTP端口: 587
    用户名: "<EMAIL>"
    密码: "your_email_password"
    收件人: ["<EMAIL>"]
    
  Webhook通知:
    启用: false
    网址: "https://your-webhook-url.com"
